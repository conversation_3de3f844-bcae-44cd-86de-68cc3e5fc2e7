package org.dromara.system.mq.consumer;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.mq.queue.RabbitQueue;
import org.dromara.system.service.ISysConfigService;
import org.dromara.system.service.ISysUserService;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
/**
 * 用户消息消费者
 * 用于接收和处理用户相关的MQ消息
 */
@Slf4j
//@Component
public class UserConsumer {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 水务平台AES key
     */
    private static final String AES_KEY = "istarcloudaeskey";

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISysUserService userService;

    /**
     * 监听新增用户队列
     *
     * @param message 消息内容
     */
    @RabbitListener(queues = RabbitQueue.QUEUE_ADD_USER)
    public void receiveAddUserMessage(Message message) {
        try {
            String messageBody = new String(message.getBody());
            log.info("接收到新增用户消息: {}", messageBody);

            // 解析消息内容
            JsonNode jsonNode = objectMapper.readTree(messageBody);

            // 提取信息
            String externalPlatformId = null;
            if (jsonNode.has("id") && !jsonNode.get("id").isNull()) {
                JsonNode idNode = jsonNode.get("id");
                if (idNode.has("id")) {
                    externalPlatformId = idNode.get("id").asText();
                }
            }

            // 提取tenantId信息
//            String tenantId = null;
//            if (jsonNode.has("tenantId") && !jsonNode.get("tenantId").isNull()) {
//                JsonNode tenantIdNode = jsonNode.get("tenantId");
//                if (tenantIdNode.has("id")) {
//                    tenantId = tenantIdNode.get("id").asText();
//                }
//            }

            // 提取其他用户信息
            String email = jsonNode.has("email") ? jsonNode.get("email").asText() : null;
            String firstName = jsonNode.has("firstName") ? jsonNode.get("firstName").asText() : null;
            String name = jsonNode.has("name") ? jsonNode.get("name").asText() : null;
            String phone = jsonNode.has("phone") ? jsonNode.get("phone").asText() : null;
            String password = jsonNode.has("password") ? jsonNode.get("password").asText() : null;

            if(StringUtils.isBlank(firstName)) {
                log.error("用户消息 - externalPlatformId: {}, firstName（账号）不能为空", externalPlatformId);
                return;
            }
            if(StringUtils.isBlank(password)) {
                log.error("用户消息 - externalPlatformId: {}, 密码不能为空", externalPlatformId);
                return;
            }

            log.info("解析用户消息 - externalPlatformId: {}, name: {}, email: {}, phone: {}",
                externalPlatformId, name, email, phone);

            SysUserVo sysUser = userService.selectUserByUserName(firstName);
            if(sysUser != null) {
                log.error("用户消息 - externalPlatformId: {}, 用户名与平台内重复", externalPlatformId);
                return;
            }
            // TODO: 根据业务需求处理用户数据，例如保存到数据库或调用其他服务
            SysUserBo sysUserBo = new SysUserBo();
            sysUserBo.setExternalPlatformId(externalPlatformId);
            sysUserBo.setUserName(name);
            sysUserBo.setNickName(firstName);
            sysUserBo.setPhonenumber(phone);
            sysUserBo.setEmail(email);
            sysUserBo.setPassword(password);
            if(StringUtils.isNotBlank(configService.selectConfigByKey("sys.user.synchronization.initDept.id"))) {
                sysUserBo.setDeptId(Long.valueOf(configService.selectConfigByKey("sys.user.synchronization.initDept.id")));
            }
            if(StringUtils.isNotBlank(configService.selectConfigByKey("sys.user.synchronization.initRole.id"))) {
                Long[] roleIds = {Long.valueOf(configService.selectConfigByKey("sys.user.synchronization.initRole.id"))};
                sysUserBo.setRoleIds(roleIds);
            }
            sysUserBo.setUserType("water_platform_user");
            //忽略租户配置
            TenantHelper.ignore(() -> { userService.MQInsertUser(sysUserBo); });
        } catch (Exception e) {
            log.error("处理新增用户消息失败", e);
        }
    }

    /**
     * 监听更新用户队列
     *
     * @param message 消息内容
     */
    @RabbitListener(queues = RabbitQueue.QUEUE_UPDATE_USER)
    public void receiveUpdateUserMessage(Message message) {
        try {
            String messageBody = new String(message.getBody());
            log.info("接收到更新用户消息: {}", messageBody);

            // 解析消息内容
            JsonNode jsonNode = objectMapper.readTree(messageBody);

            // 提取信息
            String externalPlatformId = null;
            if (jsonNode.has("id") && !jsonNode.get("id").isNull()) {
                JsonNode idNode = jsonNode.get("id");
                if (idNode.has("id")) {
                    externalPlatformId = idNode.get("id").asText();
                }
            }

            // 提取tenantId信息
//            String tenantId = null;
//            if (jsonNode.has("tenantId") && !jsonNode.get("tenantId").isNull()) {
//                JsonNode tenantIdNode = jsonNode.get("tenantId");
//                if (tenantIdNode.has("id")) {
//                    tenantId = tenantIdNode.get("id").asText();
//                }
//            }

            // 提取其他用户信息
            String email = jsonNode.has("email") ? jsonNode.get("email").asText() : null;
            String firstName = jsonNode.has("firstName") ? jsonNode.get("firstName").asText() : null;
            String name = jsonNode.has("name") ? jsonNode.get("name").asText() : null;
            String phone = jsonNode.has("phone") ? jsonNode.get("phone").asText() : null;
            String password = jsonNode.has("password") ? jsonNode.get("password").asText() : null;

            if(StringUtils.isBlank(firstName)) {
                log.error("用户更新消息 - externalPlatformId: {}, firstName（账号）不能为空", externalPlatformId);
                return;
            }

            log.info("解析用户更新消息 - externalPlatformId: {}, name: {}, email: {}, phone: {}",
                externalPlatformId, name, email, phone);

            // 根据externalPlatformId查询用户
            SysUserVo existingUser = userService.selectUserByExternalPlatformIdAndUserType(externalPlatformId, "water_platform_user");
            if(existingUser == null) {
                log.error("用户更新消息 - externalPlatformId: {}, 用户不存在", externalPlatformId);
                return;
            }

            // 更新用户信息
            SysUserBo sysUserBo = new SysUserBo();
            sysUserBo.setUserId(existingUser.getUserId());
            sysUserBo.setUserName(name);
            sysUserBo.setNickName(firstName);
            sysUserBo.setPhonenumber(phone);
            sysUserBo.setEmail(email);

            // 如果提供了新密码，则更新密码
            if(StringUtils.isNotBlank(password)) {
                sysUserBo.setPassword(password);
            }

            // 保留原有角色和部门信息
            sysUserBo.setRoleIds(existingUser.getRoleIds());
            sysUserBo.setDeptId(existingUser.getDeptId());


            //忽略租户配置
            TenantHelper.ignore(() -> { userService.MQUpdateUser(sysUserBo); });
            log.info("用户信息更新成功 - externalPlatformId: {}", externalPlatformId);
        } catch (Exception e) {
            log.error("处理更新用户消息失败", e);
        }
    }

    /**
     * 监听删除用户队列
     *
     * @param message 消息内容
     */
    @RabbitListener(queues = RabbitQueue.QUEUE_DELETE_USER)
    public void receiveDeleteUserMessage(Message message) {
        try {
            String messageBody = new String(message.getBody());
            log.info("接收到删除用户消息: {}", messageBody);

            // 解析消息内容
            JsonNode jsonNode = objectMapper.readTree(messageBody);

            // 提取信息
            String externalPlatformId = null;
            if (jsonNode.has("id")) {
                externalPlatformId = jsonNode.get("id").asText();
            }else {
                log.error("用户删除消息 - externalPlatformId不能为空");
            }

            // 提取tenantId信息
//            String tenantId = null;
//            if (jsonNode.has("tenantId") && !jsonNode.get("tenantId").isNull()) {
//                JsonNode tenantIdNode = jsonNode.get("tenantId");
//                if (tenantIdNode.has("id")) {
//                    tenantId = tenantIdNode.get("id").asText();
//                }
//            }

            log.info("解析用户删除消息 - externalPlatformId: {}", externalPlatformId);

            if(StringUtils.isBlank(externalPlatformId)) {
                log.error("用户删除消息 - externalPlatformId不能为空");
                return;
            }

            // 根据externalPlatformId查询用户
            SysUserVo existingUser = userService.selectUserByExternalPlatformIdAndUserType(externalPlatformId, "water_platform_user");
            if(existingUser == null) {
                log.error("用户删除消息 - externalPlatformId: {}, 用户不存在", externalPlatformId);
                return;
            }

            //忽略租户配置
            TenantHelper.ignore(() -> { userService.deleteUserById(existingUser.getUserId()); });
            log.info("用户删除成功 - externalPlatformId: {}, userId: {}", externalPlatformId, existingUser.getUserId());
        } catch (Exception e) {
            log.error("处理删除用户消息失败", e);
        }
    }
}
