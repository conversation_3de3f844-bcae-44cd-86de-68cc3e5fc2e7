package org.dromara.workflow.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.dromara.common.core.enums.BusinessStatusEnum;
import org.dromara.common.core.enums.FlowNodeEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.warm.flow.core.dto.FlowParams;
import org.dromara.warm.flow.core.entity.Definition;
import org.dromara.warm.flow.core.entity.Instance;
import org.dromara.warm.flow.core.enums.SkipType;
import org.dromara.warm.flow.core.service.*;
import org.dromara.warm.flow.orm.entity.FlowTask;
import org.dromara.warm.flow.orm.mapper.FlowHisTaskMapper;
import org.dromara.warm.flow.orm.mapper.FlowInstanceMapper;
import org.dromara.warm.flow.orm.mapper.FlowNodeMapper;
import org.dromara.warm.flow.orm.mapper.FlowTaskMapper;
import org.dromara.waterfee.api.RemoteWaterfeeUserService;
import org.dromara.waterfee.api.domain.WaterfeeMeterDTO;
import org.dromara.waterfee.api.domain.WaterfeeUserDTO;
import org.dromara.workflow.common.enums.TaskStatusEnum;
import org.dromara.workflow.domain.bo.CompleteTaskBo;
import org.dromara.workflow.domain.bo.CompleteWaterfeeTaskBo;
import org.dromara.workflow.domain.bo.FlowCopyBo;
import org.dromara.workflow.handler.FlowProcessEventHandler;
import org.dromara.workflow.mapper.FlwCategoryMapper;
import org.dromara.workflow.mapper.FlwTaskMapper;
import org.dromara.workflow.service.IFlwCommonService;
import org.dromara.workflow.service.IFlwTaskAssigneeService;
import org.dromara.workflow.service.IFlwTaskService;
import org.dromara.workflow.service.IFlwWaterfeeUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Service
public class FlwWaterfeeUserServiceImpl implements IFlwWaterfeeUserService {

    private final TaskService taskService;
    private final InsService insService;
    private final DefService defService;
    private final HisTaskService hisTaskService;
    private final NodeService nodeService;
    private final FlowInstanceMapper flowInstanceMapper;
    private final FlowTaskMapper flowTaskMapper;
    private final FlowHisTaskMapper flowHisTaskMapper;
    private final IdentifierGenerator identifierGenerator;
    private final FlowProcessEventHandler flowProcessEventHandler;
    private final FlwTaskMapper flwTaskMapper;
    private final FlwCategoryMapper flwCategoryMapper;
    private final FlowNodeMapper flowNodeMapper;
    private final IFlwTaskAssigneeService flwTaskAssigneeService;
    private final IFlwCommonService flwCommonService;

    @Autowired
    private IFlwTaskService flwTaskService;

    @Autowired
    private FlwTaskServiceImpl flwTaskServiceImpl;

    @DubboReference
    private RemoteWaterfeeUserService remoteWaterfeeUserService;

    /**
     * 办理任务
     *
     * @param completeWaterfeeTaskBo 办理任务参数
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean completeWaterUserTask(CompleteWaterfeeTaskBo completeWaterfeeTaskBo) {
        CompleteTaskBo completeTaskBo = new CompleteTaskBo();
        BeanUtils.copyProperties(completeWaterfeeTaskBo, completeTaskBo);
        try {
            // 获取任务ID并查询对应的流程任务和实例信息
            Long taskId = completeTaskBo.getTaskId();
            List<String> messageType = completeTaskBo.getMessageType();
            String notice = completeTaskBo.getNotice();
            // 获取抄送人
            List<FlowCopyBo> flowCopyList = completeTaskBo.getFlowCopyList();
            FlowTask flowTask = flowTaskMapper.selectById(taskId);
            if (ObjectUtil.isNull(flowTask)) {
                throw new ServiceException("流程任务不存在或任务已审批！");
            }
            Instance ins = insService.getById(flowTask.getInstanceId());
            // 获取流程定义信息
            Definition definition = defService.getById(flowTask.getDefinitionId());

            /**  开始 根据节点代码执行不同的业务逻辑    **/
            // 根据节点代码执行不同的业务逻辑
            if (FlowNodeEnum.WATER_METER_INSTALLATION.getCode().equals(flowTask.getNodeCode())) {
                // 水表安装节点
                if (StringUtils.isNotBlank(completeWaterfeeTaskBo.getMeterNo())) {
                    // 调用远程服务绑定水表
                    Boolean bindResult = remoteWaterfeeUserService.bindUserMeter(Long.valueOf(ins.getBusinessId()), completeWaterfeeTaskBo.getMeterNo(), "workflow");
                    if (!bindResult) {
                        throw new ServiceException("绑定水表信息失败");
                    }
                } else {
                    throw new ServiceException("水表信息不能为空");
                }
            } else if (FlowNodeEnum.WATER_PRICE_SETTING.getCode().equals(flowTask.getNodeCode())) {
                // 水价设置节点
                if (completeWaterfeeTaskBo.getWaterfeeUserDTO() != null) {
                    WaterfeeUserDTO userDTO = completeWaterfeeTaskBo.getWaterfeeUserDTO();
                    // 参数校验 - 参考WaterfeeUserBo中的价格相关NotBlank注解
                    validateWaterPriceParams(userDTO);

                    userDTO.setUserId(Long.valueOf(ins.getBusinessId()));
                    // 调用远程服务更新价格信息
                    Boolean priceResult = remoteWaterfeeUserService.updateUserPriceInfo(userDTO, "workflow");
                    if (!priceResult) {
                        throw new ServiceException("设定用户水价信息失败");
                    }
                } else {
                    throw new ServiceException("用户水价信息不能为空");
                }
            } else if (FlowNodeEnum.BASIC_INFORMATION_INPUT.getCode().equals(flowTask.getNodeCode())) {
                // 用户信息录入节点，已经单独调用接口，这里不做特殊处理
            }
            /**  业务逻辑完成   **/

            // 检查流程状态是否为草稿、已撤销或已退回状态，若是则执行流程提交监听
            if (BusinessStatusEnum.isDraftOrCancelOrBack(ins.getFlowStatus())) {
                flowProcessEventHandler.processHandler(definition.getFlowCode(), ins.getBusinessId(), ins.getFlowStatus(), null, true);
            }
            // 设置弹窗处理人
            Map<String, Object> assigneeMap = flwTaskServiceImpl.setPopAssigneeMap(completeTaskBo.getAssigneeMap(), ins.getVariableMap());
            if (CollUtil.isNotEmpty(assigneeMap)) {
                completeTaskBo.getVariables().putAll(assigneeMap);
            }
            // 构建流程参数，包括变量、跳转类型、消息、处理人、权限等信息
            FlowParams flowParams = new FlowParams();
            flowParams.variable(completeTaskBo.getVariables());
            flowParams.skipType(SkipType.PASS.getKey());
            flowParams.message(completeTaskBo.getMessage());
            flowParams.flowStatus(BusinessStatusEnum.WAITING.getStatus()).hisStatus(TaskStatusEnum.PASS.getStatus());
            //添加自定义任务状态
            if(FlowNodeEnum.BASIC_INFORMATION_INPUT.getCode().equals(ins.getNodeCode())) {
                flowParams.flowStatus(BusinessStatusEnum.WAITING_INPUT_INFO.getStatus()).hisStatus(TaskStatusEnum.PASS.getStatus());
            }else if(FlowNodeEnum.WATER_METER_INSTALLATION.getCode().equals(ins.getNodeCode())) {
                flowParams.flowStatus(BusinessStatusEnum.WAITING_INSTALL_METER.getStatus()).hisStatus(TaskStatusEnum.PASS.getStatus());
            }else if(FlowNodeEnum.WATER_PRICE_SETTING.getCode().equals(ins.getNodeCode())) {
                flowParams.flowStatus(BusinessStatusEnum.WAITING_SET_PRICE.getStatus()).hisStatus(TaskStatusEnum.PASS.getStatus());
            }else {
                flowParams.flowStatus(BusinessStatusEnum.WAITING.getStatus()).hisStatus(TaskStatusEnum.PASS.getStatus());
            }

            flowParams.hisTaskExt(completeTaskBo.getFileId());
            // 执行任务跳转，并根据返回的处理人设置下一步处理人
            Instance instance = taskService.skip(taskId, flowParams);
            flwTaskServiceImpl.setHandler(instance, flowTask, flowCopyList);
            // 消息通知
            flwCommonService.sendMessage(definition.getFlowName(), ins.getId(), messageType, notice);
            //设置下一环节处理人
            flwTaskServiceImpl.setNextHandler(ins.getId());
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 校验水价参数
     * 参考WaterfeeUserBo中的价格相关NotBlank注解
     *
     * @param userDTO 用户DTO
     */
    private void validateWaterPriceParams(WaterfeeUserDTO userDTO) {
        // 必填字段校验
        if (StringUtils.isBlank(userDTO.getPriceUseWaterNature())) {
            throw new ServiceException("价格-用水性质不能为空");
        }

        if (StringUtils.isBlank(userDTO.getBillingMethod())) {
            throw new ServiceException("计费方式不能为空");
        }

        // 条件性必填字段校验
        if ("1".equals(userDTO.getIfPenalty()) && StringUtils.isBlank(userDTO.getPenaltyType())) {
            throw new ServiceException("启用违约金时，违约金类型不能为空");
        }

        if ("1".equals(userDTO.getIfExtraCharge()) && StringUtils.isBlank(userDTO.getExtraChargeType())) {
            throw new ServiceException("启用附加费时，附加费内容不能为空");
        }

        // 用户ID校验
//        if (userDTO.getUserId() == null) {
//            throw new ServiceException("用户ID不能为空");
//        }
    }
}
