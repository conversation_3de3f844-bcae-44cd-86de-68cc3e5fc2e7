package org.dromara.workflow.listener;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.enums.BusinessStatusEnum;
import org.dromara.common.core.enums.FlowNodeEnum;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.waterfee.api.RemoteWaterfeeUserService;
import org.dromara.waterfee.api.domain.WaterfeeMeterDTO;
import org.dromara.waterfee.api.domain.WaterfeeUserDTO;
import org.dromara.workflow.api.event.ProcessCreateTaskEvent;
import org.dromara.workflow.api.event.ProcessDeleteEvent;
import org.dromara.workflow.api.event.ProcessEvent;
import org.dromara.workflow.common.ConditionalOnEnable;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 用水用户流程事件监听器
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@ConditionalOnEnable
@RequiredArgsConstructor
@Component
@Slf4j
public class WaterfeeUseFlowListener {

    @DubboReference
    private RemoteWaterfeeUserService remoteWaterfeeUserService;

    /**
     * 总体流程监听(例如: 草稿，撤销，退回，作废，终止，已完成，单任务完成等)
     * 监听用水用户相关的流程
     *
     * @param processEvent 流程事件
     */
    @EventListener(condition = "#processEvent.flowCode.startsWith('newWaterfeeUser')")
    public void processHandler(ProcessEvent processEvent) {
        TenantHelper.dynamic(processEvent.getTenantId(), () -> {
            log.info("用水用户流程事件: {}", processEvent.toString());

            // 获取业务ID并转换为Long类型
            Long userId = Long.valueOf(processEvent.getBusinessId());

            // 通过远程服务获取用户信息
            WaterfeeUserDTO userDTO = remoteWaterfeeUserService.getWaterfeeUserById(userId, "workflow");
            if (userDTO == null) {
                log.error("用户ID为{}的用水用户不存在", userId);
                return;
            }

            // 更新用户状态
            userDTO.setAuditStatus(processEvent.getStatus());

            // 处理流程参数
            Map<String, Object> params = processEvent.getParams();
            if (MapUtil.isNotEmpty(params)) {
                // 历史任务扩展(通常为附件)
                String hisTaskExt = Convert.toStr(params.get("hisTaskExt"));
                // 办理人
                String handler = Convert.toStr(params.get("handler"));
                // 办理意见
                String message = Convert.toStr(params.get("message"));
            }

            // 如果是提交操作，设置为待审核状态
            if (processEvent.isSubmit()) {
                userDTO.setAuditStatus(BusinessStatusEnum.WAITING.getStatus()); // 待审核
            }

            // 调用远程服务更新用户信息
            Boolean result = remoteWaterfeeUserService.updateWaterfeeUser(userDTO, "workflow");
            if (!result) {
                log.error("更新用户ID为{}的用水用户状态失败", userId);
            }
        });
    }

    /**
     * 执行任务创建监听
     *
     * @param processCreateTaskEvent 任务创建事件
     */
    @EventListener(condition = "#processCreateTaskEvent.flowCode.startsWith('newWaterfeeUser')")
    public void processCreateTaskHandler(ProcessCreateTaskEvent processCreateTaskEvent) {
        TenantHelper.dynamic(processCreateTaskEvent.getTenantId(), () -> {
            log.info("用水用户任务创建事件: {}", processCreateTaskEvent.toString());

            // 获取业务ID并转换为Long类型
            Long userId = Long.valueOf(processCreateTaskEvent.getBusinessId());

            // 通过远程服务获取用户信息
            WaterfeeUserDTO userDTO = remoteWaterfeeUserService.getWaterfeeUserById(userId, "workflow");
            if (userDTO == null) {
                log.error("用户ID为{}的用水用户不存在", userId);
                return;
            }

            // 根据节点代码处理不同的业务逻辑
            String nodeCode = processCreateTaskEvent.getNodeCode();
            if(nodeCode.equals(FlowNodeEnum.BASIC_INFORMATION_INPUT.getCode())) {
                userDTO.setAuditStatus(BusinessStatusEnum.DRAFT.getStatus());
            }else if(nodeCode.equals(FlowNodeEnum.WATER_METER_INSTALLATION.getCode())) {
                userDTO.setAuditStatus(BusinessStatusEnum.WAITING_INSTALL_METER.getStatus());
            }else if(nodeCode.equals(FlowNodeEnum.WATER_PRICE_SETTING.getCode())) {
                userDTO.setAuditStatus(BusinessStatusEnum.WAITING_SET_PRICE.getStatus());
            }else {
                userDTO.setAuditStatus(BusinessStatusEnum.WAITING.getStatus());
            }

            // 调用远程服务更新用户信息
            Boolean result = remoteWaterfeeUserService.updateWaterfeeUser(userDTO, "workflow");
            if (!result) {
                log.error("更新用户ID为{}的用水用户状态失败", userId);
            }
        });
    }

    /**
     * 监听删除流程事件
     *
     * @param processDeleteEvent 流程删除事件
     */
    @EventListener(condition = "#processDeleteEvent.flowCode.startsWith('newWaterfeeUser')")
    public void processDeleteHandler(ProcessDeleteEvent processDeleteEvent) {
        TenantHelper.dynamic(processDeleteEvent.getTenantId(), () -> {
            log.info("用水用户流程删除事件: {}", processDeleteEvent.toString());

            // 获取业务ID并转换为Long类型
            Long userId = Long.valueOf(processDeleteEvent.getBusinessId());

            // 通过远程服务获取用户信息
            WaterfeeUserDTO userDTO = remoteWaterfeeUserService.getWaterfeeUserById(userId, "workflow");
            if (userDTO == null) {
                log.info("用户ID为{}的用水用户不存在，可能已被删除", userId);
                return;
            }

            // 根据业务需求决定是否删除用户或者更改用户状态
            // 这里假设我们不直接删除用户，而是将其状态设置为草稿状态
//            userDTO.setAuditStatus(BusinessStatusEnum.INVALID.getStatus()); // 设置为已作废状态
//            userDTO.setUserStatus("normal"); // 重置为正常状态

            // 调用远程服务更新用户信息
//            Boolean result = remoteWaterfeeUserService.updateWaterfeeUser(userDTO, "workflow");
//            if (!result) {
//                log.error("更新用户ID为{}的用水用户状态失败", userId);
//            }

            // 如果确实需要删除用户，可以使用以下代码
             Boolean deleteResult = remoteWaterfeeUserService.deleteWaterfeeUser(userId, "workflow");
             if (!deleteResult) {
                 log.error("删除用户ID为{}的用水用户失败", userId);
             }
        });
    }
}
