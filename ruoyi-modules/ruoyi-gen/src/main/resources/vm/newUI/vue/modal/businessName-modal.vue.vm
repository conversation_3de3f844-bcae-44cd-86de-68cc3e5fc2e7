<script setup lang="ts">
import type { RuleObject } from 'ant-design-vue/es/form';
import type { ${BusinessName}Form } from '#/api/${moduleName}/${businessName}/model.d';

import { computed, ref } from 'vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { Form, FormItem } from 'ant-design-vue';
import { pick } from 'lodash-es';
import { getDictOptions } from '#/utils/dict';
import { modalSchema } from './data';

import { add${BusinessName}, get${BusinessName}, update${BusinessName} } from '#/api/${moduleName}/${businessName}';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    labelWidth: 80,
  },
  schema: modalSchema(),
  showDefaultActions: false,
});

const [BasicModal, modalApi] = useVbenModal({
  class: 'w-[800px]',
  fullscreenButton: true,
  closeOnClickModal: false,
  onClosed: handleCancel,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    modalApi.modalLoading(true);
    const { id } = modalApi.getData() as { id?: number | string };
    isUpdate.value = !!id;
    if (isUpdate.value && id) {
      const record = await get${BusinessName}(id);
      await formApi.setValues(record);
    }
    modalApi.modalLoading(false);
  },
});

async function handleConfirm() {
  try {
    modalApi.modalLoading(true);
    const { valid } = await formApi.validate();
        if (!valid) {
          return;
        }
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(await formApi.getValues());
    #foreach ($column in $columns)
    #if($column.htmlType == "checkbox")
    data.$column.javaField = data.${column.javaField}.join(",");
    #end
    #end
    await (isUpdate.value ? update${BusinessName}(data) : add${BusinessName}(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    modalApi.modalLoading(false);
  }
}

async function handleCancel() {
  modalApi.close();
  await formApi.resetForm();
}
</script>

<template>
  <BasicModal :close-on-click-modal="false" :title="title">
      <BasicForm />
    </BasicModal>
</template>
