package org.dromara.waterfee.invoice.enums;

public enum InvoiceGoodsEnum {

    LEVEL_ONE("阶梯一水费"),
    LEVEL_TWO("阶梯二水费"),
    LEVEL_THREE("阶梯三水费"),
    QUOTA_ONE("额定单价水费"),
    QUOTA_TWO("超定单价1水费"),
    QUOTA_THREE("超定单价2水费"),
    SEWAGE("污水处理费"),
    NO_TAX("不征增值税自来水");

    private final String goodsName;

    InvoiceGoodsEnum(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsName() {
        return goodsName;
    }
}
