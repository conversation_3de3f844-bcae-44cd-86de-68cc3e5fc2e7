package org.dromara.waterfee.meterReading.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.meterReading.domain.bo.WaterfeeMeterReadingManualBo;
import org.dromara.waterfee.meterReading.domain.vo.WaterfeeMeterReadingManualVo;
import org.dromara.waterfee.meterReading.service.IMeterReadingRecordService;
import org.dromara.waterfee.meterReading.service.IWaterfeeMeterReadingManualService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 抄表补录
 * 前端访问路由地址为:/waterfee/meterReadingManual
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/meterReadingManual")
public class WaterfeeMeterReadingManualController extends BaseController {

    private final IWaterfeeMeterReadingManualService waterfeeMeterReadingManualService;
    private final IMeterReadingRecordService meterReadingRecordService;

    /**
     * 查询抄表补录列表
     */
    @SaCheckPermission("waterfee:meterReadingManual:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeMeterReadingManualVo> list(WaterfeeMeterReadingManualBo bo, PageQuery pageQuery) {
        return waterfeeMeterReadingManualService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出抄表补录列表
     */
    @SaCheckPermission("waterfee:meterReadingManual:export")
    @Log(title = "抄表补录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeMeterReadingManualBo bo, HttpServletResponse response) {
        List<WaterfeeMeterReadingManualVo> list = waterfeeMeterReadingManualService.queryList(bo);
        ExcelUtil.exportExcel(list, "抄表补录", WaterfeeMeterReadingManualVo.class, response);
    }

    /**
     * 获取抄表补录详细信息
     *
     * @param manualId 主键
     */
    @SaCheckPermission("waterfee:meterReadingManual:query")
    @GetMapping("/{manualId}")
    public R<WaterfeeMeterReadingManualVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long manualId) {
        return R.ok(waterfeeMeterReadingManualService.queryById(manualId));
    }

    /**
     * 新增抄表补录
     */
    @SaCheckPermission("waterfee:meterReadingManual:add")
    @Log(title = "抄表补录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated @RequestBody WaterfeeMeterReadingManualBo bo) {
        return toAjax(waterfeeMeterReadingManualService.insertByBo(bo));
    }

    /**
     * 修改抄表补录
     */
    @SaCheckPermission("waterfee:meterReadingManual:edit")
    @Log(title = "抄表补录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated @RequestBody WaterfeeMeterReadingManualBo bo) {
        return toAjax(waterfeeMeterReadingManualService.updateByBo(bo));
    }

    /**
     * 删除抄表补录
     *
     * @param manualIds 主键串
     */
    @SaCheckPermission("waterfee:meterReadingManual:remove")
    @Log(title = "抄表补录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{manualIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] manualIds) {
        return toAjax(waterfeeMeterReadingManualService.deleteWithValidByIds(List.of(manualIds), true));
    }

    /**
     * 查询水表信息
     *
     * @param meterNo 水表编号
     */
    @SaCheckPermission("waterfee:meterReadingManual:query")
    @GetMapping("/meterInfo/{meterNo}")
    public R<Map<String, Object>> getMeterInfo(@NotBlank(message = "水表编号不能为空") @PathVariable String meterNo) {
        return R.ok(meterReadingRecordService.queryMeterInfo(meterNo));
    }
}
