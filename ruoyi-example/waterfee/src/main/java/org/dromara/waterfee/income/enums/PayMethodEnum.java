package org.dromara.waterfee.income.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付方式枚举
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@Getter
@AllArgsConstructor
public enum PayMethodEnum {

    /**
     * 微信支付
     */
    WECHAT("wechat", "微信支付"),

    /**
     * 支付宝支付
     */
    ALIPAY("alipay", "支付宝支付"),

    /**
     * 现金支付
     */
    CASH("cash", "现金支付"),

    /**
     * 其他支付方式
     */
    OTHER("other", "其他方式");

    private final String code;
    private final String info;

    /**
     * 根据code获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static PayMethodEnum getByCode(String code) {
        for (PayMethodEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 