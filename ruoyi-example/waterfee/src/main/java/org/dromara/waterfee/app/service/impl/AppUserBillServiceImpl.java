package org.dromara.waterfee.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.app.domain.bo.AppUserBillConsumptionBo;
import org.dromara.waterfee.app.domain.bo.AppUserBillDetailBo;
import org.dromara.waterfee.app.domain.vo.*;
import org.dromara.waterfee.app.service.IAppUserBillService;
import org.dromara.waterfee.bill.domain.bo.WaterfeeBillBo;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterBo;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.meterReading.domain.vo.MeterReadingRecordVo;
import org.dromara.waterfee.meterReading.mapper.MeterReadingRecordMapper;
import org.dromara.waterfee.priceManage.domain.WaterfeePriceTier;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * App用户账单服务实现
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppUserBillServiceImpl implements IAppUserBillService {

    private final IWaterfeeBillService billService;
    private final IWaterfeeUserService userService;
    private final IWaterfeeMeterService meterService;
    private final MeterReadingRecordMapper readingRecordMapper;
//    private final WaterfeePriceTierMapper priceTierMapper;
//    private final WaterfeePriceConfigMapper waterfeePriceConfigMapper;

    @SneakyThrows
    @Override
    public List<AppUserBillConsumptionVo> getUserConsumption(AppUserBillConsumptionBo bo) {
        // 构建查询条件
        WaterfeeBillBo billBo = new WaterfeeBillBo();
        billBo.setCustomerId(bo.getUserId());
        billBo.setBillMonth(bo.getYear()); // 传入年份，如 2025，用于模糊匹配

        // 查询账单列表
        List<WaterfeeBillVo> billList = billService.queryList(billBo);

//        // 转换为消费量视图对象并按月份合并
//        Map<String, AppUserBillConsumptionVo> resultMap = billList.stream()
//            .map(this::convertToConsumptionVo)
//            .collect(Collectors.toMap(
//                AppUserBillConsumptionVo::getBillMonth,
//                vo -> vo,
//                (v1, v2) -> {
//                    v1.setBillId(null);
//                    v1.setConsumptionVolume(
//                        v1.getConsumptionVolume().add(v2.getConsumptionVolume()));
//                    v1.setTotalAmount(
//                        v1.getTotalAmount().add(v2.getTotalAmount()));
//                    // 当前读数取最大值
//                    v1.setCurrentReadingValue(
//                        v1.getCurrentReadingValue().max(v2.getCurrentReadingValue()));
//
//                    // 合并阶梯用水量和费用
//                    mergeTierInfo(v1, v2);
//
//                    return v1;
//                }
//            ));
        Map<String, AppUserBillConsumptionVo> resultMap = billList.stream()
            .map(bill -> {
                try {
                    return convertToConsumptionVo(bill);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException("转换失败", e); // 重新抛出为非受检异常
                }
            })
            .collect(Collectors.toMap(
                AppUserBillConsumptionVo::getBillMonth,
                vo -> vo,
                (v1, v2) -> {
                    v1.setBillId(null);
                    v1.setConsumptionVolume(v1.getConsumptionVolume().add(v2.getConsumptionVolume()));
                    v1.setTotalAmount(v1.getTotalAmount().add(v2.getTotalAmount()));
                    v1.setCurrentReadingValue(v1.getCurrentReadingValue().max(v2.getCurrentReadingValue()));
                    mergeTierInfo(v1, v2);
                    return v1;
                }
            ));


        return new ArrayList<>(resultMap.values());
    }

    /**
     * 合并两个账单的阶梯信息
     *
     * @param v1 第一个账单
     * @param v2 第二个账单
     */
    private void mergeTierInfo(AppUserBillConsumptionVo v1, AppUserBillConsumptionVo v2) {
        if (v1.getTierInfoMap() == null) {
            v1.setTierInfoMap(new HashMap<>());
        }

        if (v2.getTierInfoMap() != null) {
            v2.getTierInfoMap().forEach((tier, tierInfo) -> {
                if (v1.getTierInfoMap().containsKey(tier)) {
                    // 如果v1中已有该阶梯，则合并用水量和费用
                    AppUserBillConsumptionVo.TierInfo existingTierInfo = v1.getTierInfoMap().get(tier);

                    // 合并用水量
                    if (existingTierInfo.getUsage() != null && tierInfo.getUsage() != null) {
                        existingTierInfo.setUsage(existingTierInfo.getUsage().add(tierInfo.getUsage()));
                    } else if (tierInfo.getUsage() != null) {
                        existingTierInfo.setUsage(tierInfo.getUsage());
                    }

                    // 合并费用
                    if (existingTierInfo.getAmount() != null && tierInfo.getAmount() != null) {
                        existingTierInfo.setAmount(existingTierInfo.getAmount().add(tierInfo.getAmount()));
                    } else if (tierInfo.getAmount() != null) {
                        existingTierInfo.setAmount(tierInfo.getAmount());
                    }
                } else {
                    // 如果v1中没有该阶梯，则直接添加
                    v1.getTierInfoMap().put(tier, tierInfo);
                }
            });
        }
    }

    /**
     * 将账单视图对象转换为消费量视图对象
     *
     * @param bill 账单视图对象
     * @return 消费量视图对象
     */
//    private AppUserBillConsumptionVo convertToConsumptionVo(WaterfeeBillVo bill) {
//        AppUserBillConsumptionVo vo = new AppUserBillConsumptionVo();
//        vo.setBillId(bill.getBillId());
//        vo.setConsumptionVolume(bill.getConsumptionVolume());
//        vo.setCurrentReadingValue(bill.getCurrentReadingValue());
//        vo.setTotalAmount(bill.getTotalAmount());
//        vo.setBillMonth(bill.getBillMonth());
//
//        // 设置阶梯用水量和费用
//        Map<Integer, AppUserBillConsumptionVo.TierInfo> tierInfoMap = new HashMap<>();
//
//        // 获取账单对应的价格配置ID和用户ID
//        Long priceConfigId = bill.getPricePlanId();
//        Long userId = bill.getCustomerId();
//
//        // 如果没有必要的信息，则无法计算
//        if (priceConfigId == null || userId == null || bill.getConsumptionVolume() == null || bill.getBillMonth() == null) {
//            log.warn("缺少计算阶梯的必要信息，无法计算阶梯用水量");
//            createDefaultTierInfo(tierInfoMap, bill);
//            vo.setTierInfoMap(tierInfoMap);
//            return vo;
//        }
//
//        try {
//            // 查询用户信息
//            WaterfeeUser user = userService.queryById(userId);
//            if (user == null) {
//                throw new RuntimeException("未找到用户信息: " + userId);
//            }
//
//            // 查询阶梯水价配置
//            List<WaterfeePriceTier> tiers = priceTierMapper.selectList(
//                new LambdaQueryWrapper<WaterfeePriceTier>()
//                    .eq(WaterfeePriceTier::getPriceConfigId, priceConfigId)
//                    .orderByAsc(WaterfeePriceTier::getTierNumber)
//            );
//
//            if (tiers == null || tiers.isEmpty()) {
//                throw new RuntimeException("未找到价格配置: " + priceConfigId);
//            }
//
//            // 解析账单月份，格式为yyyyMM
//            String billMonth = bill.getBillMonth();
//            String year = billMonth.substring(0, 4);
//            String month = billMonth.substring(4);
//
//            log.info("开始计算账单ID: {} 的阶梯用水量，用户ID: {}, 账单月份: {}, 总用水量: {}",
//                bill.getBillId(), userId, billMonth, bill.getConsumptionVolume());
//
//            // 计算从1月到该月前一个月的累计用水量
//            BigDecimal previousMonthsUsage = calculatePreviousMonthsUsage(userId, year, month);
//            log.info("用户ID: {} 在 {} 年 {} 月前的累计用水量: {}", userId, year, month, previousMonthsUsage);
//
//            // 总用水量
//            BigDecimal totalUsage = bill.getConsumptionVolume();
//            // 当前用水量（剩余待分配用水量）
//            BigDecimal currentUsage = totalUsage;
//            // 年度累计用水量（从1月到当前月份前一个月）
//            BigDecimal annualUsage = previousMonthsUsage;
//            // 总费用
//            BigDecimal totalCharge = BigDecimal.ZERO;
//
//            // 使用Map存储每一级的用量和费用
//            Map<Integer, BigDecimal> tierUsageMap = new HashMap<>();
//            Map<Integer, BigDecimal> tierAmountMap = new HashMap<>();
//
//            // 处理每个阶梯
//            for (WaterfeePriceTier tier : tiers) {
//                int tierNumber = tier.getTierNumber();
//                BigDecimal start = tier.getStartQuantity();
//                BigDecimal end = tier.getEndQuantity();
//
//                log.info("处理第{}级阶梯，起始量: {}, 结束量: {}", tierNumber, start, end);
//
//                // 如果是居民用户，且阶梯价格按人口计算
//                if ("resident".equals(user.getUseWaterNature())) {
//                    // 查询价格配置
//                    // 注意：这里应该添加价格配置查询逻辑，但由于没有相关代码，暂时使用简化逻辑
//                    if (user.getUseWaterNumber() != null) {
//                        BigDecimal multiplier = BigDecimal.valueOf(user.getUseWaterNumber());
//                        start = start.multiply(multiplier);
//                        if (end != null) {
//                            end = end.multiply(multiplier);
//                        }
//                        log.info("按人口调整后的阶梯，起始量: {}, 结束量: {}", start, end);
//                    }
//                }
//
//                // 计算当前阶梯的总价格
//                BigDecimal price = tier.getPrice()
//                    .add(tier.getWaterResourceTax() != null ? tier.getWaterResourceTax() : BigDecimal.ZERO)
//                    .add(tier.getSewageTreatmentFee() != null ? tier.getSewageTreatmentFee() : BigDecimal.ZERO)
//                    .add(tier.getGarbageDisposalFee() != null ? tier.getGarbageDisposalFee() : BigDecimal.ZERO)
//                    .add(tier.getSanitationFee() != null ? tier.getSanitationFee() : BigDecimal.ZERO)
//                    .add(tier.getMaintenanceFund() != null ? tier.getMaintenanceFund() : BigDecimal.ZERO);
//
//                // 计算阶梯用水量
//                BigDecimal tierUsage = BigDecimal.ZERO;
//
//                // 计算本阶梯实际可计费的区间 = 当前阶梯范围 - 已用
//                BigDecimal tierAvailable;
//                if (end == null) {
//                    // 最后一个阶梯，没有上限
//                    tierAvailable = currentUsage;
//                } else {
//                    // 计算本阶梯的可用量 = 阶梯上限 - 阶梯下限 - (年度累计用水量 - 阶梯下限).max(0)
//                    tierAvailable = end.subtract(start)
//                        .subtract(annualUsage.subtract(start).max(BigDecimal.ZERO));
//                }
//
//                // 如果本阶梯还有可用量
//                if (tierAvailable.compareTo(BigDecimal.ZERO) > 0) {
//                    // 本阶梯实际用量 = min(剩余用水量, 阶梯可用量)
//                    tierUsage = currentUsage.min(tierAvailable);
//                }
//
//                log.info("第{}级阶梯可用量: {}, 实际用量: {}, 单价: {}",
//                    tierNumber, tierAvailable, tierUsage, price);
//
//                if (tierUsage.compareTo(BigDecimal.ZERO) > 0) {
//                    BigDecimal amount = tierUsage.multiply(price).setScale(2, BigDecimal.ROUND_HALF_UP);
//                    totalCharge = totalCharge.add(amount);
//
//                    // 保存到Map中
//                    tierUsageMap.put(tierNumber, tierUsage);
//                    tierAmountMap.put(tierNumber, amount);
//
//                    // 更新剩余用水量和年度累计用水量
//                    currentUsage = currentUsage.subtract(tierUsage);
//                    annualUsage = annualUsage.add(tierUsage);
//
//                    log.info("第{}级阶梯费用: {}, 剩余用水量: {}, 年度累计用水量: {}",
//                        tierNumber, amount, currentUsage, annualUsage);
//                }
//
//                if (currentUsage.compareTo(BigDecimal.ZERO) <= 0) {
//                    break;
//                }
//            }
//
//            // 将计算结果转换为TierInfo对象
//            for (Map.Entry<Integer, BigDecimal> entry : tierUsageMap.entrySet()) {
//                int tierNumber = entry.getKey();
//                BigDecimal usage = entry.getValue();
//                BigDecimal amount = tierAmountMap.get(tierNumber);
//
//                AppUserBillConsumptionVo.TierInfo tierInfo = new AppUserBillConsumptionVo.TierInfo();
//                tierInfo.setUsage(usage);
//                tierInfo.setAmount(amount);
//                tierInfoMap.put(tierNumber, tierInfo);
//            }
//
//            // 检查是否所有用水量都已分配
//            if (currentUsage.compareTo(BigDecimal.ZERO) > 0) {
//                log.warn("账单ID: {} 有未分配的用水量: {}", bill.getBillId(), currentUsage);
//            }
//
//            // 如果没有任何阶梯信息，则创建一个默认的
//            if (tierInfoMap.isEmpty()) {
//                log.warn("未能计算出任何阶梯信息，创建默认阶梯");
//                createDefaultTierInfo(tierInfoMap, bill);
//            }
//
//        } catch (Exception e) {
//            log.error("计算阶梯用水量和费用时出错: {}", e.getMessage(), e);
//            // 出错时创建一个默认阶梯
//            createDefaultTierInfo(tierInfoMap, bill);
//        }
//
//        vo.setTierInfoMap(tierInfoMap);
//        return vo;
//    }
    private AppUserBillConsumptionVo convertToConsumptionVo(WaterfeeBillVo bill) throws JsonProcessingException {
        AppUserBillConsumptionVo vo = new AppUserBillConsumptionVo();
        vo.setBillId(bill.getBillId());
        vo.setConsumptionVolume(bill.getConsumptionVolume());
        vo.setCurrentReadingValue(bill.getCurrentReadingValue());
        vo.setTotalAmount(bill.getTotalAmount());
        vo.setBillMonth(bill.getBillMonth());

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(bill.getDetailJson());

        // 设置阶梯用水量和费用
        Map<Integer, AppUserBillConsumptionVo.TierInfo> tierInfoMap = new HashMap<>();
        JsonNode tierDetails = root.get("tierDetails");
        if (tierDetails != null && tierDetails.isArray()) {
            for (JsonNode tier : tierDetails) {
                Integer tierNumber = tier.get("tierNumber").asInt();
                BigDecimal usage = tier.has("usage") && !tier.get("usage").isNull()
                    ? tier.get("usage").decimalValue() : BigDecimal.ZERO;
                BigDecimal amount = tier.has("amount") && !tier.get("amount").isNull()
                    ? tier.get("amount").decimalValue() : BigDecimal.ZERO;

                AppUserBillConsumptionVo.TierInfo info = new AppUserBillConsumptionVo.TierInfo();
                info.setUsage(usage);
                info.setAmount(amount);

                tierInfoMap.put(tierNumber, info);
            }
        }
        vo.setTierInfoMap(tierInfoMap);
        return vo;
    }

    /**
     * 计算指定用户在指定年份从1月到指定月份前一个月的累计用水量
     *
     * @param userId 用户ID
     * @param year   年份
     * @param month  月份
     * @return 累计用水量
     */
    private BigDecimal calculatePreviousMonthsUsage(Long userId, String year, String month) {
        BigDecimal totalUsage = BigDecimal.ZERO;

        try {
            // 获取当前月份的数字表示
            int currentMonth = Integer.parseInt(month);

            // 如果是1月，则没有前几个月的用水量
            if (currentMonth <= 1) {
                return totalUsage;
            }

            // 构建查询条件
            WaterfeeBillBo billBo = new WaterfeeBillBo();
            billBo.setCustomerId(userId);

            // 查询当年所有账单
            List<WaterfeeBillVo> yearBills = billService.queryList(billBo);

            // 过滤出1月到当前月份前一个月的账单
            for (WaterfeeBillVo bill : yearBills) {
                String billMonth = bill.getBillMonth();

                // 确保账单月份格式正确
                if (billMonth != null && billMonth.length() >= 6) {
                    String billYear = billMonth.substring(0, 4);
                    String billMonthNum = billMonth.substring(4);

                    // 只统计当年且月份小于当前月份的账单
                    if (year.equals(billYear)) {
                        try {
                            int billMonthInt = Integer.parseInt(billMonthNum);
                            if (billMonthInt < currentMonth) {
                                if (bill.getConsumptionVolume() != null) {
                                    totalUsage = totalUsage.add(bill.getConsumptionVolume());
                                }
                            }
                        } catch (NumberFormatException e) {
                            log.warn("账单月份格式错误: {}", billMonth);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("计算前几个月累计用水量时出错: {}", e.getMessage(), e);
        }

        return totalUsage;
    }

    /**
     * 创建默认的阶梯信息
     *
     * @param tierInfoMap 阶梯信息Map
     * @param bill        账单视图对象
     */
    private void createDefaultTierInfo(Map<Integer, AppUserBillConsumptionVo.TierInfo> tierInfoMap, WaterfeeBillVo bill) {
        // 创建一个简单的阶梯信息，将所有用水量放在第一阶梯
        if (bill.getConsumptionVolume() != null) {
            AppUserBillConsumptionVo.TierInfo tierInfo = new AppUserBillConsumptionVo.TierInfo();
            tierInfo.setUsage(bill.getConsumptionVolume());
            tierInfo.setAmount(bill.getTotalAmount());
            tierInfoMap.put(1, tierInfo);
        }
    }

    @Override
    public AppUserBillDetailVo getUserBillDetail(AppUserBillDetailBo bo) throws JsonProcessingException {
        // 1. 创建返回对象
        AppUserBillDetailVo detailVo = new AppUserBillDetailVo();

        // 2. 获取用户信息
        WaterfeeUser user;
        if (bo.getUserId() != null) {
            user = userService.queryById(bo.getUserId());
        } else {
            user = userService.queryByUserNo(bo.getUserNo());
        }

        if (user == null) {
            return null;
        }

        // 3. 设置用户基本信息
        UserInfoVo userInfo = new UserInfoVo();
        userInfo.setUserName(user.getUserName());
        userInfo.setUserNo(user.getUserNo());
        userInfo.setAddress(user.getAddress());
        userInfo.setLadderUsage(user.getLadderUsage());

        detailVo.setUserInfo(userInfo);

        // 4. 查询用水信息
        String yearMonth = bo.getYearMonth(); // 格式：yyyy-MM
        String[] parts = yearMonth.split("-");
        String year = parts[0];
        String month = parts[1];

        // 当月账单查询
        WaterfeeBillBo currentMonthBo = new WaterfeeBillBo();
        currentMonthBo.setCustomerId(user.getUserId());
        currentMonthBo.setBillMonth(year + month);
        List<WaterfeeBillVo> currentMonthBills = billService.queryList(currentMonthBo);

        // 上月账单查询
        int prevMonth = Integer.parseInt(month) - 1;
        int prevYear = Integer.parseInt(year);
        if (prevMonth <= 0) {
            prevMonth = 12;
            prevYear -= 1;
        }
        String prevYearMonth = String.format("%04d-%02d", prevYear, prevMonth);

        WaterfeeBillBo lastMonthBo = new WaterfeeBillBo();
        lastMonthBo.setCustomerId(user.getUserId());
        lastMonthBo.setBillMonth(prevYearMonth);
        List<WaterfeeBillVo> lastMonthBills = billService.queryList(lastMonthBo);

        // 5. 设置用水信息
        WaterConsumptionInfoVo waterInfo = new WaterConsumptionInfoVo();
        waterInfo.setBillMonth(yearMonth);

        // 合并当月用水量
        waterInfo.setCurrentMonthConsumption(
            currentMonthBills.stream()
                .map(WaterfeeBillVo::getConsumptionVolume)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        // 合并上月用水量
        waterInfo.setLastMonthConsumption(
            lastMonthBills.stream()
                .map(WaterfeeBillVo::getConsumptionVolume)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        detailVo.setWaterConsumptionInfo(waterInfo);

        // 6. 根据计费方式查询水价配置
        String billingMethod = user.getBillingMethod();
        if (billingMethod != null) {
//            Long priceConfigId = Long.valueOf(billingMethod);
//            List<WaterfeePriceTier> tiers = priceTierMapper.selectTiersByPriceConfigId(priceConfigId);

            ObjectMapper mapper = new ObjectMapper();
            String json = billService.queryList(new WaterfeeBillBo()).get(0).getDetailJson();
            JsonNode root = mapper.readTree(json);

            List<WaterPriceTierInfoVo> tierInfoList = new ArrayList<>();
//            for (WaterfeePriceTier tier : tiers) {
//                WaterPriceTierInfoVo tierInfo = getWaterPriceTierInfoVo(tier, user);
//
//                tierInfoList.add(tierInfo);
//            }

            JsonNode tierDetails = root.get("tierDetails");
            if (tierDetails != null && tierDetails.isArray()) {
                for (JsonNode node : tierDetails) {
                    WaterfeePriceTier tier = new WaterfeePriceTier();

                    tier.setTierNumber(node.get("tierNumber").asInt());
                    tier.setStartQuantity(new BigDecimal(node.get("startQuantity").asText()));
                    if (node.has("endQuantity") && !node.get("endQuantity").isNull()) {
                        tier.setEndQuantity(new BigDecimal(node.get("endQuantity").asText()));
                    }
                    tier.setPrice(new BigDecimal(node.get("price").asText()));
                    tier.setWaterResourceTax(new BigDecimal(node.get("waterResourceTax").asText()));
                    tier.setSewageTreatmentFee(new BigDecimal(node.get("sewageTreatmentFee").asText()));
                    tier.setGarbageDisposalFee(new BigDecimal(node.get("garbageDisposalFee").asText()));
                    tier.setSanitationFee(new BigDecimal(node.get("sanitationFee").asText()));
                    tier.setMaintenanceFund(new BigDecimal(node.get("maintenanceFund").asText()));

                    WaterPriceTierInfoVo tierInfo = BeanUtil.copyProperties(tier, WaterPriceTierInfoVo.class);
                    tierInfoList.add(tierInfo);
                }
            }

            detailVo.setWaterPriceTierInfoList(tierInfoList);
        }

        // 7. 查询水表信息和读表时间
        WaterfeeMeterBo meterBo = new WaterfeeMeterBo();
        meterBo.setUserId(user.getUserId());
        List<WaterfeeMeterVo> meters = meterService.queryList(meterBo);

        if (!meters.isEmpty()) {
            WaterfeeMeterVo meter = meters.get(0); // 获取第一个水表
            String meterNo = meter.getMeterNo();

            // 查询最新的读表记录
            MeterReadingRecordVo readingRecord = readingRecordMapper.selectLatestByMeterNo(meterNo);

            if (readingRecord != null) {
                MeterReadingTimeInfoVo readingTimeInfo = new MeterReadingTimeInfoVo();
                readingTimeInfo.setMeterNo(meterNo);
                readingTimeInfo.setReadingTime(readingRecord.getReadingTime());
                readingTimeInfo.setLastReadingTime(readingRecord.getLastReadingTime());

                detailVo.setMeterReadingTimeInfo(readingTimeInfo);
            }
        }

        return detailVo;
    }

//    @NotNull
//    private WaterPriceTierInfoVo getWaterPriceTierInfoVo(WaterfeePriceTier tier, WaterfeeUser user) {
//        WaterPriceTierInfoVo tierInfo = new WaterPriceTierInfoVo();
//
//        BigDecimal multiplier = BigDecimal.ONE; // 默认乘数为1
//        // 如果是居民用户，且阶梯价格按人口计算
//        if ("resident".equals(user.getUseWaterNature())) {
//            WaterfeePriceConfig priceConfig = waterfeePriceConfigMapper.selectById(tier.getPriceConfigId());
//            if (priceConfig.getIsPopulation() == 1 && user.getUseWaterNumber() != null) {
//                multiplier = BigDecimal.valueOf(user.getUseWaterNumber());
//            }
//        }
//
//        tierInfo.setTierNumber(tier.getTierNumber());
//        tierInfo.setStartQuantity(tier.getStartQuantity().multiply(multiplier));
//        if (tier.getEndQuantity() != null) {
//            tierInfo.setEndQuantity(tier.getEndQuantity().multiply(multiplier));
//        } else {
//            tierInfo.setEndQuantity(null); // 如果没有结束量，则设置为null
//        }
//        tierInfo.setPrice(tier.getPrice());
//        tierInfo.setWaterResourceTax(tier.getWaterResourceTax());
//        tierInfo.setSewageTreatmentFee(tier.getSewageTreatmentFee());
//        tierInfo.setGarbageDisposalFee(tier.getGarbageDisposalFee());
//        tierInfo.setSanitationFee(tier.getSanitationFee());
//        tierInfo.setMaintenanceFund(tier.getMaintenanceFund());
//        return tierInfo;
//    }
}
