package org.dromara.waterfee.area.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.convert.Convert;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.area.domain.bo.WaterfeeAreaBo;
import org.dromara.waterfee.area.domain.vo.WaterfeeAreaVo;
import org.dromara.waterfee.area.service.IWaterfeeAreaService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 区域
 * 前端访问路由地址为:/waterfee/area
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/area")
public class WaterfeeAreaController extends BaseController {

    private final IWaterfeeAreaService waterfeeAreaService;

    /**
     * 查询区域列表
     */
    @SaCheckPermission("waterfee:area:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeAreaVo> list(WaterfeeAreaBo bo, PageQuery pageQuery) {
        return waterfeeAreaService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询区域下拉列表
     */
    @SaCheckPermission("waterfee:area:list")
    @GetMapping("/getSelectList")
    public R list(WaterfeeAreaBo bo) {
        return R.ok(waterfeeAreaService.queryList(bo));
    }

    /**
     * 导出区域列表
     */
    @SaCheckPermission("waterfee:area:export")
    @Log(title = "区域", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeAreaBo bo, HttpServletResponse response) {
        List<WaterfeeAreaVo> list = waterfeeAreaService.queryList(bo);
        ExcelUtil.exportExcel(list, "区域", WaterfeeAreaVo.class, response);
    }

    /**
     * 查询区域列表（排除节点）
     *
     * @param areaId
     */
    @SaCheckPermission("system:area:list")
    @GetMapping("/list/exclude/{areaId}")
    public R<List<WaterfeeAreaVo>> excludeChild(@PathVariable(value = "areaId", required = false) Long areaId) {
        List<WaterfeeAreaVo> areaVos = waterfeeAreaService.queryList(new WaterfeeAreaBo());
        areaVos.removeIf(d -> d.getAreaId().equals(areaId)
            || StringUtils.splitList(d.getAncestors()).contains(Convert.toStr(areaId)));
        return R.ok(areaVos);
    }

    /**
     * 获取区域详细信息
     *
     * @param areaId 主键
     */
    @SaCheckPermission("waterfee:area:query")
    @GetMapping("/{areaId}")
    public R<WaterfeeAreaVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("areaId") Long areaId) {
        return R.ok(waterfeeAreaService.queryById(areaId));
    }

    /**
     * 新增区域
     */
    @SaCheckPermission("waterfee:area:add")
    @Log(title = "区域", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeAreaBo bo) {
        return toAjax(waterfeeAreaService.insertByBo(bo));
    }

    /**
     * 修改区域
     */
    @SaCheckPermission("waterfee:area:edit")
    @Log(title = "区域", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeAreaBo bo) {
        return toAjax(waterfeeAreaService.updateByBo(bo));
    }

    /**
     * 删除区域
     *
     * @param areaIds 主键串
     */
    @SaCheckPermission("waterfee:area:remove")
    @Log(title = "区域", businessType = BusinessType.DELETE)
    @DeleteMapping("/{areaIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("areaIds") Long[] areaIds) {
        return toAjax(waterfeeAreaService.deleteWithValidByIds(List.of(areaIds), true));
    }
}
