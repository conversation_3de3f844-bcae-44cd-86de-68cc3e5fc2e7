package org.dromara.waterfee.user.domain.bo;

import org.dromara.waterfee.user.domain.VatManagement;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 增值税管理业务对象 vat_management
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = VatManagement.class, reverseConvertGenerate = false)
public class VatManagementBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 关联账单编号
     */
    @NotBlank(message = "关联账单编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String billNumber;

    /**
     * 发票类型（普通/专用）（字典invoice_type）
     */
    @NotBlank(message = "发票类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String invoiceType;

    /**
     * 发票代码
     */
    @NotBlank(message = "发票代码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String invoiceCode;

    /**
     * 发票号码
     */
    @NotBlank(message = "发票号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String invoiceNumber;

    /**
     * 发票状态（已开具/已红冲/已作废）（字典invoice_status）
     */
    @NotBlank(message = "发票状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String invoiceStatus;

    /**
     * 价税合计金额
     */
    @NotNull(message = "价税合计金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long totalAmount;

    /**
     * 不含税金额
     */
    @NotNull(message = "不含税金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long taxExclusiveAmount;

    /**
     * 税率（如0.13）
     */
    @NotNull(message = "税率（如0.13）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long taxRate;

    /**
     * 税额
     */
    @NotNull(message = "税额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long taxAmount;

    /**
     * 是否含税（1含税/0不含税）（字典yes_no）
     */
    @NotBlank(message = "是否含税不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isTaxIncluded;

    /**
     * 购方名称
     */
    @NotBlank(message = "购方名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String buyerName;

    /**
     * 购方纳税人识别号
     */
    @NotBlank(message = "购方纳税人识别号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String buyerTaxId;

    /**
     * 购方电话
     */
    private String buyerTel;

    /**
     * 购方地址
     */
    private String buyerAddress;

    /**
     * 购方开户行及账号
     */
    private String buyerBankAccount;

    /**
     * 销方名称
     */
    @NotBlank(message = "销方名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sellerName;

    /**
     * 销方纳税人识别号
     */
    @NotBlank(message = "销方纳税人识别号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sellerTaxId;

    /**
     * 销方电话
     */
    private String sellerTel;

    /**
     * 销方地址
     */
    private String sellerAddress;

    /**
     * 销方开户行及账号
     */
    @NotBlank(message = "销方开户行及账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sellerBankAccount;

    /**
     * 开票时间
     */
    @NotNull(message = "开票时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date issueTime;

    /**
     * 红冲时间
     */
    private Date redFlushTime;

    /**
     * 作废时间
     */
    private Date cancelTime;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 红冲原因
     */
    private String redFlushReason;

    /**
     * 作废原因
     */
    private String cancelReason;


}
