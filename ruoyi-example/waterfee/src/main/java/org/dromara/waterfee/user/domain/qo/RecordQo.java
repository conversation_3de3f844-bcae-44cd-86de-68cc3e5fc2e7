package org.dromara.waterfee.user.domain.qo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class RecordQo {

    /** 用户编号或用户名 */
    private String userNoOrUserName;

    /** 查询开始时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /** 查询结束时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

}
