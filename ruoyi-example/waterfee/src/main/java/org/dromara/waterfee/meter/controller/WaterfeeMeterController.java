package org.dromara.waterfee.meter.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.alibaba.excel.EasyExcel;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.area.domain.vo.WaterfeeAreaVo;
import org.dromara.waterfee.area.service.IWaterfeeAreaService;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterBo;
import org.dromara.waterfee.meter.domain.vo.IntelligentMeterImportVo;
import org.dromara.waterfee.meter.domain.vo.MechanicalMeterImportVo;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.meterBook.domain.vo.MeterBookVo;
import org.dromara.waterfee.meterBook.service.IMeterBookService;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 水表信息Controller 前端接口： /waterfee/meter
 *
 * <AUTHOR>
 * @date 2025-04-02
 */

@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/meter")
public class WaterfeeMeterController extends BaseController {

    private final IWaterfeeMeterService meterService;
    private final IMeterBookService meterBookService;
    private final IWaterfeeAreaService areaService;

    /**
     * 查询水表信息列表
     */
    @SaCheckPermission("meter:meter:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeMeterVo> list(WaterfeeMeterBo bo, PageQuery pageQuery) {
        return meterService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出水表信息列表
     */
    @SaCheckPermission("meter:meter:export")
    @Log(title = "水表信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeMeterBo bo, HttpServletResponse response) {
        List<WaterfeeMeterVo> list = meterService.queryList(bo);
        ExcelUtil.exportExcel(list, "水表信息", WaterfeeMeterVo.class, response);
    }

    /**
     * 获取导入模板
     */
    @SaCheckPermission("meter:meter:import")
    @GetMapping("/importTemplate")
    public void importTemplate(@RequestParam("meterType") Integer meterType, HttpServletResponse response) {
        try {
            log.info("收到导入模板下载请求，水表类型: {}", meterType);

            // 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName;

            // 根据水表类型选择不同的VO类和创建对应的空列表
            if (meterType != null && meterType == 2) {
                fileName = "智能表导入模板";
                List<IntelligentMeterImportVo> list = new ArrayList<>();
                ExcelUtil.exportExcel(list, fileName, IntelligentMeterImportVo.class, response);
            } else {
                fileName = "机械表导入模板";
                List<MechanicalMeterImportVo> list = new ArrayList<>();
                ExcelUtil.exportExcel(list, fileName, MechanicalMeterImportVo.class, response);
            }

            fileName = java.net.URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            log.info("导出Excel模板完成");
        } catch (Exception e) {
            log.error("下载导入模板失败", e);
        }
    }

    /**
     * 导入水表数据
     */
    @SaCheckPermission("meter:meter:import")
    @Log(title = "水表信息", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Map<String, Object>> importData(@RequestParam("file") MultipartFile file, @RequestParam("meterType") Integer meterType) {
        try {
            // 检查文件是否为空
            if (file == null || file.isEmpty()) {
                return R.fail("请选择要导入的文件");
            }

            // 检查文件类型
            String originalFilename = file.getOriginalFilename();
            if (!StringUtils.endsWithAny(originalFilename, ".xls", ".xlsx")) {
                return R.fail("请上传Excel文件");
            }

            log.info("收到数据导入请求，水表类型: {}, 文件名: {}, 文件大小: {}", meterType, originalFilename, file.getSize());

            // 根据水表类型选择不同的VO类读取Excel
            List<?> importList;
            try {
                if (meterType != null && meterType == 2) {
                    importList = EasyExcel.read(file.getInputStream())
                        .head(IntelligentMeterImportVo.class)
                        .sheet()
                        .doReadSync();
                    log.info("解析到智能表Excel数据 {} 条", importList.size());
                } else {
                    importList = EasyExcel.read(file.getInputStream())
                        .head(MechanicalMeterImportVo.class)
                        .sheet()
                        .doReadSync();
                    log.info("解析到机械表Excel数据 {} 条", importList.size());
                }
            } catch (IOException e) {
                log.error("解析Excel文件失败", e);
                return R.fail("解析Excel文件失败：" + e.getMessage());
            }

            // 调用Service处理导入数据
            Map<String, Object> result = meterService.importMeter(importList, meterType);

            log.info("导入完成，成功: {}, 失败: {}",
                result.get("successCount"), result.get("failureCount"));

            return R.ok(result);
        } catch (ServiceException e) {
            log.error("导入失败", e);
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("导入异常", e);
            return R.fail("导入失败：" + e.getMessage());
        }
    }

    /**
     * 获取水表信息详细信息
     *
     * @param meterId 主键
     */
    @SaCheckPermission("meter:meter:query")
    @GetMapping("/id/{meterId}")
    public R<WaterfeeMeterVo> getInfoById(@NotNull(message = "主键不能为空") @PathVariable Long meterId) {
        return R.ok(meterService.queryById(meterId));
    }

    /**
     * 批量获取水表信息详细信息
     *
     * @param meterIds 主键数组
     */
    @SaCheckPermission("meter:meter:query")
    @PostMapping("/ids")
    public R<List<WaterfeeMeterVo>> getInfoByIds(@NotEmpty(message = "主键不能为空") @RequestBody Long[] meterIds) {
        return R.ok(meterService.queryByIds(Arrays.asList(meterIds)));
    }

    /**
     * 获取水表信息详细信息
     *
     * @param meterNo 水表编号
     */
    @SaCheckPermission("meter:meter:query")
    @GetMapping("/no/{meterNo}")
    public R<WaterfeeMeterVo> getInfoByNo(@NotNull(message = "水表编号不能为空") @PathVariable String meterNo) {
        return R.ok(meterService.queryByNo(meterNo));
    }

    /**
     * 新增水表信息
     */
    @SaCheckPermission("meter:meter:add")
    @Log(title = "水表信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeMeterBo bo) {
        return toAjax(meterService.insertByBo(bo));
    }

    /**
     * 修改水表信息
     */
    @SaCheckPermission("meter:meter:edit")
    @Log(title = "水表信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeMeterBo bo) {
        return toAjax(meterService.updateByBo(bo));
    }

    /**
     * 删除水表信息
     *
     * @param meterIds 主键串
     */
    @SaCheckPermission("meter:meter:remove")
    @Log(title = "水表信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{meterIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] meterIds) {
        return toAjax(meterService.deleteWithValidByIds(Arrays.asList(meterIds), true));
    }

    /**
     * 关联用水户
     */
    @SaCheckPermission("meter:meter:associate")
    @Log(title = "关联用水户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/associate/{meterId}")
    public R<Void> associateUser(@NotNull(message = "水表ID不能为空") @PathVariable Long meterId,
                                 @NotNull(message = "用水户ID不能为空") @RequestParam Long userId) {
        return toAjax(meterService.associateUser(meterId, userId));
    }

    /**
     * 根据用户编号查询水表信息
     *
     * @param userNo 用户编号
     */
    @SaCheckPermission("meter:meter:query")
    @GetMapping("/user/{userNo}")
    public R<List<WaterfeeMeterVo>> getInfoByUserNo(@NotBlank(message = "用户编号不能为空") @PathVariable String userNo) {
        try {
            log.info("根据用户编号查询水表信息，用户编号：{}", userNo);
            List<WaterfeeMeterVo> meterList = meterService.queryByUserNo(userNo);
            return R.ok(meterList);
        } catch (ServiceException e) {
            log.error("查询失败", e);
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("查询异常", e);
            return R.fail("系统异常");
        }
    }

    /**
     * 批量获取水表信息详细信息（根据水表编号）
     *
     * @param meterNos 水表编号数组
     */
    @SaCheckPermission("meter:meter:query")
    @PostMapping("/nos")
    public R<List<WaterfeeMeterVo>> meterInfoByNos(@NotEmpty(message = "水表编号不能为空") @RequestBody String[] meterNos) {
        log.info("批量查询水表信息，水表编号：{}", Arrays.toString(meterNos));
        return R.ok(meterService.queryByNos(Arrays.asList(meterNos)));
    }

    /**
     * 批量获取区域信息
     *
     * @param areaIds 区域ID数组
     */
    @SaCheckPermission("meter:meter:query")
    @PostMapping("/areas")
    public R<List<WaterfeeAreaVo>> areaInfoBatch(@NotEmpty(message = "区域ID不能为空") @RequestBody Long[] areaIds) {
        log.info("批量查询区域信息，区域ID：{}", Arrays.toString(areaIds));
        return R.ok(areaService.queryByIds(Arrays.asList(areaIds)));
    }

    /**
     * 批量获取表册信息
     *
     * @param bookIds 表册ID数组
     */
    @SaCheckPermission("meter:meter:query")
    @PostMapping("/books")
    public R<List<MeterBookVo>> meterBookInfoBatch(@NotEmpty(message = "表册ID不能为空") @RequestBody Long[] bookIds) {
        log.info("批量查询表册信息，表册ID：{}", Arrays.toString(bookIds));
        return R.ok(meterBookService.queryByIds(Arrays.asList(bookIds)));
    }
}
