package org.dromara.waterfee.pay.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.waterfee.pay.domain.WechatRefundRecord;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 微信退款记录视图对象 wechat_refund_record
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WechatRefundRecord.class)
public class WechatRefundRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 退款单号 (主键)
     */
    @ExcelProperty(value = "退款单号 (主键)")
    private String refundId;

    /**
     * 缴费明细ID (订单号)
     */
    @ExcelProperty(value = "缴费明细ID (订单号)")
    private String paymentDetailId;

    /**
     * 微信退款单号
     */
    @ExcelProperty(value = "微信退款单号")
    private String wechatRefundId;

    /**
     * 退款状态SUCCESS—退款成功 CLOSED—退款关闭 PROCESSING—退款处理中 ABNORMAL—退款异常
     */
    @ExcelProperty(value = "退款状态SUCCESS—退款成功 CLOSED—退款关闭 PROCESSING—退款处理中 ABNORMAL—退款异常")
    private String refundStatus;

    /**
     * 退款成功时间
     */
    @ExcelProperty(value = "退款成功时间")
    private Date successTime;

    /**
     * 退款入账账户
     */
    @ExcelProperty(value = "退款入账账户")
    private String userReceivedAccount;

    /**
     * 交易流水号 (支付平台返回)
     */
    @ExcelProperty(value = "交易流水号 (支付平台返回)")
    private String transactionId;

    /**
     * 订单总金额
     */
    @ExcelProperty(value = "订单总金额")
    private BigDecimal paymentAmount;

    /**
     * 退款金额
     */
    @ExcelProperty(value = "退款金额")
    private BigDecimal paymentRefund;

    /**
     * 实际支付金额（不包含代金券）
     */
    @ExcelProperty(value = "实际支付金额")
    private BigDecimal payerAmount;

    /**
     * 用户实际退款金额（不包含代金券）
     */
    @ExcelProperty(value = "用户实际退款金额")
    private BigDecimal payerRefund;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
