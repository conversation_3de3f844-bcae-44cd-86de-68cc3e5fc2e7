package org.dromara.waterfee.area.domain.bo;

import org.dromara.waterfee.area.domain.WaterfeeArea;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 区域业务对象 waterfee_area
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeArea.class, reverseConvertGenerate = false)
public class WaterfeeAreaBo extends BaseEntity {

    /**
     * 区域id
     */
    @NotNull(message = "区域id不能为空", groups = { EditGroup.class })
    private Long areaId;

    /**
     * 父区域id
     */
    private Long parentId;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 区域类别编码
     */
    private String areaCategory;

    /**
     * 显示顺序
     */
    private Long orderNum;

    /**
     * 负责人
     */
    private Long leader;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 区域状态（0正常 1停用）
     */
    private String status;


}
