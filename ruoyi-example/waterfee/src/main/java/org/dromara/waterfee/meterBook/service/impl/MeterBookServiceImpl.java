package org.dromara.waterfee.meterBook.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.model.LoginUser;
import org.dromara.waterfee.area.domain.WaterfeeArea;
import org.dromara.waterfee.area.mapper.WaterfeeAreaMapper;
import org.dromara.waterfee.meterBook.domain.MeterBook;
import org.dromara.waterfee.meterBook.domain.bo.MeterBookBo;
import org.dromara.waterfee.meterBook.domain.vo.MeterBookVo;
import org.dromara.waterfee.meterBook.mapper.MeterBookMapper;
import org.dromara.waterfee.meterBook.service.IMeterBookService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * u6284u8868u624bu518cu670du52a1u5b9eu73b0u7c7b
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@RequiredArgsConstructor
@Service
public class MeterBookServiceImpl implements IMeterBookService {

    private final MeterBookMapper baseMapper;
    private final WaterfeeAreaMapper areaMapper;
    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * u67e5u8be2u6284u8868u624bu518c
     *
     * @param id u6284u8868u624bu518cu4e3bu952e
     * @return u6284u8868u624bu518c
     */
    @Override
    public MeterBookVo queryById(Long id) {
        MeterBookVo vo = baseMapper.selectVoById(id);
        if (vo != null && vo.getAreaId() != null) {
            WaterfeeArea area = areaMapper.selectById(vo.getAreaId());
            if (area != null) {
                vo.setAreaName(area.getAreaName());
            }
        }
        return vo;
    }

    /**
     * u67e5u8be2u6284u8868u624bu518cu5217u8868
     *
     * @param bo u6284u8868u624bu518c
     * @return u6284u8868u624bu518c
     */
    @Override
    public TableDataInfo<MeterBookVo> queryPageList(MeterBookBo bo, PageQuery pageQuery) {

        LambdaQueryWrapper<MeterBook> lqw = buildQueryWrapper(bo);
        Page<MeterBookVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // u8865u5145u533au57dfu540du79f0
        List<MeterBookVo> list = result.getRecords();
        if (!list.isEmpty()) {
            List<Long> areaIds = list.stream().map(MeterBookVo::getAreaId).distinct().toList();
            List<WaterfeeArea> areas = areaMapper.selectBatchIds(areaIds);
            Map<Long, String> areaMap = areas.stream().collect(
                java.util.stream.Collectors.toMap(WaterfeeArea::getAreaId, WaterfeeArea::getAreaName));

            list.forEach(vo -> vo.setAreaName(areaMap.get(vo.getAreaId())));
        }

        return TableDataInfo.build(result);
    }

    /**
     * u67e5u8be2u6284u8868u624bu518cu5217u8868
     *
     * @param bo u6284u8868u624bu518c
     * @return u6284u8868u624bu518c
     */
    @Override
    public List<MeterBookVo> queryList(MeterBookBo bo) {
        LambdaQueryWrapper<MeterBook> lqw = buildQueryWrapper(bo);
        List<MeterBookVo> list = baseMapper.selectVoList(lqw);

        // u8865u5145u533au57dfu540du79f0
        if (!list.isEmpty()) {
            List<Long> areaIds = list.stream().map(MeterBookVo::getAreaId).distinct().toList();
            List<WaterfeeArea> areas = areaMapper.selectBatchIds(areaIds);
            Map<Long, String> areaMap = areas.stream().collect(
                java.util.stream.Collectors.toMap(WaterfeeArea::getAreaId, WaterfeeArea::getAreaName));

            list.forEach(vo -> vo.setAreaName(areaMap.get(vo.getAreaId())));
        }

        return list;
    }

    private LambdaQueryWrapper<MeterBook> buildQueryWrapper(MeterBookBo bo) {
        LambdaQueryWrapper<MeterBook> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAreaId() != null, MeterBook::getAreaId, bo.getAreaId());
        lqw.like(StringUtils.isNotBlank(bo.getBookNo()), MeterBook::getBookNo, bo.getBookNo());
        lqw.like(StringUtils.isNotBlank(bo.getBookName()), MeterBook::getBookName, bo.getBookName());
        lqw.eq(StringUtils.isNotBlank(bo.getReadType()), MeterBook::getReadType, bo.getReadType());
        lqw.eq(StringUtils.isNotBlank(bo.getReadCycle()), MeterBook::getReadCycle, bo.getReadCycle());
        lqw.eq(bo.getReadDay() != null, MeterBook::getReadDay, bo.getReadDay());
        lqw.eq(bo.getReadBaseDay() != null, MeterBook::getReadBaseDay, bo.getReadBaseDay());
        lqw.eq(bo.getReader() != null, MeterBook::getReader, bo.getReader());
        lqw.eq(bo.getReaderLeader() != null, MeterBook::getReaderLeader, bo.getReaderLeader());
        return lqw;
    }

    /**
     * u65b0u589eu6284u8868u624bu518c
     *
     * @param bo u6284u8868u624bu518c
     * @return u7ed3u679c
     */
    @Override
    public Boolean insertByBo(MeterBookBo bo) {
        MeterBook add = MapstructUtils.convert(bo, MeterBook.class);

        String tenantId = LoginHelper.getTenantId();
        LoginUser read = remoteUserService.getUserInfo(bo.getReader(), tenantId);
        LoginUser readLeader = remoteUserService.getUserInfo(bo.getReaderLeader(), tenantId);

        if (add != null) {
            add.setReaderName(read.getNickname());
            add.setReaderLeaderName(readLeader.getNickname());
            validEntityBeforeSave(add);
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * u4feeu6539u6284u8868u624bu518c
     *
     * @param bo u6284u8868u624bu518c
     * @return u7ed3u679c
     */
    @Override
    public Boolean updateByBo(MeterBookBo bo) {
        MeterBook update = MapstructUtils.convert(bo, MeterBook.class);

        String tenantId = LoginHelper.getTenantId();
        LoginUser read = remoteUserService.getUserInfo(bo.getReader(), tenantId);
        LoginUser readLeader = remoteUserService.getUserInfo(bo.getReaderLeader(), tenantId);

        if (update != null) {
            update.setReaderName(read.getNickname());
            update.setReaderLeaderName(readLeader.getNickname());
            validEntityBeforeSave(update);
        }
        return baseMapper.updateById(update) > 0;
    }

    /**
     * u4fddu5b58u524du7684u6570u636eu6821u9a8c
     *
     * @param entity u5b9eu4f53u5bf9u8c61
     */
    private void validEntityBeforeSave(MeterBook entity) {
        // u6821u9a8cu624bu518cu7f16u53f7u552fu4e00u6027
        if (StringUtils.isNotEmpty(entity.getBookNo())) {
            LambdaQueryWrapper<MeterBook> lqw = Wrappers.lambdaQuery();
            lqw.eq(MeterBook::getBookNo, entity.getBookNo());
            if (entity.getId() != null) {
                lqw.ne(MeterBook::getId, entity.getId());
            }
            long count = baseMapper.selectCount(lqw);
            if (count > 0) {
                throw new RuntimeException("u624bu518cu7f16u53f7u5df2u5b58u5728");
            }
        }

        // u6821u9a8cu533au57dfu662fu5426u5b58u5728
        if (entity.getAreaId() != null) {
            WaterfeeArea area = areaMapper.selectById(entity.getAreaId());
            if (area == null) {
                throw new RuntimeException("u7ba1u8f96u533au57dfu4e0du5b58u5728");
            }
        }
    }

    /**
     * u6279u91cfu5220u9664u6284u8868u624bu518c
     *
     * @param ids     u9700u8981u5220u9664u7684u6284u8868u624bu518cu4e3bu952eu96c6u5408
     * @param isValid u662fu5426u6821u9a8c,true-u5220u9664u524du6821u9a8c,false-u4e0du6821u9a8c
     * @return u7ed3u679c
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // u8fdbu884cu4e1au52a1u6821u9a8c,u4f8bu5982u662fu5426u5b58u5728u5173u8054u6570u636e
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据多个表册ID查询表册信息
     *
     * @param bookIds 表册ID集合
     * @return 表册信息集合
     */
    @Override
    public List<MeterBookVo> queryByIds(Collection<Long> bookIds) {
        if (bookIds == null || bookIds.isEmpty()) {
            return new ArrayList<>();
        }
        List<MeterBookVo> list = baseMapper.selectVoList(
            new LambdaQueryWrapper<MeterBook>()
                .in(MeterBook::getId, bookIds)
                .eq(MeterBook::getDelFlag, "0")
        );


        return list;
    }
}
