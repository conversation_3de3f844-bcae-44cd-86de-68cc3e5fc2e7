package org.dromara.waterfee.user.domain.bo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.List;

/**
 * 批量销户用水用户业务对象
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WaterfeeUserBatchCancellationBo extends BaseEntity {

    /**
     * 用户ID列表
     */
    @NotEmpty(message = "用户ID列表不能为空", groups = {AddGroup.class, EditGroup.class})
    private List<Long> userIds;

    /**
     * 销户原因
     */
    @NotNull(message = "销户原因不能为空", groups = {AddGroup.class, EditGroup.class})
    private String cancellationReason;
}