package org.dromara.waterfee.pay.domain;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class WechatPrePayParam {

    /**
     * 商品信息描述，用户微信账单的商品字段中可见
     */
    @NotBlank(message = "商品描述不能为空")
    private String description;

    /**
     * 商户系统内部订单号
     */
//    @NotBlank(message = "商户订单号不能为空")
//    private String outTradeNo;

    /**
     * 支付结束时间 选填
     */
    private String timeExpire;

    /**
     * 电子发票入口开放标识 选填
     */
    private boolean supportFaPiao;

    /**
     * 订单总金额，单位为分 1元应填写 100
     */
    @NotBlank(message = "订单总金额不能为空")
    private Integer amount;

    /**
     * 用户标识
     */
    @NotBlank(message = "openid不能为空")
    private String openid;

    /**
     * 户号
     */
    @NotBlank(message = "户号不能为空")
    private String userNo;

    /**
     * 是否支付账单
     */
    private Integer ifPayBill;

    /**
     * 账单ID （非预存充值）销账时需要填写
     */
    private Long billId;
}
