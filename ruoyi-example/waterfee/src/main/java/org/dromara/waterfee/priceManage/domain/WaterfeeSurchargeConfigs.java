package org.dromara.waterfee.priceManage.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 附加费配置 (Surcharge Configurations)对象 waterfee_surcharge_configs
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_surcharge_configs")
public class WaterfeeSurchargeConfigs extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 附加费名称
     */
    private String name;

    /**
     * 计算方式 (e.g., fixed_amount, meter_reading)
     */
    private String calculationMethod;

    /**
     * 固定金额 (if calculation_method is fixed_amount)
     */
    private Long fixedAmount;

    /**
     * 比例(%) (e.g., if calculation_method is meter_reading)
     */
    private Long ratePercent;

    /**
     * 附加费类别
     */
    private String category;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
