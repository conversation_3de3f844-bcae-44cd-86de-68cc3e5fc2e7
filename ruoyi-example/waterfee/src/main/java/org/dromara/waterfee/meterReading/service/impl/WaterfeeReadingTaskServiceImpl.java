package org.dromara.waterfee.meterReading.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.area.service.IWaterfeeAreaService;
import org.dromara.waterfee.meter.domain.WaterfeeMeter;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meter.mapper.WaterfeeMeterMapper;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.domain.WaterfeeReadingTask;
import org.dromara.waterfee.meterReading.domain.bo.WaterfeeReadingTaskBo;
import org.dromara.waterfee.meterReading.domain.vo.WaterfeeReadingTaskVo;
import org.dromara.waterfee.meterReading.mapper.WaterfeeReadingTaskMapper;
import org.dromara.waterfee.meterReading.service.IMeterReadingCommonService;
import org.dromara.waterfee.meterReading.service.IWaterfeeReadingTaskService;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.mapper.WaterfeeUserMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 抄表任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-21
 */
@Slf4j
@Service
public class WaterfeeReadingTaskServiceImpl extends ServiceImpl<WaterfeeReadingTaskMapper, WaterfeeReadingTask> implements IWaterfeeReadingTaskService {

    private final IWaterfeeAreaService waterfeeAreaService;
    private final WaterfeeMeterMapper waterfeeMeterMapper;
    private final WaterfeeUserMapper waterfeeUserMapper;
    private final IMeterReadingCommonService meterReadingCommonService;
    private final IWaterfeeMeterService waterfeeMeterService;

    public WaterfeeReadingTaskServiceImpl(IWaterfeeAreaService waterfeeAreaService,
                                          WaterfeeMeterMapper waterfeeMeterMapper,
                                          WaterfeeUserMapper waterfeeUserMapper,
                                          IMeterReadingCommonService meterReadingCommonService,
                                          IWaterfeeMeterService waterfeeMeterService) {
        this.waterfeeAreaService = waterfeeAreaService;
        this.waterfeeMeterMapper = waterfeeMeterMapper;
        this.waterfeeUserMapper = waterfeeUserMapper;
        this.meterReadingCommonService = meterReadingCommonService;
        this.waterfeeMeterService = waterfeeMeterService;
    }

    /**
     * 查询抄表任务
     *
     * @param taskId 主键
     * @return 抄表任务
     */
    @Override
    public WaterfeeReadingTaskVo queryById(Long taskId) {
        return baseMapper.selectTaskVoById(taskId);
    }

    /**
     * 查询抄表任务列表
     *
     * @param bo 抄表任务
     * @return 抄表任务
     */
    @Override
    public TableDataInfo<WaterfeeReadingTaskVo> queryPageList(WaterfeeReadingTaskBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeReadingTask> lqw = buildQueryWrapper(bo);
        Page<WaterfeeReadingTaskVo> result = baseMapper.selectTaskVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询抄表任务列表
     *
     * @param bo 抄表任务
     * @return 抄表任务
     */
    @Override
    public List<WaterfeeReadingTaskVo> queryList(WaterfeeReadingTaskBo bo) {
        LambdaQueryWrapper<WaterfeeReadingTask> lqw = buildQueryWrapper(bo);
        return baseMapper.selectTaskVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeReadingTask> buildQueryWrapper(WaterfeeReadingTaskBo bo) {

        LambdaQueryWrapper<WaterfeeReadingTask> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTaskName()), WaterfeeReadingTask::getTaskName, bo.getTaskName());
        lqw.eq(bo.getBusinessAreaId() != null, WaterfeeReadingTask::getBusinessAreaId, bo.getBusinessAreaId());
        lqw.eq(bo.getMeterBookId() != null, WaterfeeReadingTask::getMeterBookId, bo.getMeterBookId());
        lqw.eq(bo.getReaderId() != null, WaterfeeReadingTask::getReaderId, bo.getReaderId());
        lqw.like(StringUtils.isNotBlank(bo.getReaderName()), WaterfeeReadingTask::getReaderName, bo.getReaderName());
        lqw.eq(StringUtils.isNotBlank(bo.getReadingMethod()), WaterfeeReadingTask::getReadingMethod, bo.getReadingMethod());
        lqw.eq(StringUtils.isNotBlank(bo.getReadingCycle()), WaterfeeReadingTask::getReadingCycle, bo.getReadingCycle());
        lqw.eq(StringUtils.isNotBlank(bo.getIsCycle()), WaterfeeReadingTask::getIsCycle, bo.getIsCycle());
        lqw.eq(StringUtils.isNotBlank(bo.getTaskStatus()), WaterfeeReadingTask::getTaskStatus, bo.getTaskStatus());
        lqw.apply("t.del_flag = {0}", "0"); // 逻辑删除标志
        return lqw;
    }

    /**
     * 新增抄表任务
     *
     * @param bo 抄表任务
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(WaterfeeReadingTaskBo bo) {
        WaterfeeReadingTask add = BeanUtil.toBean(bo, WaterfeeReadingTask.class);
        // 设置默认值
        if (StringUtils.isBlank(add.getTaskStatus())) {
            add.setTaskStatus("1"); // 默认正常
        }

        // 检查表册ID是否已经被其他任务使用
        if (add.getMeterBookId() != null) {
            checkMeterBookIdUnique(add.getMeterBookId(), null);

            // 计算表册关联的水表数量和用户数量
            Map<String, Integer> countMap = calculateMeterAndUserCount(add.getMeterBookId());
            add.setPlanReadingNum(countMap.get("meterCount"));
            add.setBookUserNum(countMap.get("userCount"));
        }

        // 计算下次执行时间
        if (add.getStartDate() != null) {
            Date nextExecuteTime = calculateNextExecuteTime(add);
            add.setNextExecuteTime(nextExecuteTime);
        }

        // 设置抄表月份（使用当前月份）
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        add.setReadingMonth(sdf.format(new Date()));

        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setTaskId(add.getTaskId());
        }
        return flag;
    }

    /**
     * 修改抄表任务
     *
     * @param bo 抄表任务
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(WaterfeeReadingTaskBo bo) {
        WaterfeeReadingTask update = BeanUtil.toBean(bo, WaterfeeReadingTask.class);

        // 检查表册ID是否已经被其他任务使用
        if (update.getMeterBookId() != null) {
            checkMeterBookIdUnique(update.getMeterBookId(), update.getTaskId());

            // 计算表册关联的水表数量和用户数量
            Map<String, Integer> countMap = calculateMeterAndUserCount(update.getMeterBookId());
            update.setPlanReadingNum(countMap.get("meterCount"));
            update.setBookUserNum(countMap.get("userCount"));
        }

        // 计算下次执行时间
        if (update.getStartDate() != null) {
            Date nextExecuteTime = calculateNextExecuteTime(update);
            update.setNextExecuteTime(nextExecuteTime);
        }

        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(WaterfeeReadingTask entity) {
        // 校验抄表例日（具体安排哪一天进行抄表，通常是设置成每个月固定的一天，比如每月5号）
        if (entity.getReadingDay() != null) {
            if (entity.getReadingDay() < 1 || entity.getReadingDay() > 31) {
                throw new RuntimeException("抄表例日必须在1-31之间");
            }

            // 根据抄表周期进行额外验证
            if ("month_1".equals(entity.getReadingCycle()) || "month_2".equals(entity.getReadingCycle())) {
                // 对于月度抄表，例日应该是月1-28日，避免月底问题
                if (entity.getReadingDay() > 28) {
                    throw new RuntimeException("月度抄表的例日建议设置在1-28之间，避免因月底天数不同导致的问题");
                }
            }
        }

        // 校验抄表基准日（系统计算账单的基准日期，一般也设置成每月的某一天，比如20号）
        if (entity.getBaseDay() != null) {
            if (entity.getBaseDay() < 1 || entity.getBaseDay() > 31) {
                throw new RuntimeException("抄表基准日必须在1-31之间");
            }

            // 基准日应该在例日之后，以确保有足够的时间处理抄表数据
            if (entity.getReadingDay() != null && entity.getBaseDay() <= entity.getReadingDay()) {
                throw new RuntimeException("抄表基准日应该在抄表例日之后，以确保有足够的时间处理抄表数据");
            }
        }
    }

    /**
     * 批量删除抄表任务
     *
     * @param ids 需要删除的抄表任务主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验是否允许删除
            for (Long id : ids) {
                WaterfeeReadingTask task = baseMapper.selectTaskById(id);
                if (task != null && "1".equals(task.getTaskStatus())) {
                    throw new RuntimeException("正常状态的任务不能删除");
                }
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 暂停抄表任务
     * 对已设置自动循环的任务可终止操作，对已生成的任务不影响，但是影响下一次
     *
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean pauseTask(Long taskId) {
        WaterfeeReadingTask task = baseMapper.selectTaskById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }

        // 只有正常状态的任务可以暂停
        if (!"1".equals(task.getTaskStatus())) {
            throw new RuntimeException("只有正常状态的任务可以暂停");
        }

        // 设置任务状态为暂停
        task.setTaskStatus("0"); // 暂停

        // 如果是自动循环任务，清除下次执行时间，以影响下一次执行
        if ("1".equals(task.getIsCycle())) {
            task.setNextExecuteTime(null);
        }

        return baseMapper.updateById(task) > 0;
    }

    /**
     * 启用抄表任务
     *
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean enableTask(Long taskId) {
        WaterfeeReadingTask task = baseMapper.selectTaskById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }

        // 只有暂停状态的任务可以启用
        if (!"0".equals(task.getTaskStatus())) {
            throw new RuntimeException("只有暂停状态的任务可以启用");
        }

        task.setTaskStatus("1"); // 正常

        // 重新计算下次执行时间
        Date nextExecuteTime = calculateNextExecuteTime(task);
        task.setNextExecuteTime(nextExecuteTime);

        return baseMapper.updateById(task) > 0;
    }

    /**
     * 下发抄表任务
     * 只能针对非自动循环设置的任务有效
     *
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean dispatchTask(Long taskId) {
        WaterfeeReadingTask task = baseMapper.selectTaskById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }

        // 只能针对非自动循环设置的任务有效
        if ("1".equals(task.getIsCycle())) {
            throw new RuntimeException("自动循环任务不能手动下发，系统会自动执行");
        }

        // 检查任务是否已经执行过
        if (task.getLastExecuteTime() != null) {
            // 检查是否已经有抄表记录
            int recordCount = meterReadingCommonService.countRecordsByTaskId(taskId);
            if (recordCount > 0) {
                throw new RuntimeException("该任务已经执行过，不能重复执行");
            }
        }

        // 只有正常状态的任务可以下发
        if (!"1".equals(task.getTaskStatus())) {
            throw new RuntimeException("只有正常状态的任务可以下发");
        }

        // 获取表册ID
        Long meterBookId = task.getMeterBookId();
        if (meterBookId == null) {
            throw new RuntimeException("任务未关联抄表手册");
        }

        // 获取表册中的所有水表编号
        List<String> meterNos = waterfeeMeterMapper.selectMeterNosByBookId(meterBookId);
        if (meterNos.isEmpty()) {
            throw new RuntimeException("抄表手册中没有水表");
        }

        log.info("开始下发抄表任务，任务ID: {}, 表册ID: {}, 水表数量: {}", taskId, meterBookId, meterNos.size());

        // 获取当前时间作为抄表时间
        Date now = new Date();
        int successCount = 0;
        int failCount = 0;

        // 为每个水表创建抄表记录
        for (String meterNo : meterNos) {
            try {
                // 查询水表信息
                WaterfeeMeterVo meter = waterfeeMeterService.queryByNo(meterNo);
                if (meter == null) {
                    log.warn("水表不存在，跳过创建抄表记录，水表编号: {}", meterNo);
                    failCount++;
                    continue;
                }

                // 查询该水表的最新抄表记录
                WaterfeeMeterReadingRecord latestRecord = meterReadingCommonService.selectByBookIdAndTaskId(meterBookId, taskId)
                    .stream()
                    .filter(record -> record.getMeterNo().equals(meterNo))
                    .findFirst()
                    .orElse(null);

                // 创建新的抄表记录
                WaterfeeMeterReadingRecord newRecord = new WaterfeeMeterReadingRecord();
                newRecord.setMeterNo(meterNo);
                newRecord.setMeterBookId(meter.getMeterBookId());

                // 设置水表类型
                if (meter.getMeterType() != null) {
                    newRecord.setMeterType(meter.getMeterType().toString());
                }

                // 设置上次抄表信息
                if (latestRecord != null) {
                    newRecord.setLastReading(latestRecord.getCurrentReading());
                    newRecord.setLastReadingTime(latestRecord.getReadingTime());
                } else {
                    // 如果没有上次抄表记录，使用水表的初始读数
                    if (meter.getInitialReading() != null) {
                        newRecord.setLastReading(meter.getInitialReading().doubleValue());
                    } else {
                        newRecord.setLastReading(0.0);
                    }
                    newRecord.setLastReadingTime(meter.getInstallDate());
                }

                // 本期抄表读数和水量暂时为空，等待实际抄表后填写
                newRecord.setCurrentReading(null);
                newRecord.setWaterUsage(null);
                newRecord.setOldMeterStopReading(0.0); // 默认旧表止数为0

                // 设置抄表任务ID和来源
                newRecord.setTaskId(taskId);
                newRecord.setSourceType("normal"); // 来源为正常抄表
                newRecord.setReadingTime(now); // 设置抄表时间为当前时间


                // 保存抄表记录
                boolean success = meterReadingCommonService.createMeterReadingRecord(newRecord);
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                    log.error("创建抄表记录失败，水表编号: {}", meterNo);
                }
            } catch (Exception e) {
                failCount++;
                log.error("创建抄表记录异常，水表编号: {}", meterNo, e);
            }
        }

        // 更新任务状态
        task.setTaskStatus("1"); // 保持任务状态为正常
        task.setLastExecuteTime(now); // 设置上次执行时间为当前时间

        boolean updateResult = baseMapper.updateById(task) > 0;
        log.info("抄表任务下发完成，任务ID: {}, 成功: {}, 失败: {}, 任务状态更新: {}",
            taskId, successCount, failCount, updateResult);

        return updateResult;
    }

    /**
     * 更新表册关联的用户数和水表数
     *
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateMeterAndUserCount(Long taskId) {
        WaterfeeReadingTask task = baseMapper.selectTaskById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }

        return updateTaskMeterAndUserCount(task);
    }

    /**
     * 批量更新表册关联的用户数和水表数
     *
     * @param taskIds 任务ID集合
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateMeterAndUserCountBatch(Collection<Long> taskIds) {
        if (taskIds == null || taskIds.isEmpty()) {
            return false;
        }

        boolean result = true;
        for (Long taskId : taskIds) {
            try {
                WaterfeeReadingTask task = baseMapper.selectTaskById(taskId);
                if (task != null) {
                    boolean success = updateTaskMeterAndUserCount(task);
                    if (!success) {
                        result = false;
                    }
                }
            } catch (Exception e) {
                log.error("更新任务ID为" + taskId + "的表册关联数据失败", e);
                result = false;
            }
        }

        return result;
    }

    /**
     * 更新任务的表册关联数据
     *
     * @param task 抄表任务
     * @return 结果
     */
    private Boolean updateTaskMeterAndUserCount(WaterfeeReadingTask task) {
        // 获取表册ID
        Long meterBookId = task.getMeterBookId();
        if (meterBookId == null) {
            throw new RuntimeException("表册ID不能为空");
        }

        // 计算表册关联的水表数量和用户数量
        Map<String, Integer> countMap = calculateMeterAndUserCount(meterBookId);

        // 更新任务信息
        task.setPlanReadingNum(countMap.get("meterCount"));
        task.setBookUserNum(countMap.get("userCount"));

        return baseMapper.updateById(task) > 0;
    }

    /**
     * 检查表册ID是否已经被其他任务使用
     *
     * @param meterBookId 表册ID
     * @param taskId      当前任务ID（修改时使用，新增时为null）
     */
    private void checkMeterBookIdUnique(Long meterBookId, Long taskId) {
        LambdaQueryWrapper<WaterfeeReadingTask> queryWrapper = Wrappers.lambdaQuery(WaterfeeReadingTask.class);
        queryWrapper.eq(WaterfeeReadingTask::getMeterBookId, meterBookId);
        queryWrapper.eq(WaterfeeReadingTask::getDelFlag, "0");
        queryWrapper.eq(WaterfeeReadingTask::getIsAudited, "0");

        // 如果是修改操作，排除当前任务
        if (taskId != null) {
            queryWrapper.ne(WaterfeeReadingTask::getTaskId, taskId);
        }

        // 查询是否存在使用相同表册ID的任务
        Long count = baseMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new RuntimeException("该表册存在未审核完成的抄表任务");
        }
    }

    /**
     * 计算表册关联的水表数量和用户数量
     *
     * @param meterBookId 表册ID
     * @return 水表数量和用户数量
     */
    private Map<String, Integer> calculateMeterAndUserCount(Long meterBookId) {
        Map<String, Integer> result = new HashMap<>();

        // 查询表册关联的水表数量
        LambdaQueryWrapper<WaterfeeMeter> meterQuery = Wrappers.lambdaQuery(WaterfeeMeter.class);
        meterQuery.eq(WaterfeeMeter::getMeterBookId, meterBookId);
        meterQuery.eq(WaterfeeMeter::getDelFlag, "0");
        Integer meterCount = Math.toIntExact(waterfeeMeterMapper.selectCount(meterQuery));
        result.put("meterCount", meterCount);

        // 查询表册关联的水表编号
        List<String> meterNos = waterfeeMeterMapper.selectMeterNosByBookId(meterBookId);

        // 查询这些水表关联的用户数量
        int userCount = 0;
        if (!meterNos.isEmpty()) {
            // 1. 从水表表中查询这些 meterNo 对应的用户ID
            List<Long> userIds = waterfeeMeterMapper.selectList(
                    Wrappers.lambdaQuery(WaterfeeMeter.class)
                        .in(WaterfeeMeter::getMeterNo, meterNos)
                        .select(WaterfeeMeter::getUserId)
                ).stream()
                .map(WaterfeeMeter::getUserId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

            // 2. 查询用户表中这些 ID 对应的数量（不包含逻辑删除）
            if (!userIds.isEmpty()) {
                LambdaQueryWrapper<WaterfeeUser> userQuery = Wrappers.lambdaQuery(WaterfeeUser.class)
                    .in(WaterfeeUser::getUserId, userIds)
                    .eq(WaterfeeUser::getDelFlag, "0");
                userCount = Math.toIntExact(waterfeeUserMapper.selectCount(userQuery));
            }
        }
        result.put("userCount", userCount);

        return result;
    }

    /**
     * 计算下次执行时间
     *
     * @param task 抄表任务
     * @return 下次执行时间
     */
    private Date calculateNextExecuteTime(WaterfeeReadingTask task) {
        Date startDate = task.getStartDate();
        if (startDate == null) {
            return null;
        }

        // 如果开始日期在未来，则下次执行时间为开始日期
        Date now = new Date();
        if (startDate.after(now)) {
            return startDate;
        }

        // 根据抄表周期计算下次执行时间
        String cycle = task.getReadingCycle();
        Integer readingDay = task.getReadingDay();

        // 如果不是循环任务，则没有下次执行时间
        if (!"1".equals(task.getIsCycle())) {
            return null;
        }

        // 如果没有设置抄表例日，则使用当前日期
        if (readingDay == null) {
            readingDay = 1; // 默认使用每月1号
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);

        // 先调整到当前月的抄表例日
        int currentDay = calendar.get(Calendar.DAY_OF_MONTH);
        if (currentDay >= readingDay) {
            // 如果当前日期已经过了抄表例日，则计算下一个周期的抄表日期
            switch (cycle) {
                case "month_1":
                    // 一月一抄，下次执行时间为下月的抄表例日
                    calendar.add(Calendar.MONTH, 1);
                    break;
                case "month_2":
                    // 二月一抄，下次执行时间为两个月后的抄表例日
                    calendar.add(Calendar.MONTH, 2);
                    break;
                case "half_year":
                    // 半年一抄，下次执行时间为半年后的抄表例日
                    calendar.add(Calendar.MONTH, 6);
                    break;
                case "year":
                    // 一年一抄，下次执行时间为一年后的抄表例日
                    calendar.add(Calendar.YEAR, 1);
                    break;
                default:
                    // 默认一月一抄
                    calendar.add(Calendar.MONTH, 1);
                    break;
            }
        }

        // 调整到抄表例日
        int maxDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        calendar.set(Calendar.DAY_OF_MONTH, Math.min(readingDay, maxDay));

        // 设置时间为当天的开始时间（00:00:00）
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }
}
