package org.dromara.waterfee.counter.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.enums.FormatsType;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.waterfee.bill.domain.PaymentDetail;
import org.dromara.waterfee.bill.domain.WaterfeeBill;
import org.dromara.waterfee.bill.domain.bo.WaterfeeBillBo;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.waterfee.bill.mapper.PaymentDetailMapper;
import org.dromara.waterfee.bill.mapper.WaterfeeBillMapper;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.dromara.waterfee.counter.domain.bo.*;
import org.dromara.waterfee.counter.domain.vo.WaterfeeUserInfoVo;
import org.dromara.waterfee.counter.service.IWaterfeeCounterPaymentService;
import org.dromara.waterfee.income.domain.bo.WaterfeeIncomeRecordBo;
import org.dromara.waterfee.pay.domain.WechatRefundParam;
import org.dromara.waterfee.pay.service.IWechatRefundService;
import org.dromara.waterfee.priceManage.service.WaterfeePriceCacheService;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.lock.UserLockManager;
import org.dromara.waterfee.user.mapper.WaterfeeUserMapper;
import org.dromara.waterfee.user.service.IWaterfeeUserBalanceChangeRecordService;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.dromara.waterfee.user.service.IWechatUserBindingService;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 窗口收费服务实现
 *
 * <AUTHOR> Li
 * @date 2025-05-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WaterfeeCounterPaymentServiceImpl implements IWaterfeeCounterPaymentService {

    private final WaterfeeUserMapper userMapper;
    private final WaterfeeBillMapper billMapper;
    private final PaymentDetailMapper paymentDetailMapper;
    // private final WaterfeePaymentMapper paymentMapper;
    // private final WaterfeeMeterReadingRecordMapper meterReadingMapper;
    private final IWaterfeeBillService billService;
    private final WaterfeePriceCacheService priceCacheService;
    private final UserLockManager userLockManager;
    private final IWaterfeeUserService userService;
    private final IWaterfeeUserBalanceChangeRecordService balanceChangeRecordService;
    private final IWechatRefundService wechatRefundService;
    private final IWechatUserBindingService wechatUserBindingService;

    @Override
    public WaterfeeUserInfoVo getUserByKeyword(String keyword) {
        return null;
    }

    @Override
    public WaterfeeUser getUserById(Long userId) {
        WaterfeeUser waterfeeUser = userMapper.selectById(userId);
        return waterfeeUser;
    }

    /**
     * 根据关键字查询用户信息
     *
     * @param keyword 用户编号或姓名
     * @return 用户信息
     */
    @Override
    public List<WaterfeeUser> searchUsersByKeyword(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            throw new ServiceException("查询关键字不能为空");
        }

        // 查询用户信息
        List<WaterfeeUser> users = userMapper.selectUserByUserNameNu(keyword);

        if (CollectionUtil.isEmpty(users)) {
            return Collections.emptyList();
        }

        return users;
    }

    /**
     * 获取用户账单列表
     *
     * @return 账单列表
     */
    @Override
    public List<WaterfeeBillVo> getUserBills(Long userId, String status, String month) {
        if (null == userId) {
            throw new ServiceException("用户不能为空");
        }

        return billMapper.selectVoListByUserId(userId, status, month);
    }

    /**
     * 获取用户缴费明细
     *
     * @param userId 用户ID
     * @return 缴费明细列表
     */
    @Override
    public List<PaymentDetail> getUserPaymentDetails(Long userId) {
        if (null == userId) {
            throw new ServiceException("用户ID不能为空");
        }

        // 查询用户的所有缴费明细
        LambdaQueryWrapper<PaymentDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentDetail::getUserId, userId);
        queryWrapper.orderByDesc(PaymentDetail::getPaymentTime);

        List<PaymentDetail> paymentDetails = paymentDetailMapper.selectList(queryWrapper);

        // 转换为VO对象
        return paymentDetails;
    }

    /**
     * 根据用户ID和账单月份获取缴费明细
     *
     * @param userId    用户ID
     * @param billMonth 账单月份
     * @return 缴费明细列表
     */
    @Override
    public List<PaymentDetail> getUserPaymentDetailsByMonth(Long userId, String billMonth) {
        if (null == userId) {
            throw new ServiceException("用户ID不能为空");
        }
        if (StringUtils.isBlank(billMonth)) {
            throw new ServiceException("账单月份不能为空");
        }

        // 查询用户指定月份的缴费明细
        LambdaQueryWrapper<PaymentDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentDetail::getUserId, userId);
        queryWrapper.eq(PaymentDetail::getBillMonth, billMonth);
        queryWrapper.orderByDesc(PaymentDetail::getPaymentTime);

        List<PaymentDetail> paymentDetails = paymentDetailMapper.selectList(queryWrapper);

        // 转换为VO对象
        return paymentDetails;
    }

    // /**
    // * 获取用户抄表记录
    // *
    // * @param userId 用户编号
    // * @return 抄表记录列表
    // */
    // @Override
    // public List<WaterfeeMeterReadingVo> getUserMeterReadings(Long userId) {
    // if (null == userId) {
    // throw new ServiceException("用户编号不能为空");
    // }
    //
    // return meterReadingMapper.selectReadingsByUserNo(userNo);
    // return null;
    // }

    /**
     * 缴费
     *
     * @param bo 缴费数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean payBills(WaterfeePayBillsBo bo) {
        if (bo.getBillIds() == null || bo.getBillIds().isEmpty()) {
            throw new ServiceException("账单ID不能为空");
        }

        if (bo.getAmount() == null || bo.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("支付金额必须>=0");
        }

        // 获取账单信息
        List<WaterfeeBillVo> bills = new ArrayList<>();
        for (Long billId : bo.getBillIds()) {
            WaterfeeBillVo bill = billMapper.selectVoById(billId);
            if (bill == null) {
                throw new ServiceException("账单不存在：" + billId);
            }
            if (!"ISSUED".equals(bill.getBillStatus())) {
                throw new ServiceException("账单状态不正确，无法支付：" + bill.getBillNumber());
            }
            bills.add(bill);
        }

        // 计算总应付金额
        BigDecimal totalDue = bills.stream()
            .map(bill -> bill.getBalanceDue() != null ? bill.getBalanceDue() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 验证支付金额
        if (bo.getAmount().compareTo(totalDue) > 0) {
            throw new ServiceException("支付金额不能大于应付总额");
        }

        // 如果是预存款支付，检查余额并扣款
        if ("DEPOSIT".equals(bo.getPaymentMethod())) {
            Long userId = bills.get(0).getCustomerId();
            if (userId == null) {
                throw new ServiceException("账单未关联用户ID，无法使用预存款支付");
            }

            // 查询用户信息
            WaterfeeUser user = userMapper.selectById(userId);
            if (user == null) {
                throw new ServiceException("用户不存在：" + userId);
            }

            String userNo = user.getUserNo();
            if (StringUtils.isBlank(userNo)) {
                throw new ServiceException("用户户号为空，无法使用预存款支付");
            }

            // 获取用户锁
            ReentrantLock lock = userLockManager.getUserLock(userNo);
            try {
                // 尝试获取锁，最多等待3秒
                if (lock.tryLock(3, TimeUnit.SECONDS)) {
                    try {
                        // 重新查询用户信息（确保获取最新余额）
                        user = userMapper.selectById(userId);
                        if (user == null) {
                            throw new ServiceException("用户不存在：" + userId);
                        }

                        // 使用IWaterfeeUserService获取余额
                        BigDecimal balance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
                        if (balance.compareTo(bo.getAmount()) < 0) {
                            throw new ServiceException("预存款余额不足，当前余额：" + balance + "元，需支付：" + bo.getAmount() + "元");
                        }

                        // 直接扣减用户余额
                        BigDecimal newBalance = balance.subtract(bo.getAmount());

                        WaterfeeUser updateUser = new WaterfeeUser();
                        updateUser.setUserId(userId);
                        updateUser.setBalance(newBalance);
                        updateUser.setUpdateBy(LoginHelper.getUserId());
                        updateUser.setUpdateTime(DateUtils.getNowDate());
                        int rows = userMapper.updateById(updateUser);

                        if (rows <= 0) {
                            throw new ServiceException("更新用户余额失败");
                        }

                        log.info("用户余额更新成功，户号：{}，当前余额：{}，支付金额：{}，更新后余额：{}",
                            userNo, balance, bo.getAmount(), newBalance);

                        // 记录余额变更
                        balanceChangeRecordService.recordBalanceChange(
                            userId, userNo, "PAYMENT",
                            balance, bo.getAmount().negate(), newBalance,
                            bills.stream().map(b -> b.getBillId().toString()).collect(Collectors.joining(",")),
                            "BILL", "账单支付", LoginHelper.getUsername(),
                            "使用预存款支付账单：" + bills.get(0).getBillNumber() + " 等" + bills.size() + "个账单"
                        );
                    } finally {
                        // 释放锁
                        lock.unlock();
                    }
                } else {
                    log.warn("获取用户余额锁超时，户号：{}", userNo);
                    throw new ServiceException("系统繁忙，请稍后再试");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("支付时被中断，户号：{}", userNo, e);
                throw new ServiceException("支付操作被中断");
            }

            String billNumbers = bills.stream()
                .map(WaterfeeBillVo::getBillNumber)
                .collect(Collectors.joining(", "));

            bo.setRemark("使用预存款支付：" + (bo.getRemark() != null ? bo.getRemark() : "支付账单：" + billNumbers));

        }

        // 分配支付金额到各个账单
        BigDecimal remainingAmount = bo.getAmount();
        for (WaterfeeBillVo bill : bills) {
            if (remainingAmount.compareTo(BigDecimal.ZERO) < 0) {
                break;
            }

            BigDecimal billDue = bill.getBalanceDue() != null ? bill.getBalanceDue() : BigDecimal.ZERO;
            BigDecimal payAmount = remainingAmount.compareTo(billDue) >= 0 ? billDue : remainingAmount;

            // 调用账单支付接口
            billService.payBill(bill.getBillId(), payAmount);
            // 预存款支付不创建交易记录
            if(!"DEPOSIT".equals(bo.getPaymentMethod())) {
                // 创建支付明细记录（包含income信息）
                PaymentDetail paymentDetail = new PaymentDetail();
                paymentDetail.setPaymentDetailId(generatePaymentNumber());
                paymentDetail.setBillId(bill.getBillId().toString());
                paymentDetail.setUserId(bill.getCustomerId());
                paymentDetail.setBillMonth(bill.getBillMonth());
                paymentDetail.setPaymentAmount(payAmount);
                paymentDetail.setPaymentTime(DateUtils.getNowDate());
                paymentDetail.setPaymentMethod(bo.getPaymentMethod());
                paymentDetail.setPaymentStatus("SUCCESS");
                paymentDetail.setRemark(bo.getRemark());

                // 设置income相关字段
                paymentDetail.setIncomeType("bill_payment");
                paymentDetail.setTollCollector(LoginHelper.getUsername());
                paymentDetail.setIncomeTime(DateUtils.getNowDate());

                paymentDetailMapper.insert(paymentDetail);
            }
            remainingAmount = remainingAmount.subtract(payAmount);
        }

        return true;
    }

    /**
     * 检查用户余额是否足够支付账单,并更新用户余额
     *
     * @param bo
     * @param balance
     * @param userId
     * @return
     */
    @NotNull
    private static WaterfeeUser checkUserBalanceAndPay(WaterfeePayBillsBo bo, BigDecimal balance, Long userId) {
        if (balance.compareTo(bo.getAmount()) < 0) {
            throw new ServiceException("预存款余额不足，当前余额：" + balance + "元，需支付：" + bo.getAmount() + "元");
        }

        // 直接扣减用户余额
        BigDecimal newBalance = balance.subtract(bo.getAmount());

        WaterfeeUser updateUser = new WaterfeeUser();
        updateUser.setUserId(userId);
        updateUser.setBalance(newBalance);
        updateUser.setUpdateBy(LoginHelper.getUserId());
        updateUser.setUpdateTime(DateUtils.getNowDate());
        return updateUser;
    }

    /**
     * 获取水费收入记录业务对象
     *
     * @param bo        缴费业务对象
     * @param bill      账单信息
     * @param payAmount 支付金额
     * @return 水费收入记录业务对象
     */
    @NotNull
    private static WaterfeeIncomeRecordBo getWaterfeeIncomeRecordBo(WaterfeePayBillsBo bo, WaterfeeBillVo bill,
                                                                    BigDecimal payAmount) {
        WaterfeeIncomeRecordBo incomeRecordBo = new WaterfeeIncomeRecordBo();

        incomeRecordBo.setUserNo(bill.getUserNo());
        String incomeType = switch (bo.getPaymentMethod()) {
            case "WECHAT_PAY" -> "wechat";
            case "ALIPAY" -> "alipay";
            case "CASH" -> "cash";
            case "BANK_TRANSFER" -> "other";
            case "DEPOSIT" -> "deposit";
            default -> "other";
        };
        incomeRecordBo.setPayMethod(incomeType);
        incomeRecordBo.setCollectorName(LoginHelper.getUsername());
        incomeRecordBo.setIncomeTime(DateUtils.getNowDate());
        incomeRecordBo.setAmount(payAmount);
        incomeRecordBo.setRemark(bo.getRemark());
        incomeRecordBo.setCreateBy(LoginHelper.getUserId());
        incomeRecordBo.setCreateTime(DateUtils.getNowDate());
        incomeRecordBo.setCreateDept(LoginHelper.getDeptId());
        return incomeRecordBo;
    }

    /**
     * 获取水费收入记录业务对象
     *
     * @param bo        缴费业务对象
     * @param bill      账单信息
     * @param payAmount 支付金额
     * @return 水费收入记录业务对象
     */
    @NotNull
    private static WaterfeeIncomeRecordBo getWaterfeeIncomeRecordBo2(WaterfeeRefundBo bo, WaterfeeBillVo bill,
                                                                     BigDecimal payAmount) {
        WaterfeeIncomeRecordBo incomeRecordBo = new WaterfeeIncomeRecordBo();

        incomeRecordBo.setUserNo(bill.getUserNo());
        String incomeType = switch (bo.getRefundMethod()) {
            case "WECHAT_PAY" -> "wechat";
            case "ALIPAY" -> "alipay";
            case "CASH" -> "cash";
            case "BANK_TRANSFER" -> "other";
            case "DEPOSIT" -> "deposit";
            default -> "other";
        };
        incomeRecordBo.setPayMethod(incomeType);
        incomeRecordBo.setCollectorName(LoginHelper.getUsername());
        incomeRecordBo.setIncomeTime(DateUtils.getNowDate());
        incomeRecordBo.setAmount(payAmount);
        incomeRecordBo.setRemark(bo.getRefundReason());
        incomeRecordBo.setCreateBy(LoginHelper.getUserId());
        incomeRecordBo.setCreateTime(DateUtils.getNowDate());
        incomeRecordBo.setCreateDept(LoginHelper.getDeptId());
        return incomeRecordBo;
    }

    /**
     * 预存充值
     *
     * @param bo 充值数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addDeposit(WaterfeeDepositBo bo) {
        if (bo.getUserId() == null) {
            throw new ServiceException("用户ID不能为空");
        }

        if (bo.getAmount() == null || bo.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("充值金额必须大于0");
        }

        // 查询用户信息
        WaterfeeUser user = userMapper.selectById(bo.getUserId());
        if (user == null) {
            throw new ServiceException("用户不存在：" + bo.getUserId());
        }

        String userNo = user.getUserNo();
        if (StringUtils.isBlank(userNo)) {
            throw new ServiceException("用户户号为空，无法充值");
        }

        // 获取用户锁
        ReentrantLock lock = userLockManager.getUserLock(userNo);
        try {
            // 尝试获取锁，最多等待3秒
            if (lock.tryLock(3, TimeUnit.SECONDS)) {
                try {
                    // 重新查询用户信息（确保获取最新余额）
                    user = userMapper.selectById(bo.getUserId());
                    if (user == null) {
                        throw new ServiceException("用户不存在：" + bo.getUserId());
                    }

                    // 直接更新用户余额
                    BigDecimal currentBalance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
                    BigDecimal newBalance = currentBalance.add(bo.getAmount());

                    WaterfeeUser updateUser = new WaterfeeUser();
                    updateUser.setUserId(bo.getUserId());
                    updateUser.setBalance(newBalance);
                    updateUser.setUpdateBy(LoginHelper.getUserId());
                    updateUser.setUpdateTime(DateUtils.getNowDate());
                    int rows = userMapper.updateById(updateUser);

                    if (rows <= 0) {
                        throw new ServiceException("更新用户余额失败");
                    }

                    log.info("用户余额更新成功，户号：{}，当前余额：{}，充值金额：{}，更新后余额：{}",
                        userNo, currentBalance, bo.getAmount(), newBalance);

                    // 创建充值记录
                    PaymentDetail paymentDetail = new PaymentDetail();
                    paymentDetail.setPaymentDetailId(generatePaymentNumber());
                    paymentDetail.setUserId(bo.getUserId());
                    paymentDetail.setPaymentAmount(bo.getAmount());
                    paymentDetail.setPaymentTime(DateUtils.getNowDate());
                    paymentDetail.setPaymentMethod(bo.getPaymentMethod());
                    paymentDetail.setPaymentStatus("SUCCESS");
                    paymentDetail.setRemark("预存充值：" + (bo.getRemark() != null ? bo.getRemark() : ""));

                    // 设置income相关字段
                    paymentDetail.setIncomeType("prestore");
                    paymentDetail.setTollCollector(bo.getTollCollector() != null ? bo.getTollCollector() : LoginHelper.getUsername());
                    paymentDetail.setIncomeTime(DateUtils.getNowDate());

                    paymentDetailMapper.insert(paymentDetail);

                    // 记录余额变更
                    balanceChangeRecordService.recordBalanceChange(
                        bo.getUserId(), userNo, "RECHARGE",
                        currentBalance, bo.getAmount(), newBalance,
                        paymentDetail.getPaymentDetailId(), "DEPOSIT", "预存充值",
                        bo.getTollCollector() != null ? bo.getTollCollector() : LoginHelper.getUsername(),
                        "预存充值：" + (bo.getRemark() != null ? bo.getRemark() : "")
                    );

                    return true;
                } finally {
                    // 释放锁
                    lock.unlock();
                }
            } else {
                log.warn("获取用户余额锁超时，户号：{}", userNo);
                throw new ServiceException("系统繁忙，请稍后再试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("充值时被中断，户号：{}", userNo, e);
            throw new ServiceException("充值操作被中断");
        } catch (Exception e) {
            if (!(e instanceof ServiceException)) {
                log.error("预存充值失败，户号：{}", userNo, e);
                throw new ServiceException("预存充值失败：" + e.getMessage());
            }
            throw e;
        }
    }

    /**
     * 调整账单
     *
     * @param bo 调整数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean adjustConsumption(WaterfeeAdjustConsumptionBo bo) {
        if (bo.getBillId() == null) {
            throw new ServiceException("账单ID不能为空");
        }

        if (StringUtils.isBlank(bo.getAdjustmentReason())) {
            throw new ServiceException("调整原因不能为空");
        }

        // 查询账单信息
        WaterfeeBillVo bill = billMapper.selectVoById(bo.getBillId());
        if (bill == null) {
            throw new ServiceException("账单不存在：" + bo.getBillId());
        }

        // 允许调整草稿和已发布状态的账单
        if (!"DRAFT".equals(bill.getBillStatus()) && !"ISSUED".equals(bill.getBillStatus())) {
            throw new ServiceException("只能调整草稿或已发布状态的账单");
        }

        // 获取用户信息，确定价格类型
        WaterfeeUser user = userMapper.selectById(bill.getCustomerId());
        if (user == null) {
            throw new ServiceException("用户信息不存在");
        }

        // 创建账单业务对象，用于调整
        WaterfeeBillBo billBo = new WaterfeeBillBo();
        billBo.setBillId(bill.getBillId());
        billBo.setAdjustmentReason(bo.getAdjustmentReason());

        // 构建调整说明
        StringBuilder adjustmentNoteBuilder = new StringBuilder();

        // 处理读数调整（会自动计算用量）
        if (bo.getAdjustmentReading() != null) {
            BigDecimal previousReading = bill.getPreviousReadingValue();
            BigDecimal currentReading = bill.getCurrentReadingValue();
            BigDecimal adjustedReading = bo.getAdjustmentReading();

            // 检查调整后的读数是否大于等于上期读数
            if (adjustedReading.compareTo(previousReading) < 0) {
                throw new ServiceException("调整后读数不能小于上期读数: " + previousReading);
            }

            // 计算新的用水量
            BigDecimal newConsumption = adjustedReading.subtract(previousReading);

            // 设置调整后的读数和用量
            billBo.setCurrentReadingValue(adjustedReading);
            billBo.setConsumptionVolume(newConsumption);

            // 添加调整说明
            adjustmentNoteBuilder.append("读数调整: ").append(currentReading).append(" m³ -> ").append(adjustedReading)
                .append(" m³, ")
                .append("用量变更: ").append(bill.getConsumptionVolume()).append(" m³ -> ").append(newConsumption)
                .append(" m³");
        }


        // 如果没有任何调整项，抛出异常
        if (adjustmentNoteBuilder.length() == 0) {
            throw new ServiceException("未提供任何调整项");
        }

        // 添加调整原因
        adjustmentNoteBuilder.append(", 原因: ").append(bo.getAdjustmentReason());
        String adjustmentNote = adjustmentNoteBuilder.toString();

        // 设置调整备注
        billBo.setNotes(adjustmentNote);

        // 调用账单服务进行调整
        Boolean result = billService.adjustConsumption(billBo);

        if (result) {
            // 记录操作日志
            log.info("用户 {} 的账单 {} 调整成功: {}",
                user.getUserName(), bill.getBillNumber(), adjustmentNote);
        }

        return result;
    }

    /**
     * 账单退费
     *
     * @param bo 退费数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean refundBill(WaterfeeRefundBo bo) {
        if (bo.getBillId() == null) {
            throw new ServiceException("账单ID不能为空");
        }

        if (bo.getRefundAmount() == null || bo.getRefundAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("退费金额必须大于0");
        }

        if (StringUtils.isBlank(bo.getRefundReason())) {
            throw new ServiceException("退费原因不能为空");
        }

        // 查询账单信息
        WaterfeeBillVo bill = billMapper.selectVoById(bo.getBillId());
        if (bill == null) {
            throw new ServiceException("账单不存在：" + bo.getBillId());
        }

        if (!"PAID".equals(bill.getBillStatus())) {
            throw new ServiceException("只能退费已支付的账单");
        }

        BigDecimal amountPaid = bill.getAmountPaid() != null ? bill.getAmountPaid() : BigDecimal.ZERO;

        // 查询该账单已退费金额
        LambdaQueryWrapper<PaymentDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentDetail::getBillId, bill.getBillId().toString());
        queryWrapper.eq(PaymentDetail::getIncomeType, "refund");
        List<PaymentDetail> refundRecords = paymentDetailMapper.selectList(queryWrapper);

        // 计算已退费总金额
        BigDecimal totalRefunded = refundRecords.stream()
            .map(PaymentDetail::getPaymentAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 检查退费金额是否超过可退费金额
        BigDecimal availableRefund = amountPaid.subtract(totalRefunded);
        if (bo.getRefundAmount().compareTo(availableRefund) > 0) {
            throw new ServiceException("退费金额不能超过可退费金额：" + availableRefund + "元");
        }

        // 如果是退款到预存款
        if ("DEPOSIT".equals(bo.getRefundMethod())) {
            Long userId = bill.getCustomerId();
            if (userId == null) {
                throw new ServiceException("账单未关联用户ID，无法退款到预存款");
            }

            // 查询用户信息
            WaterfeeUser user = userMapper.selectById(userId);
            if (user == null) {
                throw new ServiceException("用户不存在：" + userId);
            }

            String userNo = user.getUserNo();
            if (StringUtils.isBlank(userNo)) {
                throw new ServiceException("用户户号为空，无法退款到预存款");
            }

            // 获取用户锁
            ReentrantLock lock = userLockManager.getUserLock(userNo);
            try {
                // 尝试获取锁，最多等待3秒
                if (lock.tryLock(3, TimeUnit.SECONDS)) {
                    try {
                        // 重新查询用户信息（确保获取最新余额）
                        user = userMapper.selectById(userId);
                        if (user == null) {
                            throw new ServiceException("用户不存在：" + userId);
                        }

                        // 直接增加用户余额
                        BigDecimal currentBalance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
                        BigDecimal newBalance = currentBalance.add(bo.getRefundAmount());

                        WaterfeeUser updateUser = new WaterfeeUser();
                        updateUser.setUserId(userId);
                        updateUser.setBalance(newBalance);
                        updateUser.setUpdateBy(LoginHelper.getUserId());
                        updateUser.setUpdateTime(DateUtils.getNowDate());
                        int rows = userMapper.updateById(updateUser);

                        if (rows <= 0) {
                            throw new ServiceException("更新用户余额失败");
                        }

                        log.info("用户余额更新成功，户号：{}，当前余额：{}，退款金额：{}，更新后余额：{}",
                            userNo, currentBalance, bo.getRefundAmount(), newBalance);

                        // 记录余额变更
                        balanceChangeRecordService.recordBalanceChange(
                            userId, userNo, "REFUND",
                            currentBalance, bo.getRefundAmount(), newBalance,
                            bill.getBillId().toString(), "REFUND", "退款到预存款",
                            LoginHelper.getUsername(), "退费：" + bo.getRefundReason()
                        );
                    } finally {
                        // 释放锁
                        lock.unlock();
                    }
                } else {
                    log.warn("获取用户余额锁超时，户号：{}", userNo);
                    throw new ServiceException("系统繁忙，请稍后再试");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("退款时被中断，户号：{}", userNo, e);
                throw new ServiceException("退款操作被中断");
            }
        }

        // 创建退费记录
        PaymentDetail refundDetail = new PaymentDetail();
        refundDetail.setPaymentDetailId(generatePaymentNumber());
        refundDetail.setBillId(bill.getBillId().toString());
        refundDetail.setUserId(bill.getCustomerId());
        refundDetail.setBillMonth(bill.getBillMonth());
        refundDetail.setPaymentAmount(bo.getRefundAmount());
        refundDetail.setPaymentTime(DateUtils.getNowDate());
        refundDetail.setPaymentMethod(bo.getRefundMethod());
        refundDetail.setPaymentStatus("SUCCESS");
        refundDetail.setRemark("退费：" + bo.getRefundReason());

        // 设置income相关字段
        refundDetail.setIncomeType("refund");
        refundDetail.setTollCollector(LoginHelper.getUsername());
        refundDetail.setIncomeTime(DateUtils.getNowDate());

        paymentDetailMapper.insert(refundDetail);

        // 更新账单支付信息
        BigDecimal newAmountPaid = amountPaid.subtract(bo.getRefundAmount());
        BigDecimal newBalanceDue = bill.getTotalAmount().subtract(newAmountPaid);
        String newStatus = newAmountPaid.compareTo(BigDecimal.ZERO) == 0 ? "ISSUED" : "PARTIAL_PAID";

        WaterfeeBill updateBill = new WaterfeeBill();
        updateBill.setBillId(bill.getBillId());
        updateBill.setAmountPaid(newAmountPaid);
        updateBill.setBalanceDue(newBalanceDue);
        updateBill.setBillStatus(newStatus);
        updateBill.setUpdateBy(LoginHelper.getUserId());
        updateBill.setUpdateTime(DateUtils.getNowDate());
        billMapper.updateById(updateBill);

        return true;
    }

    /**
     * 计算账单调整结果（不保存数据）
     *
     * @param bo 调整数据
     * @return 计算结果
     */
    @Override
    public Map<String, Object> calculateAdjustment(WaterfeeAdjustConsumptionBo bo) {
        if (bo.getBillId() == null) {
            throw new ServiceException("账单ID不能为空");
        }

        // 查询账单信息
        WaterfeeBillVo bill = billMapper.selectVoById(bo.getBillId());
        if (bill == null) {
            throw new ServiceException("账单不存在：" + bo.getBillId());
        }

        // 获取用户信息，确定价格类型
        WaterfeeUser user = userMapper.selectById(bill.getCustomerId());
        if (user == null) {
            throw new ServiceException("用户信息不存在");
        }

        // 创建结果Map
        Map<String, Object> result = new HashMap<>();

        // 设置原始值
        result.put("originalConsumption", bill.getConsumptionVolume());
        result.put("originalPreviousReading", bill.getPreviousReadingValue());
        result.put("originalCurrentReading", bill.getCurrentReadingValue());
        result.put("originalBaseCharge", bill.getBaseChargeAmount());
        result.put("originalAdditionalCharge", bill.getAdditionalChargeAmount());
        result.put("originalSurcharge", bill.getSurchargeAmount());
        result.put("originalTotalAmount", bill.getTotalAmount());

        // 获取价格类型（标准价格或阶梯价格）
        String priceType = getPriceType(bill);

        // 设置价格类型
        result.put("priceType", priceType);

        // 设置原始阶梯水价相关字段
        if ("LADDER".equals(priceType)) {
            result.put("originalTier1", bill.getTier1());
            result.put("originalTier1Amount", bill.getTier1Amount());
            result.put("originalTier2", bill.getTier2());
            result.put("originalTier2Amount", bill.getTier2Amount());
            result.put("originalTier3", bill.getTier3());
            result.put("originalTier3Amount", bill.getTier3Amount());
        }

        // 创建一个临时账单对象，用于计算
        WaterfeeBill tempBill = new WaterfeeBill();
        tempBill.setBillId(bill.getBillId());
        tempBill.setPreviousReadingValue(bill.getPreviousReadingValue());
        tempBill.setCurrentReadingValue(bill.getCurrentReadingValue());
        tempBill.setConsumptionVolume(bill.getConsumptionVolume());
        tempBill.setBaseChargeAmount(bill.getBaseChargeAmount());
        tempBill.setTotalAmount(bill.getTotalAmount());
        tempBill.setPricePlanId(bill.getPricePlanId());


        // 处理读数调整（会自动计算用量）
        if (bo.getAdjustmentReading() != null) {
            BigDecimal previousReading = tempBill.getPreviousReadingValue();
            BigDecimal adjustedReading = bo.getAdjustmentReading();

            // 检查调整后的读数是否大于等于上期读数
            if (adjustedReading.compareTo(previousReading) < 0) {
                throw new ServiceException("调整后读数不能小于上期读数: " + previousReading);
            }

            // 计算新的用水量
            BigDecimal newConsumption = adjustedReading.subtract(previousReading);

            // 设置调整后的读数和用量
            tempBill.setCurrentReadingValue(adjustedReading);
            tempBill.setConsumptionVolume(newConsumption);

            // 根据价格类型重新计算费用
            if ("STANDARD".equals(priceType)) {
                recalculateStandardPrice(tempBill, newConsumption);
            } else if ("LADDER".equals(priceType)) {
                recalculateLadderPrice(tempBill, newConsumption);
            } else {
                // 默认简单计算
                BigDecimal originalVolume = bill.getConsumptionVolume();
                if (originalVolume.compareTo(BigDecimal.ZERO) > 0
                    && bill.getBaseChargeAmount().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal unitPrice = bill.getBaseChargeAmount().divide(originalVolume, 2,
                        BigDecimal.ROUND_HALF_UP);
                    tempBill.setBaseChargeAmount(unitPrice.multiply(newConsumption));

                    // 更新总金额
                    BigDecimal oldTotal = tempBill.getTotalAmount();
                    BigDecimal oldBaseCharge = bill.getBaseChargeAmount();
                    BigDecimal newBaseCharge = tempBill.getBaseChargeAmount();
                    BigDecimal newTotal = oldTotal.subtract(oldBaseCharge).add(newBaseCharge);
                    tempBill.setTotalAmount(newTotal);
                }
            }
        }

        // 设置计算结果
        result.put("adjustedConsumption", tempBill.getConsumptionVolume());
        result.put("adjustedCurrentReading", tempBill.getCurrentReadingValue());
        result.put("adjustedBaseCharge", tempBill.getBaseChargeAmount());
        result.put("adjustedTotalAmount", tempBill.getTotalAmount());

        // 打印调试信息
        log.info("计算调整结果 - adjustedConsumption: {}", tempBill.getConsumptionVolume());
        log.info("计算调整结果 - adjustedBaseCharge: {}", tempBill.getBaseChargeAmount());
        log.info("计算调整结果 - adjustedTotalAmount: {}", tempBill.getTotalAmount());

        return result;
    }

    /**
     * 获取价格类型（标准价格或阶梯价格）
     *
     * @param bill 账单信息
     * @return 价格类型
     */
    private String getPriceType(WaterfeeBillVo bill) {
        // 根据账单中的阶梯费用字段判断
        if (bill.getTier1Amount() != null && bill.getTier1Amount().compareTo(BigDecimal.ZERO) > 0) {
            return "LADDER";
        }

        // 根据价格方案ID判断
        Long pricePlanId = bill.getPricePlanId();
        if (pricePlanId != null) {
            try {
                // 从Redis缓存中获取价格方案类型
                String priceType = priceCacheService.getPriceType(pricePlanId);
                if (StringUtils.isNotEmpty(priceType)) {
                    return priceType;
                }
            } catch (Exception e) {
                log.error("从缓存获取价格类型失败", e);
            }
        }

        // 默认返回标准价格
        return "STANDARD";
    }

    /**
     * 根据标准价格重新计算费用
     *
     * @param bill           账单信息
     * @param adjustedVolume 调整后的用量
     */
    private void recalculateStandardPrice(WaterfeeBill bill, BigDecimal adjustedVolume) {
        // 获取价格方案ID
        Long pricePlanId = bill.getPricePlanId();
        if (pricePlanId == null) {
            log.warn("账单没有关联价格方案，使用简单计算方式");
            calculateBySimpleRatio(bill, adjustedVolume);
            return;
        }

        try {
            // 从数据库获取标准价格信息
            // 这里应该调用价格服务获取价格信息，但为了简化，我们使用账单中的信息
            BigDecimal unitPrice = getUnitPriceFromPricePlan(pricePlanId);
            if (unitPrice == null) {
                log.warn("无法获取价格方案的单价，使用简单计算方式");
                calculateBySimpleRatio(bill, adjustedVolume);
                return;
            }

            // 计算新的基础费用
            BigDecimal newBaseCharge = unitPrice.multiply(adjustedVolume).setScale(2, BigDecimal.ROUND_HALF_UP);

            // 更新基础费用
            BigDecimal originalBaseCharge = bill.getBaseChargeAmount();
            bill.setBaseChargeAmount(newBaseCharge);

            // 更新总金额
            BigDecimal oldTotal = bill.getTotalAmount();
            bill.setTotalAmount(newBaseCharge);
        } catch (Exception e) {
            log.error("计算标准价格失败", e);
            // 如果计算失败，使用简单比例计算
            calculateBySimpleRatio(bill, adjustedVolume);
        }
    }

    /**
     * 从价格方案获取单价
     *
     * @param pricePlanId 价格方案ID
     * @return 单价
     */
    private BigDecimal getUnitPriceFromPricePlan(Long pricePlanId) {
        try {
            // 从Redis缓存中获取标准价格方案
            org.dromara.waterfee.priceManage.domain.vo.WaterfeeStandardPriceVo priceVo = priceCacheService
                .getStandardPrice(pricePlanId);
            if (priceVo != null && priceVo.getPrice() != null) {
                return priceVo.getPrice();
            }
        } catch (Exception e) {
            log.error("从缓存获取标准价格方案失败", e);
        }

        // 如果获取失败，返回默认值
        return new BigDecimal("3.50");
    }

    /**
     * 使用简单比例计算费用
     *
     * @param bill           账单信息
     * @param adjustedVolume 调整后的用量
     */
    private void calculateBySimpleRatio(WaterfeeBill bill, BigDecimal adjustedVolume) {
        BigDecimal originalVolume = bill.getConsumptionVolume();
        BigDecimal originalBaseCharge = bill.getBaseChargeAmount();

        if (originalVolume.compareTo(BigDecimal.ZERO) > 0 && originalBaseCharge.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal unitPrice = originalBaseCharge.divide(originalVolume, 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal newBaseCharge = unitPrice.multiply(adjustedVolume).setScale(2, BigDecimal.ROUND_HALF_UP);

            // 更新基础费用
            bill.setBaseChargeAmount(newBaseCharge);

            // 更新总金额
            BigDecimal oldTotal = bill.getTotalAmount();
            BigDecimal oldBaseCharge = originalBaseCharge;
            BigDecimal difference = newBaseCharge.subtract(oldBaseCharge);
            bill.setTotalAmount(oldTotal.add(difference).setScale(2, BigDecimal.ROUND_HALF_UP));
        }
    }

    /**
     * 根据阶梯价格重新计算费用
     *
     * @param bill           账单信息
     * @param adjustedVolume 调整后的用量
     */
    private void recalculateLadderPrice(WaterfeeBill bill, BigDecimal adjustedVolume) {
        // 获取价格方案ID
        Long pricePlanId = bill.getPricePlanId();
        if (pricePlanId == null) {
            log.warn("账单没有关联价格方案，使用简单计算方式");
            calculateBySimpleRatio(bill, adjustedVolume);
            return;
        }

        try {
            // 获取阶梯价格信息
            List<LadderPriceTier> ladderPriceTiers = getLadderPriceTiersFromPricePlan(pricePlanId);
            if (ladderPriceTiers == null || ladderPriceTiers.isEmpty()) {
                log.warn("无法获取阶梯价格信息，使用标准价格计算");
                recalculateStandardPrice(bill, adjustedVolume);
                return;
            }

            // 计算阶梯费用
            BigDecimal totalCharge = BigDecimal.ZERO;
            BigDecimal remainingVolume = adjustedVolume;

            BigDecimal oldTierTotal = BigDecimal.ZERO;

            // 按阶梯计算费用
            for (int i = 0; i < ladderPriceTiers.size() && remainingVolume.compareTo(BigDecimal.ZERO) > 0; i++) {
                LadderPriceTier tier = ladderPriceTiers.get(i);
                BigDecimal tierLimit = tier.getUpperLimit();
                BigDecimal tierPrice = tier.getPrice();

                oldTierTotal = oldTierTotal.add(tierPrice.multiply(
                    tierLimit != null ? tierLimit : BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                // 最后一个阶梯没有上限
                BigDecimal volumeInTier;
                if (i == ladderPriceTiers.size() - 1 || tierLimit == null) {
                    volumeInTier = remainingVolume;
                } else {
                    // 计算当前阶梯的用量
                    BigDecimal previousLimit = i > 0 ? ladderPriceTiers.get(i - 1).getUpperLimit() : BigDecimal.ZERO;
                    BigDecimal tierVolume = tierLimit.subtract(previousLimit);
                    volumeInTier = remainingVolume.compareTo(tierVolume) > 0 ? tierVolume : remainingVolume;
                }

                // 计算当前阶梯的费用
                BigDecimal tierCharge = volumeInTier.multiply(tierPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
                totalCharge = totalCharge.add(tierCharge);


                // 减少剩余用量
                remainingVolume = remainingVolume.subtract(volumeInTier);
            }

            // 更新基础费用（阶梯价格中基础费用为0）
            BigDecimal originalBaseCharge = bill.getBaseChargeAmount();
            bill.setBaseChargeAmount(BigDecimal.ZERO);

            // 更新总金额
            BigDecimal oldTotal = bill.getTotalAmount();
            BigDecimal newTierTotal = totalCharge;

            // 计算差额
            BigDecimal difference = newTierTotal.subtract(oldTierTotal).subtract(originalBaseCharge);
            bill.setTotalAmount(oldTotal.add(difference).setScale(2, BigDecimal.ROUND_HALF_UP));
        } catch (Exception e) {
            log.error("计算阶梯价格失败", e);
            // 如果计算失败，使用简单比例计算
            calculateBySimpleRatio(bill, adjustedVolume);
        }
    }

    /**
     * 从价格方案获取阶梯价格信息
     *
     * @param pricePlanId 价格方案ID
     * @return 阶梯价格信息列表
     */
    private List<LadderPriceTier> getLadderPriceTiersFromPricePlan(Long pricePlanId) {
        try {
            // 从Redis缓存中获取阶梯价格方案
            org.dromara.waterfee.priceManage.domain.vo.WaterfeePriceConfigVo priceVo = priceCacheService
                .getLadderPrice(pricePlanId);
            if (priceVo != null && priceVo.getPriceTiers() != null && !priceVo.getPriceTiers().isEmpty()) {
                List<LadderPriceTier> tiers = new ArrayList<>();

                // 将价格方案中的阶梯信息转换为LadderPriceTier对象
                for (int i = 0; i < priceVo.getPriceTiers().size(); i++) {
                    org.dromara.waterfee.priceManage.domain.WaterfeePriceTier tier = priceVo.getPriceTiers().get(i);

                    // 计算上限
                    BigDecimal upperLimit = null;
                    if (tier.getEndQuantity() != null) {
                        upperLimit = new BigDecimal(tier.getEndQuantity().toString());
                    }

                    // 获取价格
                    BigDecimal price = new BigDecimal(tier.getPrice().toString());

                    // 添加到列表
                    tiers.add(new LadderPriceTier(upperLimit, price));
                }

                return tiers;
            }
        } catch (Exception e) {
            log.error("从缓存获取阶梯价格方案失败", e);
        }

        // 如果获取失败，返回默认值
        List<LadderPriceTier> tiers = new ArrayList<>();
        tiers.add(new LadderPriceTier(new BigDecimal("20"), new BigDecimal("3.00")));
        tiers.add(new LadderPriceTier(new BigDecimal("30"), new BigDecimal("3.50")));
        tiers.add(new LadderPriceTier(null, new BigDecimal("4.00")));
        return tiers;
    }

    /**
     * 阶梯价格信息
     */
    private static class LadderPriceTier {
        private final BigDecimal upperLimit;
        private final BigDecimal price;

        public LadderPriceTier(BigDecimal upperLimit, BigDecimal price) {
            this.upperLimit = upperLimit;
            this.price = price;
        }

        public BigDecimal getUpperLimit() {
            return upperLimit;
        }

        public BigDecimal getPrice() {
            return price;
        }
    }

    /**
     * 生成支付编号
     *
     * @return 支付编号
     */
    private String generatePaymentNumber() {
        Date now = DateUtils.getNowDate();
        String dateStr = DateUtils.parseDateToStr(FormatsType.YYYY_MM_DD, now);
        String uuid = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 8);
        return "PAY" + dateStr + uuid;
    }

    /**
     * 智能表自动扣款（只记录余额变更，不创建交易记录）
     *
     * @param userId  用户ID
     * @param billIds 账单ID列表
     * @param amount  扣款金额
     * @param remark  备注
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean autoPayBillByDeposit(Long userId, List<Long> billIds, BigDecimal amount, String remark) {
        if (userId == null) {
            throw new ServiceException("用户ID不能为空");
        }

        if (billIds == null || billIds.isEmpty()) {
            throw new ServiceException("账单ID不能为空");
        }

        if (amount == null || amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("扣款金额必须>=0");
        }

        // 查询用户信息
        WaterfeeUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new ServiceException("用户不存在：" + userId);
        }

        String userNo = user.getUserNo();
        if (StringUtils.isBlank(userNo)) {
            throw new ServiceException("用户户号为空，无法自动扣款");
        }

        // 获取账单信息
        List<WaterfeeBillVo> bills = new ArrayList<>();
        for (Long billId : billIds) {
            WaterfeeBillVo bill = billMapper.selectVoById(billId);
            if (bill == null) {
                throw new ServiceException("账单不存在：" + billId);
            }
            if (bill.getTotalAmount() == null || amount.compareTo(BigDecimal.ZERO) < 0) {
                log.warn("账单金额 {} 无效，跳过自动扣款：{}", amount, bill.getBillNumber());
                continue;
            } else if (amount.compareTo(BigDecimal.ZERO) == 0) {
                log.warn("账单金额为{}", amount);
                continue;
            }
            if (!"ISSUED".equals(bill.getBillStatus())) {
                log.warn("账单状态不正确，跳过自动扣款：{}", bill.getBillNumber());
                continue;
            }
            bills.add(bill);
        }

        // 获取用户锁
        ReentrantLock lock = userLockManager.getUserLock(userNo);
        try {
            // 尝试获取锁，最多等待3秒
            if (lock.tryLock(3, TimeUnit.SECONDS)) {
                try {
                    // 重新查询用户信息（确保获取最新余额）
                    user = userMapper.selectById(userId);
                    if (user == null) {
                        throw new ServiceException("用户不存在：" + userId);
                    }

                    BigDecimal balance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
                    if (balance.compareTo(amount) < 0) {
                        log.warn("用户余额不足，无法自动扣款，户号：{}，当前余额：{}，需扣款：{}",
                            userNo, balance, amount);
                        return false;
                    }

                    // 扣减用户余额
                    BigDecimal newBalance = balance.subtract(amount);
                    WaterfeeUser updateUser = new WaterfeeUser();
                    updateUser.setUserId(userId);
                    updateUser.setBalance(newBalance);
                    updateUser.setUpdateBy(1L); // 系统自动操作
                    updateUser.setUpdateTime(DateUtils.getNowDate());
                    int rows = userMapper.updateById(updateUser);

                    if (rows <= 0) {
                        throw new ServiceException("更新用户余额失败");
                    }

                    log.info("智能表自动扣款成功，户号：{}，当前余额：{}，扣款金额：{}，更新后余额：{}",
                        userNo, balance, amount, newBalance);

                    // 分配支付金额到各个账单并更新账单状态
                    BigDecimal remainingAmount = amount;
                    for (WaterfeeBillVo bill : bills) {
                        if (remainingAmount.compareTo(BigDecimal.ZERO) <= 0) {
                            break;
                        }

                        BigDecimal billDue = bill.getBalanceDue() != null ? bill.getBalanceDue() : BigDecimal.ZERO;
                        BigDecimal payAmount = remainingAmount.compareTo(billDue) >= 0 ? billDue : remainingAmount;

                        // 更新账单支付状态
                        billService.payBill(bill.getBillId(), payAmount);

                        remainingAmount = remainingAmount.subtract(payAmount);
                    }

                    // 记录余额变更（不创建交易记录）
                    balanceChangeRecordService.recordBalanceChange(
                        userId, userNo, "PAYMENT",
                        balance, amount.negate(), newBalance,
                        bills.stream().map(b -> b.getBillId().toString()).collect(Collectors.joining(",")),
                        "BILL", "智能表自动扣款", "系统",
                        remark != null ? remark : "智能表自动扣款"
                    );

                    return true;
                } finally {
                    // 释放锁
                    lock.unlock();
                }
            } else {
                log.warn("获取用户余额锁超时，户号：{}", userNo);
                return false;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("智能表自动扣款时被中断，户号：{}", userNo, e);
            return false;
        } catch (Exception e) {
            log.error("智能表自动扣款异常，户号：{}", userNo, e);
            return false;
        }
    }

    /**
     * 实收账冲正
     *
     * @param bo 冲正数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reversalPayment(WaterfeeReversalBo bo) {
        if (StringUtils.isBlank(bo.getPaymentDetailId())) {
            throw new ServiceException("收费明细ID不能为空");
        }

        if (StringUtils.isBlank(bo.getReversalReason())) {
            throw new ServiceException("冲正原因不能为空");
        }

        // 查询原收费记录
        PaymentDetail originalDetail = paymentDetailMapper.selectById(bo.getPaymentDetailId());
        if (originalDetail == null) {
            throw new ServiceException("收费记录不存在：" + bo.getPaymentDetailId());
        }

        // 验证收费记录状态
        if (!"SUCCESS".equals(originalDetail.getPaymentStatus())) {
            throw new ServiceException("只能冲正成功的收费记录");
        }

        // 检查是否已冲正 - 这里应该单独检查，避免与上面的状态检查冲突
        LambdaQueryWrapper<PaymentDetail> checkWrapper = new LambdaQueryWrapper<>();
        checkWrapper.eq(PaymentDetail::getPaymentDetailId, bo.getPaymentDetailId())
            .eq(PaymentDetail::getPaymentStatus, "REVERSED");
        if (paymentDetailMapper.selectCount(checkWrapper) > 0) {
            throw new ServiceException("该收费记录已被冲正");
        }

        // 验证冲正时间限制（24小时内）
        if (!bo.getForceReversal() && isTimeExceeded(originalDetail.getPaymentTime())) {
            throw new ServiceException("超过冲正时间限制（24小时）");
        }

        // 获取账单信息
        WaterfeeBillVo bill = null;
        if (StringUtils.isNotBlank(originalDetail.getBillId())) {
            bill = billMapper.selectVoById(Long.valueOf(originalDetail.getBillId()));
        }

        Long userId = originalDetail.getUserId();
        String userNo = null;

        if (userId != null) {
            WaterfeeUser user = userMapper.selectById(userId);
            if (user != null) {
                userNo = user.getUserNo();
            }
        }

        // 如果是预存款支付，需要处理用户余额
        if ("DEPOSIT".equals(originalDetail.getPaymentMethod()) && userId != null && StringUtils.isNotBlank(userNo)) {
            // 获取用户锁
            ReentrantLock lock = userLockManager.getUserLock(userNo);
            try {
                if (lock.tryLock(3, TimeUnit.SECONDS)) {
                    try {
                        // 重新查询用户信息
                        WaterfeeUser user = userMapper.selectById(userId);
                        if (user == null) {
                            throw new ServiceException("用户不存在：" + userId);
                        }

                        BigDecimal currentBalance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
                        BigDecimal reversalAmount = originalDetail.getPaymentAmount();

                        // 冲正预存款支付：应该增加用户余额（退还之前扣减的金额）
                        BigDecimal newBalance = currentBalance.add(reversalAmount);

                        WaterfeeUser updateUser = new WaterfeeUser();
                        updateUser.setUserId(userId);
                        updateUser.setBalance(newBalance);
                        updateUser.setUpdateBy(LoginHelper.getUserId());
                        updateUser.setUpdateTime(DateUtils.getNowDate());
                        int rows = userMapper.updateById(updateUser);

                        if (rows <= 0) {
                            throw new ServiceException("更新用户余额失败");
                        }

                        log.info("冲正操作更新用户余额成功，户号：{}，当前余额：{}，退还金额：{}，更新后余额：{}",
                            userNo, currentBalance, reversalAmount, newBalance);

                        // 记录余额变更（正数表示增加）
                        balanceChangeRecordService.recordBalanceChange(
                            userId, userNo, "REVERSAL",
                            currentBalance, reversalAmount, newBalance,
                            originalDetail.getPaymentDetailId(), "REVERSAL", "实收账冲正",
                            LoginHelper.getUsername(), "冲正原因：" + bo.getReversalReason()
                        );
                    } finally {
                        lock.unlock();
                    }
                } else {
                    log.warn("获取用户余额锁超时，户号：{}", userNo);
                    throw new ServiceException("系统繁忙，请稍后再试");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("冲正操作被中断，户号：{}", userNo, e);
                throw new ServiceException("冲正操作被中断");
            }
        }

        // 处理第三方支付退款
        if (!"DEPOSIT".equals(originalDetail.getPaymentMethod())) {
            // 对于现金、银行卡等支付方式，只记录冲正，不处理实际退款
            log.info("非预存款支付方式冲正，支付方式：{}，需要手动处理退款", originalDetail.getPaymentMethod());

            //TODO: 如果是微信、支付宝等在线支付，可以调用相应的退款接口
            // if ("WECHAT_PAY".equals(originalDetail.getPaymentMethod())) {
            //     wechatPayService.refund(originalDetail.getTransactionId(), reversalAmount);
            // }
        }

        // 更新账单状态（如果存在）
        if (bill != null) {
            WaterfeeBill updateBill = getWaterfeeBill(originalDetail, bill);
            billMapper.updateById(updateBill);
        }

        // 创建冲正记录（负数金额）
        PaymentDetail reversalDetail = new PaymentDetail();
        reversalDetail.setPaymentDetailId(generatePaymentNumber());
        reversalDetail.setBillId(originalDetail.getBillId());
        reversalDetail.setUserId(originalDetail.getUserId());
        reversalDetail.setBillMonth(originalDetail.getBillMonth());
        reversalDetail.setPaymentAmount(originalDetail.getPaymentAmount().negate()); // 负数
        reversalDetail.setPaymentTime(DateUtils.getNowDate());
        reversalDetail.setPaymentMethod(originalDetail.getPaymentMethod());
        reversalDetail.setPaymentStatus("SUCCESS");
        reversalDetail.setRemark("冲正：" + bo.getReversalReason() + "（原单号：" + originalDetail.getPaymentDetailId() + "）");
        reversalDetail.setIncomeType("reversal");
        reversalDetail.setTollCollector(LoginHelper.getUsername());
        reversalDetail.setIncomeTime(DateUtils.getNowDate());

        paymentDetailMapper.insert(reversalDetail);

        // 更新原记录状态为已冲正
        PaymentDetail updateOriginal = new PaymentDetail();
        updateOriginal.setPaymentDetailId(originalDetail.getPaymentDetailId());
        updateOriginal.setPaymentStatus("REVERSED");
        updateOriginal.setRemark(originalDetail.getRemark() + "【已冲正】");
        paymentDetailMapper.updateById(updateOriginal);

        log.info("实收账冲正成功，原单号：{}，冲正金额：{}，操作人：{}",
            originalDetail.getPaymentDetailId(), originalDetail.getPaymentAmount(), LoginHelper.getUsername());

        return true;
    }

    @NotNull
    private static WaterfeeBill getWaterfeeBill(PaymentDetail originalDetail, WaterfeeBillVo bill) {
        BigDecimal reversalAmount = originalDetail.getPaymentAmount();
        BigDecimal currentAmountPaid = bill.getAmountPaid() != null ? bill.getAmountPaid() : BigDecimal.ZERO;
        BigDecimal newAmountPaid = currentAmountPaid.subtract(reversalAmount);
        BigDecimal newBalanceDue = bill.getTotalAmount().subtract(newAmountPaid);

        // 确定新的账单状态
        String newStatus;
        if (newAmountPaid.compareTo(BigDecimal.ZERO) == 0) {
            newStatus = "ISSUED"; // 完全未支付
        } else if (newAmountPaid.compareTo(bill.getTotalAmount()) < 0) {
            newStatus = "PARTIAL_PAID"; // 部分支付
        } else {
            newStatus = "PAID"; // 已支付
        }

        WaterfeeBill updateBill = new WaterfeeBill();
        updateBill.setBillId(bill.getBillId());
        updateBill.setAmountPaid(newAmountPaid);
        updateBill.setBalanceDue(newBalanceDue);
        updateBill.setBillStatus(newStatus);
        updateBill.setUpdateBy(LoginHelper.getUserId());
        updateBill.setUpdateTime(DateUtils.getNowDate());
        return updateBill;
    }

    /**
     * 批量实收账冲正
     *
     * @param paymentDetailIds 收费明细ID列表
     * @param reversalReason   冲正原因
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchReversalPayment(List<String> paymentDetailIds, String reversalReason) {
        if (paymentDetailIds == null || paymentDetailIds.isEmpty()) {
            throw new ServiceException("收费明细ID列表不能为空");
        }

        if (StringUtils.isBlank(reversalReason)) {
            throw new ServiceException("冲正原因不能为空");
        }

        List<String> failedIds = new ArrayList<>();
        List<String> successIds = new ArrayList<>();

        // 按用户分组，避免死锁
        Map<String, List<String>> userGroups = new HashMap<>();
        for (String paymentDetailId : paymentDetailIds) {
            PaymentDetail detail = paymentDetailMapper.selectById(paymentDetailId);
            if (detail != null && detail.getUserId() != null) {
                WaterfeeUser user = userMapper.selectById(detail.getUserId());
                if (user != null && StringUtils.isNotBlank(user.getUserNo())) {
                    userGroups.computeIfAbsent(user.getUserNo(), k -> new ArrayList<>()).add(paymentDetailId);
                }
            }
        }

        // 按用户分组处理
        for (Map.Entry<String, List<String>> entry : userGroups.entrySet()) {
            for (String paymentDetailId : entry.getValue()) {
                try {
                    WaterfeeReversalBo bo = new WaterfeeReversalBo();
                    bo.setPaymentDetailId(paymentDetailId);
                    bo.setReversalReason(reversalReason);
                    bo.setReversalType("BATCH");
                    reversalPayment(bo);
                    successIds.add(paymentDetailId);
                } catch (Exception e) {
                    log.error("批量冲正失败，收费明细ID：{}，错误：{}", paymentDetailId, e.getMessage());
                    failedIds.add(paymentDetailId);
                }
            }
        }

        if (!failedIds.isEmpty()) {
            throw new ServiceException("部分冲正失败，失败数量：" + failedIds.size() + "，成功数量：" + successIds.size());
        }

        return true;
    }

    /**
     * 检查是否超过冲正时间限制
     */
    private boolean isTimeExceeded(Date paymentTime) {
        if (paymentTime == null) {
            return true;
        }

        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.HOUR_OF_DAY, -24); // 24小时前
        return paymentTime.before(cal.getTime());
    }

    /**
     * 预存款退款
     *
     * @param bo 退款数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean refundDeposit(WaterfeeDepositRefundBo bo) {
        if (bo.getUserId() == null) {
            throw new ServiceException("用户ID不能为空");
        }

        if (bo.getRefundAmount() == null || bo.getRefundAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("退款金额必须大于0");
        }

        if (StringUtils.isBlank(bo.getRefundReason())) {
            throw new ServiceException("退款原因不能为空");
        }

        if (StringUtils.isBlank(bo.getRefundMethod())) {
            throw new ServiceException("退款方式不能为空");
        }

        // 查询用户信息
        WaterfeeUser user = userMapper.selectById(bo.getUserId());
        if (user == null) {
            throw new ServiceException("用户不存在：" + bo.getUserId());
        }

        String userNo = user.getUserNo();
        if (StringUtils.isBlank(userNo)) {
            throw new ServiceException("用户户号为空");
        }

        // 验证银行卡信息（如果是银行卡退款）
        if ("BANK_CARD".equals(bo.getRefundMethod())) {
            if (StringUtils.isBlank(bo.getBankCardNo())) {
                throw new ServiceException("银行卡号不能为空");
            }
            if (StringUtils.isBlank(bo.getBankName())) {
                throw new ServiceException("开户行不能为空");
            }
            if (StringUtils.isBlank(bo.getAccountName())) {
                throw new ServiceException("账户名不能为空");
            }
        }

        // 获取用户锁，确保并发安全
        ReentrantLock lock = userLockManager.getUserLock(userNo);
        try {
            if (lock.tryLock(5, TimeUnit.SECONDS)) {
                try {
                    // 重新查询用户信息（防止并发修改）
                    user = userMapper.selectById(bo.getUserId());
                    if (user == null) {
                        throw new ServiceException("用户不存在：" + bo.getUserId());
                    }

                    BigDecimal currentBalance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
                    BigDecimal refundAmount = bo.getRefundAmount();

                    // 检查余额是否充足
                    if (currentBalance.compareTo(refundAmount) < 0) {
                        throw new ServiceException("用户余额不足，当前余额：" + currentBalance + "元，申请退款：" + refundAmount + "元");
                    }

                    // 检查是否有未结清账单（可选校验）
                    if (!bo.getForceRefund()) {
                        LambdaQueryWrapper<WaterfeeBill> billWrapper = new LambdaQueryWrapper<>();
                        billWrapper.eq(WaterfeeBill::getCustomerId, bo.getUserId())
                            .in(WaterfeeBill::getBillStatus, "ISSUED", "PARTIAL_PAID", "OVERDUE");
                        long unpaidBillCount = billMapper.selectCount(billWrapper);

                        if (unpaidBillCount > 0) {
                            throw new ServiceException("用户存在未结清账单，无法退款。如需强制退款，请联系管理员");
                        }
                    }

                    // 扣减用户余额
                    BigDecimal newBalance = currentBalance.subtract(refundAmount);
                    WaterfeeUser updateUser = new WaterfeeUser();
                    updateUser.setUserId(bo.getUserId());
                    updateUser.setBalance(newBalance);
                    updateUser.setUpdateBy(LoginHelper.getUserId());
                    updateUser.setUpdateTime(DateUtils.getNowDate());

                    int rows = userMapper.updateById(updateUser);
                    if (rows <= 0) {
                        throw new ServiceException("更新用户余额失败");
                    }

                    // 生成退款记录
                    PaymentDetail refundDetail = new PaymentDetail();
                    refundDetail.setPaymentDetailId(generatePaymentNumber());
                    refundDetail.setUserId(bo.getUserId());
                    refundDetail.setPaymentAmount(refundAmount.negate()); // 负数表示退款
                    refundDetail.setPaymentTime(DateUtils.getNowDate());
                    refundDetail.setPaymentMethod(bo.getRefundMethod());
                    refundDetail.setPaymentStatus("SUCCESS");
                    refundDetail.setIncomeType("deposit_refund");
                    refundDetail.setTollCollector(LoginHelper.getUsername());
                    refundDetail.setIncomeTime(DateUtils.getNowDate());

                    // 构建退款备注
                    StringBuilder remarkBuilder = new StringBuilder();
                    remarkBuilder.append("预存款退款：").append(bo.getRefundReason());
                    if ("BANK_CARD".equals(bo.getRefundMethod())) {
                        remarkBuilder.append("，退款至：").append(bo.getBankName())
                            .append("(").append(maskBankCard(bo.getBankCardNo())).append(")")
                            .append("，户名：").append(bo.getAccountName());
                    }
                    if (StringUtils.isNotBlank(bo.getRemark())) {
                        remarkBuilder.append("，备注：").append(bo.getRemark());
                    }
                    refundDetail.setRemark(remarkBuilder.toString());

                    paymentDetailMapper.insert(refundDetail);

                    // 记录余额变更
                    balanceChangeRecordService.recordBalanceChange(
                        bo.getUserId(), userNo, "DEPOSIT_REFUND",
                        currentBalance, refundAmount.negate(), newBalance,
                        refundDetail.getPaymentDetailId(), "REFUND", "预存款退款",
                        LoginHelper.getUsername(), bo.getRefundReason()
                    );

                    // 处理第三方退款（微信、支付宝等）
                    if ("WECHAT".equals(bo.getRefundMethod()) || "ALIPAY".equals(bo.getRefundMethod())) {
                        processThirdPartyRefund(bo, refundDetail.getPaymentDetailId());
                    }

                    log.info("预存款退款成功，用户ID：{}，户号：{}，退款金额：{}，退款方式：{}，操作人：{}",
                        bo.getUserId(), userNo, refundAmount, bo.getRefundMethod(), LoginHelper.getUsername());

                    return true;

                } finally {
                    lock.unlock();
                }
            } else {
                log.warn("获取用户余额锁超时，户号：{}", userNo);
                throw new ServiceException("系统繁忙，请稍后再试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("预存款退款操作被中断，户号：{}", userNo, e);
            throw new ServiceException("退款操作被中断");
        }
    }

    /**
     * 处理第三方退款
     */
    private void processThirdPartyRefund(WaterfeeDepositRefundBo bo, String refundOrderNo) {
        try {
            if ("WECHAT".equals(bo.getRefundMethod())) {
                // 调用微信退款接口
                WechatRefundParam refundParam = new WechatRefundParam();
                refundParam.setOutTradeNo(refundOrderNo);
                refundParam.setRefundFee(bo.getRefundAmount().multiply(new BigDecimal("100")).longValue()); // 转换为分
                refundParam.setTotalFee(bo.getRefundAmount().multiply(new BigDecimal("100")).longValue());
                refundParam.setReason(bo.getRefundReason());

                wechatRefundService.applyForRefund(refundParam);
                log.info("微信退款申请成功，订单号：{}，金额：{}", refundOrderNo, bo.getRefundAmount());

            } else if ("ALIPAY".equals(bo.getRefundMethod())) {
                // 调用支付宝退款接口（需要实现）
                log.info("支付宝退款申请，订单号：{}，金额：{}", refundOrderNo, bo.getRefundAmount());
            }
        } catch (Exception e) {
            log.error("第三方退款失败，订单号：{}，退款方式：{}，错误：{}",
                refundOrderNo, bo.getRefundMethod(), e.getMessage(), e);
            // 第三方退款失败不影响主流程，但需要记录日志供后续处理
        }
    }

    /**
     * 银行卡号脱敏
     */
    private String maskBankCard(String bankCardNo) {
        if (StringUtils.isBlank(bankCardNo) || bankCardNo.length() < 8) {
            return bankCardNo;
        }
        return bankCardNo.substring(0, 4) + "****" + bankCardNo.substring(bankCardNo.length() - 4);
    }


    /**
     * 微信预存款支付账单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean wechatPayBillByDeposit(WechatPayBillBo bo) {
        // 1. 参数验证
        if (bo.getBillId() == null) {
            throw new ServiceException("账单ID不能为空");
        }
        if (bo.getAmount() == null || bo.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("支付金额必须大于0");
        }
        if (StringUtils.isBlank(bo.getOpenid())) {
            throw new ServiceException("微信openid不能为空");
        }

        // 2. 查询账单信息
        WaterfeeBillVo bill = billMapper.selectVoById(bo.getBillId());
        if (bill == null) {
            throw new ServiceException("账单不存在");
        }
        WaterfeeUser user = userMapper.selectById(bill.getCustomerId());
        if (user == null) {
            throw new ServiceException("用户信息不存在");
        }
        String userNo = user.getUserNo();
        if (StringUtils.isBlank(userNo)) {
            throw new ServiceException("用户户号为空，无法使用预存款支付");
        }
        if(wechatUserBindingService.queryBindingUserByOpenidAndUserNo(bo.getOpenid(),user.getUserNo()) == null) {
            throw new ServiceException("微信账号未绑定该户号，无法操作");
        }
        // 3. 检查账单状态
        if (!"ISSUED".equals(bill.getBillStatus())) {
            throw new ServiceException("账单状态不正确，只能支付已发行的账单");
        }

        // 4. 检查支付金额
        BigDecimal balanceDue = bill.getBalanceDue() != null ? bill.getBalanceDue() : BigDecimal.ZERO;
        if (bo.getAmount().compareTo(balanceDue) != 0) {
            throw new ServiceException("支付金额与应付金额不一致");
        }

        // 获取用户锁
        ReentrantLock lock = userLockManager.getUserLock(userNo);
        try {
            // 尝试获取锁，最多等待3秒
            if (lock.tryLock(3, TimeUnit.SECONDS)) {
                try {
                    // 重新查询用户信息（确保获取最新余额）
                    user = userMapper.selectById(user.getUserId());
                    if (user == null) {
                        throw new ServiceException("用户不存在：" + user.getUserId());
                    }

                    // 使用IWaterfeeUserService获取余额
                    BigDecimal balance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
                    if (balance.compareTo(bo.getAmount()) < 0) {
                        throw new ServiceException("预存款余额不足，当前余额：" + balance + "元，需支付：" + bo.getAmount() + "元");
                    }

                    // 直接扣减用户余额
                    BigDecimal newBalance = balance.subtract(bo.getAmount());

                    WaterfeeUser updateUser = new WaterfeeUser();
                    updateUser.setUserId(user.getUserId());
                    updateUser.setBalance(newBalance);
                    updateUser.setUpdateBy(LoginHelper.getUserId());
                    updateUser.setUpdateTime(DateUtils.getNowDate());
                    int rows = userMapper.updateById(updateUser);

                    if (rows <= 0) {
                        throw new ServiceException("更新用户余额失败");
                    }

                    log.info("用户余额更新成功，户号：{}，当前余额：{}，支付金额：{}，更新后余额：{}",
                        userNo, balance, bo.getAmount(), newBalance);

                    // 记录余额变更
                    balanceChangeRecordService.recordBalanceChange(
                        user.getUserId(), userNo, "PAYMENT",
                        balance, bo.getAmount().negate(), newBalance,
                        bill.getBillId().toString(),
                        "BILL", "账单支付", "微信用户openid：" + bo.getOpenid(),
                        "使用预存款支付账单：" + bill.getBillNumber()
                    );
                } finally {
                    // 释放锁
                    lock.unlock();
                }
            } else {
                log.warn("获取用户余额锁超时，户号：{}", userNo);
                throw new ServiceException("系统繁忙，请稍后再试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("支付时被中断，户号：{}", userNo, e);
            throw new ServiceException("支付操作被中断");
        }

        bill.setRemark("使用预存款支付：" + (bill.getRemark() != null ? bill.getRemark() : "支付账单：" + bill.getBillNumber()));
        // 调用账单支付接口
        billService.payBill(bill.getBillId(), bo.getAmount());
        return true;
    }
}
