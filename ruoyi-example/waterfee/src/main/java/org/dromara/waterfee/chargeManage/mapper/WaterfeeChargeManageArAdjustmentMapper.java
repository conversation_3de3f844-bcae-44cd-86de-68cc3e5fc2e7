package org.dromara.waterfee.chargeManage.mapper;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageArAdjustment;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageArAdjustmentVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageArAdjustmentBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 应收账追补记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface WaterfeeChargeManageArAdjustmentMapper extends BaseMapperPlus<WaterfeeChargeManageArAdjustment, WaterfeeChargeManageArAdjustmentVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeChargeManageArAdjustmentVo>> P selectVoPage(IPage<WaterfeeChargeManageArAdjustment> page, Wrapper<WaterfeeChargeManageArAdjustment> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询应收账追补记录列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WaterfeeChargeManageArAdjustmentVo> queryList(@Param("page") Page<WaterfeeChargeManageArAdjustment> page, @Param("query") WaterfeeChargeManageArAdjustmentBo query);

}
