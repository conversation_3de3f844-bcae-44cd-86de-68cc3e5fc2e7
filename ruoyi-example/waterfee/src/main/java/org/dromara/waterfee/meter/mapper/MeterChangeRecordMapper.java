package org.dromara.waterfee.meter.mapper;

import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.waterfee.meter.domain.MeterChangeRecord;
import org.dromara.waterfee.meter.domain.vo.MeterChangeRecordVo;

/**
 * 表具变更记录Mapper接口
 */
public interface MeterChangeRecordMapper extends BaseMapperPlus<MeterChangeRecord, MeterChangeRecordVo> {

    /**
     * 查询最近一次换表记录
     */
    MeterChangeRecordVo selectLastChangeRecord(String meterNo);
}
