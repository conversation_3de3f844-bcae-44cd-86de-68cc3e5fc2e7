package org.dromara.waterfee.meter.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.waterfee.meter.domain.WaterfeeMeter;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;

import java.util.List;

/**
 * 水表信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface WaterfeeMeterMapper extends BaseMapperPlus<WaterfeeMeter, WaterfeeMeterVo> {

    /**
     * 根据表册ID查询水表编号列表
     *
     * @param meterBookId 表册ID
     * @return 水表编号列表
     */
    @Select("SELECT meter_no FROM waterfee_meter WHERE meter_book_id = #{meterBookId} AND del_flag = '0'")
    List<String> selectMeterNosByBookId(@Param("meterBookId") Long meterBookId);

    /**
     * 根据表册ID查询机械水表编号列表
     *
     * @param meterBookId 表册ID
     * @return 水表编号列表
     */
    @Select("SELECT meter_no FROM waterfee_meter WHERE meter_book_id = #{meterBookId} AND del_flag = '0' meter_type = 2 ")
    List<String> selectMechMeterNosByBookId(@Param("meterBookId") Long meterBookId);
}
