package org.dromara.waterfee.meterReading.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingManual;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.domain.bo.WaterfeeMeterReadingManualBo;
import org.dromara.waterfee.meterReading.domain.vo.WaterfeeMeterReadingManualVo;
import org.dromara.waterfee.meterReading.mapper.WaterfeeMeterReadingManualMapper;
import org.dromara.waterfee.meterReading.service.IMeterReadingRecordService;
import org.dromara.waterfee.meterReading.service.IWaterfeeMeterReadingManualService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 抄表补录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@RequiredArgsConstructor
@Service
public class WaterfeeMeterReadingManualServiceImpl implements IWaterfeeMeterReadingManualService {

    private final WaterfeeMeterReadingManualMapper baseMapper;
    private final IMeterReadingRecordService meterReadingRecordService;
    private final Converter converter;

    /**
     * 查询抄表补录
     *
     * @param manualId 主键
     * @return 抄表补录
     */
    @Override
    public WaterfeeMeterReadingManualVo queryById(Long manualId) {
        // 使用自定义方法查询详细信息，包含关联信息
        WaterfeeMeterReadingManualVo vo = baseMapper.selectManualDetailWithRelated(manualId);
        if (vo == null) {
            // 如果没有查到关联信息，则使用默认方法查询
            vo = baseMapper.selectVoById(manualId);
        }
        return vo;
    }

    /**
     * 查询抄表补录列表
     *
     * @param bo 抄表补录
     * @return 抄表补录
     */
    @Override
    public TableDataInfo<WaterfeeMeterReadingManualVo> queryPageList(WaterfeeMeterReadingManualBo bo, PageQuery pageQuery) {
        // 使用自定义方法查询列表，包含关联信息
        List<WaterfeeMeterReadingManualVo> list = baseMapper.selectManualListWithRelated(bo.getMeterNo(), bo.getBeginTime(), bo.getEndTime());

        // 手动分页
        long total = list.size();
        int pageSize = pageQuery.getPageSize();
        int pageNum = pageQuery.getPageNum();
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, list.size());

        // 如果超出范围，返回空列表
        if (fromIndex >= list.size()) {
            return TableDataInfo.build(new ArrayList<>());

        }

        // 返回分页结果
        return TableDataInfo.build(list.subList(fromIndex, toIndex));
    }

    /**
     * 查询抄表补录列表
     *
     * @param bo 抄表补录
     * @return 抄表补录
     */
    @Override
    public List<WaterfeeMeterReadingManualVo> queryList(WaterfeeMeterReadingManualBo bo) {
        // 使用自定义方法查询列表，包含关联信息
        return baseMapper.selectManualListWithRelated(bo.getMeterNo(), bo.getBeginTime(), bo.getEndTime());
    }

    private LambdaQueryWrapper<WaterfeeMeterReadingManual> buildQueryWrapper(WaterfeeMeterReadingManualBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeMeterReadingManual> lqw = new LambdaQueryWrapper<>();
        lqw.eq(bo.getManualId() != null, WaterfeeMeterReadingManual::getManualId, bo.getManualId());
        lqw.eq(StringUtils.isNotBlank(bo.getMeterNo()), WaterfeeMeterReadingManual::getMeterNo, bo.getMeterNo());
        lqw.eq(bo.getCurrentReading() != null, WaterfeeMeterReadingManual::getCurrentReading, bo.getCurrentReading());
        lqw.eq(bo.getReadingTime() != null, WaterfeeMeterReadingManual::getReadingTime, bo.getReadingTime());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), WaterfeeMeterReadingManual::getReason, bo.getReason());
        lqw.eq(StringUtils.isNotBlank(bo.getOperator()), WaterfeeMeterReadingManual::getOperator, bo.getOperator());
        return lqw;
    }

    /**
     * 新增抄表补录
     *
     * @param bo 抄表补录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(WaterfeeMeterReadingManualBo bo) {
        // 1. 转换为实体并保存补录记录
//        WaterfeeMeterReadingManual add = MapstructUtils.convert(bo, WaterfeeMeterReadingManual.class);

        WaterfeeMeterReadingManual add = BeanUtil.toBean(bo, WaterfeeMeterReadingManual.class);
        assert add != null;
        add.setOperator(bo.getOperator());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setManualId(add.getManualId());

            // 2. 查询该水表的最新抄表记录
            WaterfeeMeterReadingRecord latestRecord = meterReadingRecordService.queryLatestEntityByMeterNo(bo.getMeterNo());

            // 3. 更新最新抄表记录
            latestRecord.setCurrentReading(bo.getCurrentReading()); // 更新读数
            latestRecord.setReadingTime(bo.getReadingTime()); // 更新抄表时间
            latestRecord.setManualId(bo.getManualId()); // 更新关联补录ID
            latestRecord.setWaterUsage(null);
            latestRecord.setMeterBookId(bo.getMeterBookId()); // 更新水表账本ID

            if (latestRecord.getSourceType().equals("normal")) {  // 如果该记录是正常记录，则更新为补录记录
                latestRecord.setRecordId(null);
                latestRecord.setSourceType("manual");
                meterReadingRecordService.insertByEntity(latestRecord); // 插入一条新的记录
            } else {
                latestRecord.setSourceType("manual");
                meterReadingRecordService.updateByEntity(latestRecord); // 更新该记录
            }
        }
        return flag;
    }

    /**
     * 修改抄表补录
     *
     * @param bo 抄表补录
     * @return 结果
     */
    @Override
    public Boolean updateByBo(WaterfeeMeterReadingManualBo bo) {
        WaterfeeMeterReadingManual update = BeanUtil.toBean(bo, WaterfeeMeterReadingManual.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 批量删除抄表补录
     *
     * @param ids 需要删除的抄表补录主键
     * @return 结果
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
