package org.dromara.waterfee.user.domain.bo;

import org.dromara.waterfee.user.domain.WechatUserBinding;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 微信账号绑定户号关系业务对象 wechat_user_binding
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WechatUserBinding.class, reverseConvertGenerate = false)
public class WechatUserBindingBo extends BaseEntity {

    /**
     * 微信用户ID
     */
    @NotNull(message = "微信用户ID不能为空", groups = { EditGroup.class })
    private Long wechatUserId;

    /**
     * 微信用户标识
     */
    @NotBlank(message = "微信用户标识不能为空", groups = { AddGroup.class, EditGroup.class })
    private String openid;

    /**
     * 用水户编号
     */
    @NotBlank(message = "用水户编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userNo;

    /**
     * 备注
     */
    private String remark;


}
