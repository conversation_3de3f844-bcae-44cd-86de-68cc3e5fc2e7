package org.dromara.waterfee.meter.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.meter.domain.bo.MechanicalMeterQueryBo;
import org.dromara.waterfee.meter.domain.vo.MechanicalMeterDetailVo;
import org.dromara.waterfee.meter.domain.vo.MechanicalMeterReadingVo;

import java.util.List;
import java.util.Map;

/**
 * 机械表服务接口
 *
 * <AUTHOR>
 * @date 2025-04-xx
 */
public interface IMechanicalMeterService {

    /**
     * 分页查询机械表列表
     *
     * @param queryDto  查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    TableDataInfo<MechanicalMeterReadingVo> queryPageList(MechanicalMeterQueryBo queryDto, PageQuery pageQuery);

    /**
     * 查询机械表列表（不分页）
     *
     * @param queryDto 查询条件
     * @return 列表结果
     */
    List<MechanicalMeterReadingVo> queryList(MechanicalMeterQueryBo queryDto);

    /**
     * 获取机械表详情
     *
     * @param meterId 水表ID
     * @return 详情信息
     */
    MechanicalMeterDetailVo getDetail(Long meterId);

    /**
     * 获取统计数据
     *
     * @param queryDto 查询条件
     * @return 统计结果
     */
    Map<String, Object> getStatistics(MechanicalMeterQueryBo queryDto);
}
