package org.dromara.waterfee.demo;

import org.dromara.waterfee.demo.utils.RandomDataGenerator;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 随机数据生成器测试程序
 * 用于测试随机数据生成功能
 *
 * <AUTHOR>
 */
@RestController
@PostMapping("/demo")
public class RandomDataGeneratorTest {

    @GetMapping("/testRandomData")
    public void testRandomData() {
        StringBuilder result = new StringBuilder();
        result.append("=== 随机数据生成测试 ===\n\n");

        // 测试生成10个随机用户名
        result.append("随机用户名测试：\n");
        for (int i = 1; i <= 10; i++) {
            String userName = RandomDataGenerator.generateRandomUserName();
            result.append(i).append(". ").append(userName).append("\n");
        }

        result.append("\n随机手机号码测试：\n");
        for (int i = 1; i <= 5; i++) {
            String phoneNumber = RandomDataGenerator.generateRandomPhoneNumber();
            result.append(i).append(". ").append(phoneNumber).append("\n");
        }

        result.append("\n随机身份证号码测试：\n");
        for (int i = 1; i <= 5; i++) {
            String idCard = RandomDataGenerator.generateRandomIdCard();
            result.append(i).append(". ").append(idCard).append("\n");
        }

        result.append("\n随机地址测试：\n");
        for (int i = 1; i <= 5; i++) {
            String address = RandomDataGenerator.generateRandomAddress();
            result.append(i).append(". ").append(address).append("\n");
        }

        result.append("\n随机用户编号测试：\n");
        for (int i = 1; i <= 3; i++) {
            String userNo = RandomDataGenerator.generateRandomUserNo();
            result.append(i).append(". ").append(userNo).append("\n");
        }

        result.append("\n随机水表编号测试：\n");
        for (int i = 1; i <= 3; i++) {
            String meterNo = RandomDataGenerator.generateRandomMeterNo();
            result.append(i).append(". ").append(meterNo).append("\n");
        }

        System.out.println(result.toString());
    }
}
