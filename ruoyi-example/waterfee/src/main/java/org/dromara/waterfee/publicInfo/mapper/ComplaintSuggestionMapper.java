package org.dromara.waterfee.complaint.mapper;

import org.dromara.waterfee.complaint.domain.ComplaintSuggestion;
import org.dromara.waterfee.complaint.domain.vo.ComplaintSuggestionVo;
import org.dromara.waterfee.complaint.domain.bo.ComplaintSuggestionBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 投诉建议Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface ComplaintSuggestionMapper extends BaseMapperPlus<ComplaintSuggestion, ComplaintSuggestionVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<ComplaintSuggestionVo>> P selectVoPage(IPage<ComplaintSuggestion> page, Wrapper<ComplaintSuggestion> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询投诉建议列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<ComplaintSuggestionVo> queryList(@Param("page") Page<ComplaintSuggestion> page, @Param("query") ComplaintSuggestionBo query);

}
