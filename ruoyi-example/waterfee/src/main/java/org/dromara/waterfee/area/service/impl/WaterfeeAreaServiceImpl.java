package org.dromara.waterfee.area.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.constant.CacheNames;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.helper.DataBaseHelper;
import org.dromara.common.redis.utils.CacheUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.waterfee.area.domain.WaterfeeArea;
import org.dromara.waterfee.area.domain.bo.WaterfeeAreaBo;
import org.dromara.waterfee.area.domain.vo.WaterfeeAreaVo;
import org.dromara.waterfee.area.mapper.WaterfeeAreaMapper;
import org.dromara.waterfee.area.service.IWaterfeeAreaService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 区域Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@RequiredArgsConstructor
@Service
public class WaterfeeAreaServiceImpl implements IWaterfeeAreaService {

    private final WaterfeeAreaMapper baseMapper;

    /**
     * 查询区域
     *
     * @param areaId 主键
     * @return 区域
     */
    @Override
    public WaterfeeAreaVo queryById(Long areaId) {
        return baseMapper.selectVoById(areaId);
    }

    /**
     * 分页查询区域列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 区域分页列表
     */
    @Override
    public TableDataInfo<WaterfeeAreaVo> queryPageList(WaterfeeAreaBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeArea> lqw = buildQueryWrapper(bo);
        Page<WaterfeeAreaVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的区域列表
     *
     * @param bo 查询条件
     * @return 区域列表
     */
    @Override
    public List<WaterfeeAreaVo> queryList(WaterfeeAreaBo bo) {
        LambdaQueryWrapper<WaterfeeArea> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeArea> buildQueryWrapper(WaterfeeAreaBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeArea> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(WaterfeeArea::getAreaId);
        lqw.eq(bo.getParentId() != null, WaterfeeArea::getParentId, bo.getParentId());
        lqw.eq(StringUtils.isNotBlank(bo.getAncestors()), WaterfeeArea::getAncestors, bo.getAncestors());
        lqw.like(StringUtils.isNotBlank(bo.getAreaName()), WaterfeeArea::getAreaName, bo.getAreaName());
        lqw.eq(StringUtils.isNotBlank(bo.getAreaCategory()), WaterfeeArea::getAreaCategory, bo.getAreaCategory());
        lqw.eq(bo.getOrderNum() != null, WaterfeeArea::getOrderNum, bo.getOrderNum());
        lqw.eq(bo.getLeader() != null, WaterfeeArea::getLeader, bo.getLeader());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), WaterfeeArea::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getEmail()), WaterfeeArea::getEmail, bo.getEmail());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WaterfeeArea::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增区域
     *
     * @param bo 区域
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeAreaBo bo) {
        WaterfeeArea add = MapstructUtils.convert(bo, WaterfeeArea.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAreaId(add.getAreaId());
        }
        return flag;
    }

    /**
     * 修改区域
     *
     * @param bo 区域
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeAreaBo bo) {
        WaterfeeArea update = MapstructUtils.convert(bo, WaterfeeArea.class);
        WaterfeeArea oldArea = baseMapper.selectById(update.getAreaId());
        if (!oldArea.getParentId().equals(update.getParentId())) {
            // 如果是新父部门 则校验是否具有新父部门权限 避免越权
            this.checkDataScope(update.getParentId());
            WaterfeeArea newParentArea = baseMapper.selectById(update.getParentId());
            if (ObjectUtil.isNotNull(newParentArea) && ObjectUtil.isNotNull(oldArea)) {
                String newAncestors = newParentArea.getAncestors() + StringUtils.SEPARATOR + newParentArea.getAreaId();
                String oldAncestors = oldArea.getAncestors();
                update.setAncestors(newAncestors);
                updateAreaChildren(update.getAreaId(), newAncestors, oldAncestors);
            }
        } else {
            update.setAncestors(oldArea.getAncestors());
        }
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 修改子元素关系
     *
     * @param areaId
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    private void updateAreaChildren(Long areaId, String newAncestors, String oldAncestors) {
        List<WaterfeeArea> children = baseMapper.selectList(new LambdaQueryWrapper<WaterfeeArea>()
            .apply(DataBaseHelper.findInSet(areaId, "ancestors")));
        List<WaterfeeArea> list = new ArrayList<>();
        for (WaterfeeArea child : children) {
            WaterfeeArea waterfeeArea = new WaterfeeArea();
            waterfeeArea.setAreaId(child.getAreaId());
            waterfeeArea.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
            list.add(waterfeeArea);
        }
        if (CollUtil.isNotEmpty(list)) {
            if (baseMapper.updateBatchById(list)) {
                list.forEach(waterfeeArea -> CacheUtils.evict(CacheNames.SYS_DEPT, waterfeeArea.getAreaId()));
            }
        }
    }

    /**
     * 校验是否有数据权限
     *
     * @param areaId
     */
    @Override
    public void checkDataScope(Long areaId) {
        if (ObjectUtil.isNull(areaId)) {
            return;
        }
        if (LoginHelper.isSuperAdmin()) {
            return;
        }
        if (baseMapper.countAreaById(areaId) == 0) {
            throw new ServiceException("没有权限访问部门数据！");
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeArea entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除区域信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 获取区域下拉选择框数据
     *
     * @return 区域ID和名称的Map集合，key为areaId，value为areaName
     */
    @Override
    public Map<String, String> getSelectAreaMap() {
        // 查询所有可用的区域列表
        LambdaQueryWrapper<WaterfeeArea> lqw = Wrappers.lambdaQuery();
        lqw.eq(WaterfeeArea::getStatus, "0"); // 只查询状态正常的区域
        lqw.orderByAsc(WaterfeeArea::getOrderNum); // 按照显示顺序排序

        List<WaterfeeArea> areaList = baseMapper.selectList(lqw);

        // 转换为Map<String, String>格式，key为areaId，value为areaName
        Map<String, String> areaMap = new HashMap<>(areaList.size());
        for (WaterfeeArea area : areaList) {
            areaMap.put(area.getAreaId().toString(), area.getAreaName());
        }

        return areaMap;
    }

    /**
     * 根据多个区域ID查询区域信息
     *
     * @param areaIds 区域ID集合
     * @return 区域信息集合
     */
    @Override
    public List<WaterfeeAreaVo> queryByIds(Collection<Long> areaIds) {
        if (areaIds == null || areaIds.isEmpty()) {
            return new ArrayList<>();
        }
        return baseMapper.selectVoList(
            new LambdaQueryWrapper<WaterfeeArea>()
                .in(WaterfeeArea::getAreaId, areaIds)
                .eq(WaterfeeArea::getDelFlag, "0")
        );
    }
}
