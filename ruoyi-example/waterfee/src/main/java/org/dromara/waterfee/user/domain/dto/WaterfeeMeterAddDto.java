package org.dromara.waterfee.user.domain.dto;

import lombok.Data;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterBo;

@Data
public class WaterfeeMeterAddDto {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 水费表信息
     */
    WaterfeeMeterBo waterfeeMeterBo;

    /**
     *  是否已存在该水表信息，用于判断是否需要添加
     */
    boolean flag;
}
