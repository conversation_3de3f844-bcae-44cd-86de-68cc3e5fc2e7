package org.dromara.waterfee.user.domain.bo;

import org.dromara.waterfee.user.domain.WaterfeeUserTransferOwnershipRecord;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 用水用户过户记录业务对象 waterfee_user_transfer_ownership_record
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeUserTransferOwnershipRecord.class, reverseConvertGenerate = false)
public class WaterfeeUserTransferOwnershipRecordBo extends BaseEntity {

    /**
     * 主键
     */
    private Long transferId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 原用水人数
     */
    private Long beforeUseWaterNumber;

    /**
     * 原用水户名称
     */
    private String beforeUserName;

    /**
     * 原手机号码
     */
    private String beforePhoneNumber;

    /**
     * 原证件类型
     */
    private String beforeCertificateType;

    /**
     * 原证件号码
     */
    private String beforeCertificateNumber;

    /**
     * 原电子邮箱
     */
    private String beforeEmail;

    /**
     * 新用水人数
     */
    @NotNull(message = "新用水人数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long afterUseWaterNumber;

    /**
     * 新用水户名称
     */
    @NotBlank(message = "新用水户名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String afterUserName;

    /**
     * 新手机号码
     */
    @NotBlank(message = "新手机号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String afterPhoneNumber;

    /**
     * 新证件类型
     */
    @NotBlank(message = "新证件类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String afterCertificateType;

    /**
     * 新证件号码
     */
    @NotBlank(message = "新证件号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String afterCertificateNumber;

    /**
     * 新电子邮箱
     */
    private String afterEmail;


}
