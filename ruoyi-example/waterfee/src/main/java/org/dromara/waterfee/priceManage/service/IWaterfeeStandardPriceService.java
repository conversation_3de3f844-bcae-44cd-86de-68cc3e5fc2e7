package org.dromara.waterfee.priceManage.service;

import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.waterfee.priceManage.domain.WaterfeeStandardPrice;
import org.dromara.waterfee.priceManage.domain.bo.WaterfeeStandardPriceBo;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeeStandardPriceVo;

import java.util.Collection;
import java.util.List;

/**
 * 标准价格Service接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface IWaterfeeStandardPriceService {

    /**
     * 查询标准价格
     *
     * @param id 主键
     * @return 标准价格
     */
    WaterfeeStandardPrice queryById(Long id);

    /**
     * 分页查询标准价格列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 标准价格分页列表
     */
    TableDataInfo<WaterfeeStandardPriceVo> queryPageList(WaterfeeStandardPriceBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的标准价格列表
     *
     * @param bo 查询条件
     * @return 标准价格列表
     */
    List<WaterfeeStandardPriceVo> queryList(WaterfeeStandardPriceBo bo);

    /**
     * 新增标准价格
     *
     * @param bo 标准价格
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeStandardPriceBo bo);

    /**
     * 修改标准价格
     *
     * @param bo 标准价格
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeStandardPriceBo bo);

    /**
     * 校验并批量删除标准价格信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
