package org.dromara.waterfee.user.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.waterfee.user.domain.qo.RecordQo;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.user.domain.WaterfeeUserDeactivateRecord;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserDeactivateRecordVo;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserDeactivateRecordBo;
import org.dromara.waterfee.user.service.IWaterfeeUserDeactivateRecordService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 用水用户报停记录
 * 前端访问路由地址为:/waterfee/userDeactivateRecord
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/userDeactivateRecord")
public class WaterfeeUserDeactivateRecordController extends BaseController {

    private final IWaterfeeUserDeactivateRecordService waterfeeUserDeactivateRecordService;

    /**
     * 查询用水用户报停记录列表
     */
    @SaCheckPermission("waterfee:userDeactivateRecord:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeUserDeactivateRecordVo> list(RecordQo recordQo, PageQuery pageQuery) {
        return waterfeeUserDeactivateRecordService.queryPageList(recordQo, pageQuery);
    }

    /**
     * 导出用水用户报停记录列表
     */
    @SaCheckPermission("waterfee:userDeactivateRecord:export")
    @Log(title = "用水用户报停记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeUserDeactivateRecordBo bo, HttpServletResponse response) {
        List<WaterfeeUserDeactivateRecordVo> list = waterfeeUserDeactivateRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "用水用户报停记录", WaterfeeUserDeactivateRecordVo.class, response);
    }

    /**
     * 获取用水用户报停记录详细信息
     *
     * @param deactivateId 主键
     */
    @SaCheckPermission("waterfee:userDeactivateRecord:query")
    @GetMapping("/{deactivateId}")
    public R<WaterfeeUserDeactivateRecord> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("deactivateId") Long deactivateId) {
        return R.ok(waterfeeUserDeactivateRecordService.queryById(deactivateId));
    }

    /**
     * 新增用水用户报停记录
     */
    @SaCheckPermission("waterfee:userDeactivateRecord:add")
    @Log(title = "用水用户报停记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeUserDeactivateRecordBo bo) {
        return toAjax(waterfeeUserDeactivateRecordService.insertByBo(bo));
    }

    /**
     * 修改用水用户报停记录
     */
    @SaCheckPermission("waterfee:userDeactivateRecord:edit")
    @Log(title = "用水用户报停记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeUserDeactivateRecordBo bo) {
        return toAjax(waterfeeUserDeactivateRecordService.updateByBo(bo));
    }

    /**
     * 删除用水用户报停记录
     *
     * @param deactivateIds 主键串
     */
    @SaCheckPermission("waterfee:userDeactivateRecord:remove")
    @Log(title = "用水用户报停记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deactivateIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("deactivateIds") Long[] deactivateIds) {
        return toAjax(waterfeeUserDeactivateRecordService.deleteWithValidByIds(List.of(deactivateIds), true));
    }
}
