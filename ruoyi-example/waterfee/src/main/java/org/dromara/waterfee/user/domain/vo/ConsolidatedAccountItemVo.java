package org.dromara.waterfee.user.domain.vo;

import org.dromara.waterfee.user.domain.ConsolidatedAccountItem;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 合收户关系视图对象 consolidated_account_item
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ConsolidatedAccountItem.class)
public class ConsolidatedAccountItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long consolidatedAccountItemId;

    /**
     * 关联的合收户id
     */
    @ExcelProperty(value = "关联的合收户id")
    private Long consolidatedAccountId;

    /**
     * 关联的用户id
     */
    @ExcelProperty(value = "关联的用户id")
    private Long userId;


}
