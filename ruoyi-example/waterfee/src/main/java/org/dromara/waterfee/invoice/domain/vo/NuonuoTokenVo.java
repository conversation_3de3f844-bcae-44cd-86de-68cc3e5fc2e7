package org.dromara.waterfee.invoice.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 诺诺发票平台访问令牌视图对象
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
@ExcelIgnoreUnannotated
public class NuonuoTokenVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 令牌ID
     */
    private Long tokenId;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 过期时间（秒）
     */
    private Integer expiresIn;

    /**
     * 过期时间点
     */
    private Date expireTime;

    /**
     * 是否有效（0有效 1无效）
     */
    private String isValid;

    /**
     * 创建时间
     */
    private Date createTime;

}