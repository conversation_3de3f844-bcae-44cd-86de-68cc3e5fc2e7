package org.dromara.waterfee.meterRelation.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.waterfee.meterRelation.domain.WaterfeeMeterBalanceRecord;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:48
 **/

public interface WaterfeeMeterBalanceRecordMapper {

    int insert(WaterfeeMeterBalanceRecord record);

    List<WaterfeeMeterBalanceRecord> selectByParentAndTime(
        @Param("parentMeterId") Long parentMeterId,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime
    );

    int deleteByParentMeterId(@Param("parentMeterId") Long parentMeterId);
}
