package org.dromara.waterfee.meter.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.waterfee.meter.domain.MeterChangeRecord;

import java.util.Date;

/**
 * 水表更换记录业务对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MeterChangeRecord.class, reverseConvertGenerate = false)
public class MeterChangeRecordBo extends BaseEntity {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空", groups = {EditGroup.class})
    private Long changeId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 旧表ID
     */
    private Long oldMeterId;

    /**
     * 新表ID
     */
    private Long newMeterId;

    /**
     * 旧表编号
     */
    private String oldMeterNo;

    /**
     * 新表编号
     */
    private String newMeterNo;

    /**
     * 变更类型（1：安装 2：更换 3：拆除）
     */
    @NotNull(message = "变更类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer changeType;

    /**
     * 变更原因（1：到期更换 2：故障维修 3：用户申请 4：其他）
     */
    private Integer changeReason;

    /**
     * 旧表最终读数
     */
    private Double oldMeterReading;

    /**
     * 新表起始读数
     */
    private Double newMeterReading;

    /**
     * 变更时间
     */
    private Date changeTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核状态（0：未审核 1：已审核）
     */
    private String auditStatus;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 审核时间
     */
    private Date auditTime;

    // ========== 新表信息 ==========

    /**
     * 水表类型(1-机械表 2-智能表)
     */
    @NotNull(message = "水表类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer meterType;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 口径
     */
    private String caliber;

    /**
     * 精度
     */
    private String accuracy;

    /**
     * 安装地址
     */
    @Size(min = 0, max = 200, message = "安装地址长度不能超过200个字符")
    private String installAddress;

    /**
     * 通讯方式(NB-IoT、4G、Cat.1、LoRa)
     */
    private String communicationMode;

    /**
     * 阀控功能(0-无 1-有)
     */
    private Integer valveControl;

    /**
     * IMEI号
     */
    private String imei;

    /**
     * IMSI号
     */
    private String imsi;
}
