package org.dromara.waterfee.app.domain.vo;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 水表读数时间信息视图对象
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
public class MeterReadingTimeInfoVo implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 水表编号
   */
  private String meterNo;

  /**
   * 本期读表时间
   */
  private Date readingTime;

  /**
   * 上期读表时间
   */
  private Date lastReadingTime;
}