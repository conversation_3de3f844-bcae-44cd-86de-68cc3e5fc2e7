package org.dromara.waterfee.priceManage.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.waterfee.priceManage.domain.WaterfeeStandardPrice;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;



/**
 * 标准价格视图对象 waterfee_standard_price
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeStandardPrice.class)
public class WaterfeeStandardPriceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 价格名称
     */
    @ExcelProperty(value = "价格名称")
    private String name;

    /**
     * 价格
     */
    @ExcelProperty(value = "价格")
    private BigDecimal price;

    /**
     * 用水性质
     */
    @ExcelProperty(value = "用水性质")
    private String waterUseType;

    /**
     * 是否关联违约金
     */
    private Boolean penaltyEnabled;

    /**
     * 违约金ID
     */

    private Long penaltyId;
    /**
     * 违约金名称
     */
    @ExcelProperty(value = "违约金")
    private String penaltyName;
    /**
     * 是否关联附加费
     */
    private Boolean additionalEnabled;


    /**
     * 附加费ID列表（JSON数组）
     */
    private String additionalFeeIds;

    /**
     * 附加费名称列表（JSON数组）
     */
    @ExcelProperty(value = "附加费列表", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "J=SON数组")
    private String additionalFeeNames;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String description;


}
