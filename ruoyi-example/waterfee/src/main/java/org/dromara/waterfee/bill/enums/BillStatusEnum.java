package org.dromara.waterfee.bill.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账单状态枚举
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@Getter
@AllArgsConstructor
public enum BillStatusEnum {

    /**
     * 草稿
     */
    DRAFT("DRAFT", "草稿"),

    /**
     * 已发行
     */
    ISSUED("ISSUED", "已发行"),

    /**
     * 部分支付
     */
    PARTIALLY_PAID("PARTIALLY_PAID", "部分支付"),

    /**
     * 已支付
     */
    PAID("PAID", "已支付"),
    
    /**
     * 逾期
     */
    OVERDUE("OVERDUE", "逾期"),
    
    /**
     * 有争议
     */
    DISPUTED("DISPUTED", "有争议"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举实例
     */
    public static BillStatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (BillStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}