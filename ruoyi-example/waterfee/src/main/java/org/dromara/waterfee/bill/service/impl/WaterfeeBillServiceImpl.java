package org.dromara.waterfee.bill.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.waterfee.bill.domain.WaterfeeBill;
import org.dromara.waterfee.bill.domain.bo.WaterfeeBillBo;
import org.dromara.waterfee.bill.domain.vo.MeterBookBillSummaryVo;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.waterfee.bill.enums.BillStatusEnum;
import org.dromara.waterfee.bill.mapper.WaterfeeBillMapper;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 账单管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@RequiredArgsConstructor
@Service
public class WaterfeeBillServiceImpl implements IWaterfeeBillService {

    private final WaterfeeBillMapper baseMapper;

    /**
     * 查询账单
     *
     * @param billId 账单主键
     * @return 账单
     */
    @Override
    public WaterfeeBillVo queryById(Long billId) {
        return baseMapper.selectVoById(billId);
    }


    /**
     * 查询账单
     *
     * @param billNumber
     * @return 账单
     */
    @Override
    public WaterfeeBillVo queryByBillNumber(String billNumber) {
        LambdaQueryWrapper<WaterfeeBill> lqw = Wrappers.lambdaQuery();
        lqw.eq(WaterfeeBill::getBillNumber, billNumber);
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public List<WaterfeeBillVo> queryByIds(Collection<Long> billIds) {
        if (billIds == null || billIds.isEmpty()) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WaterfeeBill> lqw = Wrappers.lambdaQuery();
        lqw.in(WaterfeeBill::getBillId, billIds);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询账单列表
     *
     * @param bo        账单
     * @param pageQuery 分页参数
     * @return 账单
     */
    @Override
    public TableDataInfo<WaterfeeBillVo> queryPageList(WaterfeeBillBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeBill> lqw = buildQueryWrapper(bo);
        Page<WaterfeeBillVo> result = baseMapper.selectBillVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询账单列表
     *
     * @param bo        账单
     * @param pageQuery 分页参数
     * @return 账单
     */
    @Override
    public TableDataInfo<WaterfeeBillVo> queryPageListDetail(WaterfeeBillBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeBill> lqw = buildQueryWrapperIssueDetail(bo);
        Page<WaterfeeBillVo> result = baseMapper.selectBillVoPageDetail(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询账单列表
     *
     * @param bo 账单
     * @return 账单
     */
    @Override
    public List<WaterfeeBillVo> queryList(WaterfeeBillBo bo) {
        LambdaQueryWrapper<WaterfeeBill> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeBill> buildQueryWrapper(WaterfeeBillBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeBill> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBillId() != null, WaterfeeBill::getBillId, bo.getBillId());
        lqw.like(StringUtils.isNotBlank(bo.getBillNumber()), WaterfeeBill::getBillNumber, bo.getBillNumber());
        lqw.eq(bo.getCustomerId() != null, WaterfeeBill::getCustomerId, bo.getCustomerId());
        lqw.eq(bo.getMeterId() != null, WaterfeeBill::getMeterId, bo.getMeterId());
        lqw.eq(bo.getMeterBookId() != null, WaterfeeBill::getMeterBookId, bo.getMeterBookId());
        lqw.eq(bo.getPricePlanId() != null, WaterfeeBill::getPricePlanId, bo.getPricePlanId());
        lqw.eq(StringUtils.isNotBlank(bo.getBillStatus()), WaterfeeBill::getBillStatus, bo.getBillStatus());
        lqw.likeRight(StringUtils.isNotBlank(bo.getBillMonth()), WaterfeeBill::getBillMonth, bo.getBillMonth());

        // 用户编号、用户名称和用水地址等字段的查询条件在XML中处理
        // 这里只需要将这些字段设置到查询对象中
        if (StringUtils.isNotBlank(bo.getUserNo())) {
            lqw.getEntity().setUserNo(bo.getUserNo());
        }
        if (StringUtils.isNotBlank(bo.getUserName())) {
            lqw.getEntity().setUserName(bo.getUserName());
        }
        if (StringUtils.isNotBlank(bo.getAddress())) {
            lqw.getEntity().setAddress(bo.getAddress());
        }

        lqw.orderByDesc(WaterfeeBill::getCreateTime);
        return lqw;
    }

    private LambdaQueryWrapper<WaterfeeBill> buildQueryWrapperIssueDetail(WaterfeeBillBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeBill> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBillId() != null, WaterfeeBill::getBillId, bo.getBillId());
        lqw.like(StringUtils.isNotBlank(bo.getBillNumber()), WaterfeeBill::getBillNumber, bo.getBillNumber());
        lqw.eq(bo.getCustomerId() != null, WaterfeeBill::getCustomerId, bo.getCustomerId());
        lqw.eq(bo.getMeterId() != null, WaterfeeBill::getMeterId, bo.getMeterId());
        lqw.eq(bo.getMeterBookId() != null, WaterfeeBill::getMeterBookId, bo.getMeterBookId());
        lqw.eq(bo.getPricePlanId() != null, WaterfeeBill::getPricePlanId, bo.getPricePlanId());
        lqw.ne(WaterfeeBill::getBillStatus, "DRAFT");
        lqw.eq(StringUtils.isNotBlank(bo.getBillMonth()), WaterfeeBill::getBillMonth, bo.getBillMonth());

        // 用户编号、用户名称和用水地址等字段的查询条件在XML中处理
        // 这里只需要将这些字段设置到查询对象中
        if (StringUtils.isNotBlank(bo.getUserNo())) {
            lqw.getEntity().setUserNo(bo.getUserNo());
        }
        if (StringUtils.isNotBlank(bo.getUserName())) {
            lqw.getEntity().setUserName(bo.getUserName());
        }
        if (StringUtils.isNotBlank(bo.getAddress())) {
            lqw.getEntity().setAddress(bo.getAddress());
        }

        lqw.orderByDesc(WaterfeeBill::getCreateTime);
        return lqw;
    }

    /**
     * 新增账单
     */
    @Override
    public Boolean insertByBo(WaterfeeBillBo bo) {
        WaterfeeBill add = MapstructUtils.convert(bo, WaterfeeBill.class);
        validEntityBeforeSave(add);

        // 设置默认值
        if (add.getConsumptionUnit() == null) {
            add.setConsumptionUnit("m3");
        }
        if (add.getBillStatus() == null) {
            add.setBillStatus(BillStatusEnum.DRAFT.getCode());
        }
        if (add.getBaseChargeAmount() == null) {
            add.setBaseChargeAmount(BigDecimal.ZERO);
        }
        if (add.getAdjustmentsAmount() == null) {
            add.setAdjustmentsAmount(BigDecimal.ZERO);
        }
        if (add.getAmountPaid() == null) {
            add.setAmountPaid(BigDecimal.ZERO);
        }
        if (add.getWaterBillOnly() == null) {
            add.setWaterBillOnly(BigDecimal.ZERO);
        }
        if (add.getWaterResourceTax() == null) {
            add.setWaterResourceTax(BigDecimal.ZERO);
        }
        if (add.getSewageTreatmentFee() == null) {
            add.setSewageTreatmentFee(BigDecimal.ZERO);
        }

        // 计算应付余额
        if (add.getTotalAmount() != null) {
            add.setBalanceDue(add.getTotalAmount().subtract(add.getAmountPaid()));
        }

        baseMapper.insert(add);
        bo.setBillId(add.getBillId());
        return true;
    }

    /**
     * 修改账单
     */
    @Override
    public Boolean updateByBo(WaterfeeBillBo bo) {
        WaterfeeBill update = MapstructUtils.convert(bo, WaterfeeBill.class);
        validEntityBeforeSave(update);

        // 计算应付余额
        if (update.getTotalAmount() != null && update.getAmountPaid() != null) {
            update.setBalanceDue(update.getTotalAmount().subtract(update.getAmountPaid()));
        }

        // 确保分项费用字段不为空
        if (update.getWaterBillOnly() == null) {
            update.setWaterBillOnly(BigDecimal.ZERO);
        }
        if (update.getWaterResourceTax() == null) {
            update.setWaterResourceTax(BigDecimal.ZERO);
        }
        if (update.getSewageTreatmentFee() == null) {
            update.setSewageTreatmentFee(BigDecimal.ZERO);
        }

        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeBill entity) {
        // 校验账单编号唯一性
        if (StringUtils.isNotEmpty(entity.getBillNumber())) {
            LambdaQueryWrapper<WaterfeeBill> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(WaterfeeBill::getBillNumber, entity.getBillNumber());
            if (entity.getBillId() != null) {
                queryWrapper.ne(WaterfeeBill::getBillId, entity.getBillId());
            }
            long count = baseMapper.selectCount(queryWrapper);
            if (count > 0) {
                throw new RuntimeException("账单编号已存在");
            }
        }
    }

    /**
     * 批量删除账单
     *
     * @param ids     需要删除的账单主键
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验账单状态，只有草稿状态的账单才能删除
            LambdaQueryWrapper<WaterfeeBill> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(WaterfeeBill::getBillId, ids);
            queryWrapper.notIn(WaterfeeBill::getBillStatus, BillStatusEnum.DRAFT.getCode(), BillStatusEnum.CANCELLED.getCode());
            long count = baseMapper.selectCount(queryWrapper);
            if (count > 0) {
                throw new RuntimeException("只有草稿或已取消状态的账单才能删除");
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 调整账单用量
     *
     * @param bo 账单信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean adjustConsumption(WaterfeeBillBo bo) {
        if (bo.getBillId() == null || bo.getAdjustmentsAmount() == null) {
            throw new RuntimeException("账单ID和调整金额不能为空");
        }

        // 查询原账单信息
        WaterfeeBill bill = baseMapper.selectById(bo.getBillId());
        if (bill == null) {
            throw new RuntimeException("账单不存在");
        }

        // 检查账单状态，只有特定状态的账单才能调整
        if (!BillStatusEnum.DRAFT.getCode().equals(bill.getBillStatus()) && !BillStatusEnum.ISSUED.getCode().equals(bill.getBillStatus())) {
            throw new RuntimeException("只有草稿或已发行状态的账单才能调整金额");
        }

        bill.setNotes(StringUtils.isBlank(bill.getNotes()) ?
            "金额调整原因: " + bo.getAdjustmentsAmount() + " " + bo.getAdjustmentReason() :
            bill.getNotes() + "\n金额调整原因: " + bo.getAdjustmentsAmount() + " " + bo.getAdjustmentReason());


        // 更新总金额和应付余额
        BigDecimal oldTotal = bill.getTotalAmount();
        BigDecimal newTotal = oldTotal.add(bo.getAdjustmentsAmount());
        bill.setAdjustmentsAmount(bo.getAdjustmentsAmount());
        bill.setTotalAmount(newTotal);
        bill.setBalanceDue(newTotal.subtract(bill.getAmountPaid()));

        bill.setUpdateBy(LoginHelper.getUserId());
        bill.setUpdateTime(DateUtils.getNowDate());

        return baseMapper.updateById(bill) > 0;
    }

    /**
     * 支付账单
     *
     * @param billId 账单ID
     * @param amount 支付金额
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean payBill(Long billId, BigDecimal amount) {
        if (billId == null) {
            throw new ServiceException("账单ID不能为空");
        }

        if (amount == null || amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("支付金额必须>=0");
        }

        // 查询账单信息
        WaterfeeBill bill = baseMapper.selectById(billId);
        if (bill == null) {
            throw new ServiceException("账单不存在：" + billId);
        }

        // 检查账单状态
        if (!"ISSUED".equals(bill.getBillStatus()) && !"PARTIAL_PAID".equals(bill.getBillStatus())) {
            throw new ServiceException("账单状态不正确，无法支付");
        }

        // 检查支付金额
        BigDecimal balanceDue = bill.getBalanceDue() != null ? bill.getBalanceDue() : BigDecimal.ZERO;
        if (amount.compareTo(balanceDue) > 0) {
            throw new ServiceException("支付金额不能大于应付余额");
        }

        // 更新已支付金额
        BigDecimal currentAmountPaid = bill.getAmountPaid() != null ? bill.getAmountPaid() : BigDecimal.ZERO;
        BigDecimal newAmountPaid = currentAmountPaid.add(amount);
        bill.setAmountPaid(newAmountPaid);

        // 更新应付余额
        BigDecimal newBalanceDue = bill.getTotalAmount().subtract(newAmountPaid);
        bill.setBalanceDue(newBalanceDue);

        // 更新账单状态
        if (newBalanceDue.compareTo(BigDecimal.ZERO) <= 0) {
            bill.setBillStatus("PAID"); // 已支付
        } else if (newAmountPaid.compareTo(BigDecimal.ZERO) > 0) {
            bill.setBillStatus("PARTIAL_PAID"); // 部分支付
        }

        bill.setUpdateBy(LoginHelper.getUserId());
        bill.setUpdateTime(DateUtils.getNowDate());

        return baseMapper.updateById(bill) > 0;
    }

    /**
     * 查询待发行账单列表
     *
     * @param bo        账单查询条件
     * @param pageQuery 分页参数
     * @return 待发行账单集合
     */
    @Override
    public TableDataInfo<WaterfeeBillVo> queryPendingIssueList(WaterfeeBillBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeBill> lqw = buildQueryWrapper(bo);
        // 只查询草稿状态的账单
        lqw.eq(WaterfeeBill::getBillStatus, BillStatusEnum.DRAFT.getCode());
        Page<WaterfeeBillVo> result = baseMapper.selectBillVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 发行账单
     *
     * @param meterBookIds 需要发行的账单ID集合
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean issueBills(Collection<Long> meterBookIds) {
        if (meterBookIds == null || meterBookIds.isEmpty()) {
            throw new RuntimeException("表册ID不能为空");
        }

        // 查询账单信息
        LambdaQueryWrapper<WaterfeeBill> draftQueryWrapper = Wrappers.lambdaQuery();
        draftQueryWrapper.in(WaterfeeBill::getMeterBookId, meterBookIds);
        draftQueryWrapper.eq(WaterfeeBill::getBillStatus, BillStatusEnum.DRAFT.getCode());
        List<WaterfeeBill> draftBills = baseMapper.selectList(draftQueryWrapper);

        // 判断是否有草稿账单可发行
        if (draftBills.isEmpty()) {
            throw new RuntimeException("没有待发行的账单");
        }

        // 批量更新账单状态为已发行
        Date now = DateUtils.getNowDate();
        Long userId = LoginHelper.getUserId();

        List<Long> billIds = baseMapper.selectList(Wrappers.lambdaQuery(WaterfeeBill.class)
                .select(WaterfeeBill::getBillId)
                .in(WaterfeeBill::getMeterBookId, meterBookIds)
                .eq(WaterfeeBill::getBillStatus, BillStatusEnum.DRAFT.getCode()))
            .stream()
            .map(WaterfeeBill::getBillId)
            .toList();

        List<WaterfeeBill> bills = baseMapper.selectBatchIds(billIds);
        for (WaterfeeBill bill : bills) {
            bill.setBillStatus(BillStatusEnum.ISSUED.getCode());
            bill.setBillingIssueDate(now);
            bill.setUpdateBy(userId);
            bill.setUpdateTime(now);
        }

        return baseMapper.updateBatchById(bills);
    }

    /**
     * 查询按表册分组的账单汇总信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 表册账单汇总信息分页列表
     */
    @Override
    public TableDataInfo<MeterBookBillSummaryVo> queryMeterBookBillSummaryPageList(WaterfeeBillBo bo, PageQuery pageQuery) {
        // 清除用户相关的查询条件，因为汇总信息不需要按用户过滤
        bo.setUserNo(null);
        bo.setUserName(null);
        bo.setAddress(null);

        LambdaQueryWrapper<WaterfeeBill> lqw = buildQueryWrapper(bo);
        Page<MeterBookBillSummaryVo> page = baseMapper.selectMeterBookBillSummaryPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }
}
