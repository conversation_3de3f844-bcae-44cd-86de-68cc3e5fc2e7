package org.dromara.waterfee.priceManage.domain.vo;

import org.dromara.waterfee.priceManage.domain.WaterfeeSurchargeConfigs;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 附加费配置 (Surcharge Configurations)视图对象 waterfee_surcharge_configs
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeSurchargeConfigs.class)
public class WaterfeeSurchargeConfigsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 附加费名称
     */
    @ExcelProperty(value = "附加费名称")
    private String name;

    /**
     * 计算方式 (e.g., fixed_amount, meter_reading)
     */
    @ExcelProperty(value = "计算方式 (e.g., fixed_amount, meter_reading)", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "calculation_method")
    private String calculationMethod;

    /**
     * 固定金额 (if calculation_method is fixed_amount)
     */
    @ExcelProperty(value = "固定金额 (if calculation_method is fixed_amount)")
    private Long fixedAmount;

    /**
     * 比例(%) (e.g., if calculation_method is meter_reading)
     */
    @ExcelProperty(value = "比例(%) (e.g., if calculation_method is meter_reading)")
    private Long ratePercent;

    /**
     * 附加费类别
     */
    @ExcelProperty(value = "附加费类别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "collection_method")
    private String category;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;


}
