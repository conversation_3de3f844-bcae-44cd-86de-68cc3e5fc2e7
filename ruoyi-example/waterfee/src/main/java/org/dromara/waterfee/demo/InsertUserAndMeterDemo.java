package org.dromara.waterfee.demo;

import org.dromara.common.core.domain.R;
import org.dromara.waterfee.counter.domain.bo.WaterfeeDepositBo;
import org.dromara.waterfee.counter.service.IWaterfeeCounterPaymentService;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterBo;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserBo;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Date;

import static org.dromara.waterfee.demo.utils.RandomDataGenerator.*;

/**
 * 插入用户和水表数据的演示程序
 */
@RestController
@RequestMapping("/demo")
public class InsertUserAndMeterDemo {

    @Autowired
    private IWaterfeeUserService waterfeeUserService;

    @Autowired
    private IWaterfeeMeterService waterfeeMeterService;

    @Autowired
    private IWaterfeeCounterPaymentService counterPaymentService;

    @PostMapping("/insertUserAndMeter")
    @Transactional(rollbackFor = Exception.class)
    public R<Void> insertUserAndMeter() {
        // 创建用户数据
        WaterfeeUserBo userBo = new WaterfeeUserBo();
        String randomUserName = generateRandomUserName();
        userBo.setUserName(randomUserName); // 使用随机生成的用户名
        userBo.setPhoneNumber(generateRandomPhoneNumber()); // 使用随机生成的手机号码
        userBo.setCertificateType("identity_card"); // 身份证
        userBo.setCertificateNumber(generateRandomIdCard()); // 使用随机生成的身份证号码
        userBo.setUserStatus("normal"); // 正常状态
        userBo.setAddress(generateRandomAddress()); // 使用随机生成的地址
        userBo.setSupplyDate(new Date());
        userBo.setAreaId(1905058024441294849L);
        userBo.setCommunityId(1L);
        userBo.setUseWaterNature("resident"); // 居民用水
        userBo.setUseWaterNumber(3L);
        userBo.setInvoiceType("VAT_invoice"); // 增值税发票
        userBo.setPriceUseWaterNature("resident"); // 居民水
        userBo.setBillingMethod("1933328427904176130"); //关联阶梯


        // 插入用户
        Boolean userResult = waterfeeUserService.insertByBo(userBo);
        if (userResult) {
            System.out.println("用户插入成功，用户ID: " + userBo.getUserId() + "，用户名: " + randomUserName);

            // 创建水表数据
            WaterfeeMeterBo meterBo = new WaterfeeMeterBo();
            meterBo.setMeterNo(generateRandomMeterNo()); // 生成唯一水表编号
            meterBo.setMeterType(2); // 水表类型为2
            meterBo.setBusinessAreaId(1905058024441294849L);
            meterBo.setMeterBookId(2L); // 表册ID为2
            meterBo.setUserId(userBo.getUserId()); // 关联用户
            meterBo.setUserNo(userBo.getUserNo()); // 关联用户编号
            meterBo.setInstallDate(new Date());
            meterBo.setInstallAddress(userBo.getAddress()); // 使用用户地址作为安装地址


            // 插入水表
            Boolean meterResult = waterfeeMeterService.insertByBo(meterBo);
            if (meterResult) {
                System.out.println("水表创建成功，水表编号: " + meterBo.getMeterNo());
                System.out.println("用户与水表关联完成");

                // 为新用户充值10000元
                try {
                    WaterfeeDepositBo depositBo = new WaterfeeDepositBo();
                    depositBo.setUserId(userBo.getUserId());
                    depositBo.setAmount(new BigDecimal("10000.00")); // 充值10000元
                    depositBo.setPaymentMethod("CASH"); // 现金支付
                    depositBo.setRemark("新用户注册赠送");
                    depositBo.setTollCollector("系统自动");

                    Boolean depositResult = counterPaymentService.addDeposit(depositBo);
                    if (depositResult) {
                        System.out.println("用户充值成功，充值金额: 10000.00元");
                        return R.ok("Mock数据插入成功：用户[" + randomUserName + "]和水表[" + meterBo.getMeterNo() + "]创建完成，已自动充值10000元");
                    } else {
                        System.out.println("用户充值失败");
                        return R.ok("Mock数据插入成功：用户[" + randomUserName + "]和水表[" + meterBo.getMeterNo() + "]创建完成，但充值失败");
                    }
                } catch (Exception e) {
                    System.out.println("用户充值异常: " + e.getMessage());
                    return R.ok("Mock数据插入成功：用户[" + randomUserName + "]和水表[" + meterBo.getMeterNo() + "]创建完成，但充值异常: " + e.getMessage());
                }
            } else {
                System.out.println("水表创建失败");
                return R.fail("水表创建失败");
            }
        } else {
            System.out.println("用户插入失败");
            return R.fail("用户插入失败");
        }
    }
}
