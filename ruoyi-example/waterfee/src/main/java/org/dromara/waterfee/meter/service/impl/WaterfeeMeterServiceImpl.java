package org.dromara.waterfee.meter.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.waterfee.meter.domain.WaterfeeMeter;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterBo;
import org.dromara.waterfee.meter.domain.vo.IntelligentMeterImportVo;
import org.dromara.waterfee.meter.domain.vo.MechanicalMeterImportVo;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meter.mapper.WaterfeeMeterMapper;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 水表信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@RequiredArgsConstructor
@Service
public class WaterfeeMeterServiceImpl extends ServiceImpl<WaterfeeMeterMapper, WaterfeeMeter>
    implements IWaterfeeMeterService {

    private final WaterfeeMeterMapper baseMapper;
    private final JdbcTemplate jdbcTemplate;
    private static final Logger log = LoggerFactory.getLogger(WaterfeeMeterServiceImpl.class);

    /**
     * 查询水表信息
     *
     * @param meterId 主键
     * @return 水表信息
     */
    @Override
    public WaterfeeMeterVo queryById(Long meterId) {
        return baseMapper.selectVoById(meterId);
    }

    /**
     * 查询水表信息列表
     *
     * @param bo 水表信息
     * @return 水表信息
     */
    @Override
    public TableDataInfo<WaterfeeMeterVo> queryPageList(WaterfeeMeterBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeMeter> lqw = buildQueryWrapper(bo);
        Page<WaterfeeMeterVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 补充手册信息、用户信息和区域信息
        enrichMeterBookInfo(result.getRecords());
        enrichUserInfo(result.getRecords());
        enrichAreaInfo(result.getRecords());

        return TableDataInfo.build(result);
    }

    /**
     * 查询水表信息列表
     *
     * @param bo 水表信息
     * @return 水表信息
     */
    @Override
    public List<WaterfeeMeterVo> queryList(WaterfeeMeterBo bo) {
        LambdaQueryWrapper<WaterfeeMeter> lqw = buildQueryWrapper(bo);
        List<WaterfeeMeterVo> list = baseMapper.selectVoList(lqw);

        // 补充手册信息、用户信息和区域信息
        enrichMeterBookInfo(list);
        enrichUserInfo(list);
        enrichAreaInfo(list);

        return list;
    }

    private LambdaQueryWrapper<WaterfeeMeter> buildQueryWrapper(WaterfeeMeterBo bo) {
        LambdaQueryWrapper<WaterfeeMeter> lqw = Wrappers.lambdaQuery();
        // ID精确查询
        lqw.eq(bo.getMeterId() != null, WaterfeeMeter::getMeterId, bo.getMeterId());

        // 水表编号查询 - 支持精确和模糊
        lqw.like(StringUtils.isNotBlank(bo.getMeterNoLike()), WaterfeeMeter::getMeterNo, bo.getMeterNoLike());
        lqw.like(StringUtils.isNotBlank(bo.getMeterNo()), WaterfeeMeter::getMeterNo, bo.getMeterNo());

        // 水表类型(精确)
        lqw.eq(bo.getMeterType() != null, WaterfeeMeter::getMeterType, bo.getMeterType());

        // 生产厂家查询 - 支持精确和模糊
        lqw.like(StringUtils.isNotBlank(bo.getManufacturerLike()), WaterfeeMeter::getManufacturer,
            bo.getManufacturerLike());
        lqw.eq(StringUtils.isNotBlank(bo.getManufacturer()), WaterfeeMeter::getManufacturer, bo.getManufacturer());

        // 水表类别查询(精确)
        lqw.eq(StringUtils.isNotBlank(bo.getMeterCategory()), WaterfeeMeter::getMeterCategory, bo.getMeterCategory());

        // 水表分类查询(精确)
        lqw.eq(StringUtils.isNotBlank(bo.getMeterClassification()), WaterfeeMeter::getMeterClassification,
            bo.getMeterClassification());

        // 计量用途查询(精确)
        lqw.eq(StringUtils.isNotBlank(bo.getMeasurementPurpose()), WaterfeeMeter::getMeasurementPurpose,
            bo.getMeasurementPurpose());

        // 口径查询 - 支持精确和模糊
        lqw.like(StringUtils.isNotBlank(bo.getCaliberLike()), WaterfeeMeter::getCaliber, bo.getCaliberLike());
        lqw.eq(StringUtils.isNotBlank(bo.getCaliber()), WaterfeeMeter::getCaliber, bo.getCaliber());

        // 精度查询 - 支持精确和模糊
        lqw.like(StringUtils.isNotBlank(bo.getAccuracyLike()), WaterfeeMeter::getAccuracy, bo.getAccuracyLike());
        lqw.eq(StringUtils.isNotBlank(bo.getAccuracy()), WaterfeeMeter::getAccuracy, bo.getAccuracy());

        // 初始读数 - 支持范围查询
        lqw.ge(bo.getInitialReadingStart() != null, WaterfeeMeter::getInitialReading, bo.getInitialReadingStart());
        lqw.le(bo.getInitialReadingEnd() != null, WaterfeeMeter::getInitialReading, bo.getInitialReadingEnd());

        // 安装日期 - 支持范围查询
        lqw.ge(bo.getInstallDateStart() != null, WaterfeeMeter::getInstallDate, bo.getInstallDateStart());
        lqw.le(bo.getInstallDateEnd() != null, WaterfeeMeter::getInstallDate, bo.getInstallDateEnd());

        // 业务区域ID(精确)
        lqw.eq(bo.getBusinessAreaId() != null, WaterfeeMeter::getBusinessAreaId, bo.getBusinessAreaId());

        // 安装地址查询 - 支持精确和模糊
        lqw.like(StringUtils.isNotBlank(bo.getInstallAddressLike()), WaterfeeMeter::getInstallAddress,
            bo.getInstallAddressLike());
        lqw.eq(StringUtils.isNotBlank(bo.getInstallAddress()), WaterfeeMeter::getInstallAddress,
            bo.getInstallAddress());

        // 表倍率 - 支持范围查询
        lqw.ge(bo.getMeterRatioStart() != null, WaterfeeMeter::getMeterRatio, bo.getMeterRatioStart());
        lqw.le(bo.getMeterRatioEnd() != null, WaterfeeMeter::getMeterRatio, bo.getMeterRatioEnd());

        // 通讯方式查询 - 支持精确和模糊
        lqw.like(StringUtils.isNotBlank(bo.getCommunicationModeLike()), WaterfeeMeter::getCommunicationMode,
            bo.getCommunicationModeLike());
        lqw.eq(StringUtils.isNotBlank(bo.getCommunicationMode()), WaterfeeMeter::getCommunicationMode,
            bo.getCommunicationMode());

        // 阀控功能(精确)
        lqw.eq(bo.getValveControl() != null, WaterfeeMeter::getValveControl, bo.getValveControl());

        // IMEI号查询 - 支持模糊
        lqw.like(StringUtils.isNotBlank(bo.getImeiLike()), WaterfeeMeter::getImei, bo.getImeiLike());
        lqw.eq(StringUtils.isNotBlank(bo.getImei()), WaterfeeMeter::getImei, bo.getImei());

        // IMSI号查询 - 支持模糊
        lqw.like(StringUtils.isNotBlank(bo.getImsiLike()), WaterfeeMeter::getImsi, bo.getImsiLike());
        lqw.eq(StringUtils.isNotBlank(bo.getImsi()), WaterfeeMeter::getImsi, bo.getImsi());

        // 物联网平台 - 支持精确和模糊
        lqw.like(StringUtils.isNotBlank(bo.getIotPlatformLike()), WaterfeeMeter::getIotPlatform,
            bo.getIotPlatformLike());
        lqw.eq(StringUtils.isNotBlank(bo.getIotPlatform()), WaterfeeMeter::getIotPlatform, bo.getIotPlatform());

        // 是否预付费(精确)
        lqw.eq(bo.getPrepaid() != null, WaterfeeMeter::getPrepaid, bo.getPrepaid());

        // 用户关联查询 - 支持精确和模糊
        lqw.eq(bo.getUserId() != null, WaterfeeMeter::getUserId, bo.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getUserNoLike()), WaterfeeMeter::getUserNo, bo.getUserNoLike());
        lqw.like(StringUtils.isNotBlank(bo.getUserNo()), WaterfeeMeter::getUserNo, bo.getUserNo());

        // 特殊处理：用户关联状态查询
        if (bo.getUserId() != null) {
            if (bo.getUserId() == 0) {
                // 特殊处理：userId=0表示查询所有未关联用户的水表
                lqw.isNull(WaterfeeMeter::getUserId);
            } else if (bo.getUserId() == -1) {
                // 特殊处理：userId=-1表示查询所有已关联用户的水表
                lqw.isNotNull(WaterfeeMeter::getUserId);
            }
        }

        return lqw;
    }

    /**
     * 新增水表信息
     *
     * @param bo 水表信息
     * @return 结果
     */
    @Override
    public Boolean insertByBo(WaterfeeMeterBo bo) {
        WaterfeeMeter add = BeanUtil.toBean(bo, WaterfeeMeter.class);
        // 设置安装日期
        if (add.getInstallDate() == null) {
            add.setInstallDate(new Date());
        }
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMeterId(add.getMeterId());
        }
        return flag;
    }

    /**
     * 修改水表信息
     *
     * @param bo 水表信息
     * @return 结果
     */
    @Override
    public Boolean updateByBo(WaterfeeMeterBo bo) {
        WaterfeeMeter update = BeanUtil.toBean(bo, WaterfeeMeter.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(WaterfeeMeter entity) {
        // 校验逻辑
    }

    /**
     * 批量删除水表信息
     *
     * @param ids 需要删除的水表信息主键
     * @return 结果
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验是否允许删除
            // 可以添加关联检查，例如检查是否有关联的用水户
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    /**
     * 关联用水户
     *
     * @param meterId 水表ID
     * @param userId  用水户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean associateUser(Long meterId, Long userId) {
        WaterfeeMeter meter = baseMapper.selectById(meterId);
        if (meter == null) {
            throw new RuntimeException("水表不存在");
        }

        // 查询用户信息
        String sql = "SELECT user_no, user_name FROM waterfee_user WHERE user_id = ? AND tenant_id = ? AND del_flag = '0'";
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, userId, LoginHelper.getTenantId());
        if (results.isEmpty()) {
            throw new RuntimeException("用户不存在");
        }

        String userNo = results.get(0).get("user_no").toString();
        String userName = results.get(0).get("user_name") != null ? results.get(0).get("user_name").toString() : "";

        // 更新水表关联的用户
        meter.setUserId(userId);
        meter.setUserNo(userNo);
        meter.setUpdateBy(LoginHelper.getUserId());
        meter.setUpdateTime(new Date());

        return baseMapper.updateById(meter) > 0;
    }

    /**
     * 关联抄表手册
     *
     * @param meterId     水表ID
     * @param meterBookId 抄表手册ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean associateMeterBook(Long meterId, Long meterBookId) {
        WaterfeeMeter meter = baseMapper.selectById(meterId);
        if (meter == null) {
            throw new RuntimeException("水表不存在");
        }

        // 查询抄表手册信息
        String sql = "SELECT id FROM waterfee_meter_book WHERE id = ? AND tenant_id = ? AND del_flag = '0'";
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, meterBookId, LoginHelper.getTenantId());
        if (results.isEmpty()) {
            throw new RuntimeException("抄表手册不存在");
        }

        // 更新水表关联的抄表手册
        meter.setMeterBookId(meterBookId);
        meter.setUpdateBy(LoginHelper.getUserId());
        meter.setUpdateTime(new Date());

        return baseMapper.updateById(meter) > 0;
    }

    @Override
    public WaterfeeMeterVo queryByNo(String meterNo) {
        LambdaQueryWrapper<WaterfeeMeter> lqw = Wrappers.lambdaQuery();
        lqw.eq(WaterfeeMeter::getMeterNo, meterNo);
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 补充抄表手册信息
     *
     * @param list 水表列表
     */
    private void enrichMeterBookInfo(List<WaterfeeMeterVo> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        // 收集需要查询的手册ID
        List<Long> bookIds = list.stream()
            .filter(meter -> meter.getMeterBookId() != null)
            .map(WaterfeeMeterVo::getMeterBookId)
            .distinct()
            .collect(Collectors.toList());

        if (!bookIds.isEmpty()) {
            try {
                // 批量查询手册信息
                String sql = "SELECT id, book_name FROM waterfee_meter_book WHERE id IN (:ids) AND tenant_id = :tenantId AND del_flag = '0'";
                Map<String, Object> params = new HashMap<>();
                params.put("ids", bookIds);
                params.put("tenantId", LoginHelper.getTenantId());

                // 使用命名参数查询
                NamedParameterJdbcTemplate namedJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
                List<Map<String, Object>> bookResults = namedJdbcTemplate.queryForList(sql, params);

                // 构建手册ID到名称的映射
                Map<Long, String> bookMap = new HashMap<>();
                for (Map<String, Object> book : bookResults) {
                    bookMap.put(((Number) book.get("id")).longValue(), (String) book.get("book_name"));
                }

                // 为每个水表设置手册名称
                for (WaterfeeMeterVo meter : list) {
                    if (meter.getMeterBookId() != null && bookMap.containsKey(meter.getMeterBookId())) {
                        meter.setMeterBookName(bookMap.get(meter.getMeterBookId()));
                    }
                }
            } catch (Exception e) {
                log.error("补充抄表手册信息失败", e);
            }
        }
    }

    /**
     * 补充用户信息
     *
     * @param list 水表列表
     */
    private void enrichUserInfo(List<WaterfeeMeterVo> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        // 收集需要查询的用户ID
        List<Long> userIds = list.stream()
            .filter(meter -> meter.getUserId() != null)
            .map(WaterfeeMeterVo::getUserId)
            .distinct()
            .collect(Collectors.toList());

        if (!userIds.isEmpty()) {
            try {
                // 批量查询用户信息 - 使用正确的表名和字段名
                String sql = "SELECT user_id, user_name, user_no FROM waterfee_user WHERE user_id IN (:ids) AND tenant_id = :tenantId AND del_flag = '0'";
                Map<String, Object> params = new HashMap<>();
                params.put("ids", userIds);
                params.put("tenantId", LoginHelper.getTenantId());

                // 使用命名参数查询
                NamedParameterJdbcTemplate namedJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
                List<Map<String, Object>> userResults = namedJdbcTemplate.queryForList(sql, params);

                // 构建用户ID到名称和编号的映射
                Map<Long, Map<String, String>> userMap = new HashMap<>();
                for (Map<String, Object> user : userResults) {
                    long userId = ((Number) user.get("user_id")).longValue();
                    Map<String, String> userInfo = new HashMap<>();
                    userInfo.put("userName", (String) user.get("user_name"));
                    userInfo.put("userNo", (String) user.get("user_no"));
                    userMap.put(userId, userInfo);
                }

                // 为每个水表设置用户信息
                for (WaterfeeMeterVo meter : list) {
                    if (meter.getUserId() != null && userMap.containsKey(meter.getUserId())) {
                        Map<String, String> userInfo = userMap.get(meter.getUserId());
                        meter.setUserName(userInfo.get("userName"));
                        // 如果userNo为空，则使用查询到的值
                        if (StringUtils.isBlank(meter.getUserNo())) {
                            meter.setUserNo(userInfo.get("userNo"));
                        }
                    }
                }
            } catch (Exception e) {
                log.error("补充用户信息失败", e);
                // 输出详细错误信息以便调试
                log.error("详细错误:", e);
            }
        }
    }

    /**
     * 补充业务区域信息
     *
     * @param list 水表列表
     */
    private void enrichAreaInfo(List<WaterfeeMeterVo> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        // 收集需要查询的区域ID
        List<Long> areaIds = list.stream()
            .filter(meter -> meter.getBusinessAreaId() != null)
            .map(WaterfeeMeterVo::getBusinessAreaId)
            .distinct()
            .collect(Collectors.toList());

        if (!areaIds.isEmpty()) {
            try {
                // 批量查询区域信息
                String sql = "SELECT area_id, area_name FROM waterfee_area WHERE area_id IN (:ids) AND tenant_id = :tenantId AND del_flag = '0'";
                Map<String, Object> params = new HashMap<>();
                params.put("ids", areaIds);
                params.put("tenantId", LoginHelper.getTenantId());

                // 使用命名参数查询
                NamedParameterJdbcTemplate namedJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
                List<Map<String, Object>> areaResults = namedJdbcTemplate.queryForList(sql, params);

                // 构建区域ID到名称的映射
                Map<Long, String> areaMap = new HashMap<>();
                for (Map<String, Object> area : areaResults) {
                    areaMap.put(((Number) area.get("area_id")).longValue(), (String) area.get("area_name"));
                }

                // 为每个水表设置区域名称
                for (WaterfeeMeterVo meter : list) {
                    if (meter.getBusinessAreaId() != null && areaMap.containsKey(meter.getBusinessAreaId())) {
                        meter.setBusinessAreaName(areaMap.get(meter.getBusinessAreaId()));
                    }
                }
            } catch (Exception e) {
                log.error("补充业务区域信息失败", e);
                log.error("详细错误:", e);
            }
        }
    }

    /**
     * 导入水表数据
     *
     * @param importList 导入数据列表
     * @param meterType  水表类型
     * @return 导入结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, Object> importMeter(List<?> importList, Integer meterType) {
        if (CollUtil.isEmpty(importList)) {
            throw new ServiceException("导入数据不能为空！");
        }

        Map<String, Object> result = new HashMap<>(4);
        List<WaterfeeMeter> meterList = new ArrayList<>();
        List<String> failureMsg = new ArrayList<>();
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        for (Object item : importList) {
            try {
                WaterfeeMeter meter = new WaterfeeMeter();

                // 根据水表类型处理不同的导入VO
                if (meterType == 2 && item instanceof IntelligentMeterImportVo vo) {
                    // 智能表数据转换
                    meter.setMeterNo(vo.getMeterNo());
                    meter.setManufacturer(vo.getManufacturer());
                    meter.setCaliber(vo.getCaliber());
                    meter.setAccuracy(vo.getAccuracy());
                    meter.setInstallAddress(vo.getInstallLocation());
                    meter.setInitialReading(new BigDecimal(vo.getInitialReading()));
                    meter.setMeterType(2);
                    // 智能表特有字段
                    meter.setCommunicationMode(vo.getProtocol());
                    // 其他智能表特有字段设置...
                } else if (item instanceof MechanicalMeterImportVo vo) {
                    // 机械表数据转换
                    meter.setMeterNo(vo.getMeterNo());
                    meter.setManufacturer(vo.getManufacturer());
                    meter.setCaliber(vo.getCaliber());
                    meter.setAccuracy(vo.getAccuracy());
                    meter.setInstallAddress(vo.getInstallLocation());
                    meter.setInitialReading(new BigDecimal(vo.getInitialReading()));
                    meter.setMeterType(1);
                } else {
                    failureMsg.add(String.format("第%d行数据格式有误", failureCount.incrementAndGet()));
                    continue;
                }

                // 通用字段处理
                meter.setInstallDate(new Date());  // 设置安装日期为当前日期
                meter.setDelFlag("0");  // 设置删除标志为未删除

                // 验证必填字段
                if (StringUtils.isEmpty(meter.getMeterNo())) {
                    failureMsg.add(String.format("第%d行水表编号为空", failureCount.incrementAndGet()));
                    continue;
                }

                // 验证水表编号是否重复
                if (checkMeterNoUnique(meter.getMeterNo())) {
                    failureMsg.add(String.format("第%d行水表编号已存在: %s", failureCount.incrementAndGet(), meter.getMeterNo()));
                    continue;
                }

                meterList.add(meter);
                successCount.incrementAndGet();
            } catch (Exception e) {
                failureMsg.add(String.format("第%d行数据处理失败: %s", failureCount.incrementAndGet(), e.getMessage()));
            }
        }

        if (!meterList.isEmpty()) {
            try {
                // 批量插入水表数据
                saveBatch(meterList);
            } catch (Exception e) {
                throw new ServiceException("批量导入水表数据失败：" + e.getMessage());
            }
        }

        result.put("successCount", successCount.get());
        result.put("failureCount", failureCount.get());
        result.put("failureMsg", failureMsg);

        return result;
    }

    /**
     * 检查水表编号是否唯一
     *
     * @param meterNo 水表编号
     * @return 结果
     */
    private boolean checkMeterNoUnique(String meterNo) {
        return baseMapper.selectCount(
            new LambdaQueryWrapper<WaterfeeMeter>()
                .eq(WaterfeeMeter::getMeterNo, meterNo)
        ) > 0;
    }

    /**
     * 根据表册ID获取水表编号列表
     *
     * @param meterBookId 表册ID
     * @return 水表编号列表
     */
    @Override
    public List<String> getMeterNosByBookId(Long meterBookId) {
        return baseMapper.selectMeterNosByBookId(meterBookId);
    }

    /**
     * 根据表册ID获取机械水表编号列表
     *
     * @param meterBookId 表册ID
     * @return 水表编号列表
     */
    @Override
    public List<String> getMechMeterNosByBookId(Long meterBookId) {
        return baseMapper.selectMechMeterNosByBookId(meterBookId);
    }

    /**
     * 根据用户编号查询水表信息
     *
     * @param userNo 用户编号
     * @return 水表信息集合
     */
    @Override
    public List<WaterfeeMeterVo> queryByUserNo(String userNo) {
        if (StringUtils.isBlank(userNo)) {
            throw new ServiceException("用户编号不能为空");
        }

        try {
            // 构建查询条件
            LambdaQueryWrapper<WaterfeeMeter> lqw = Wrappers.lambdaQuery();
            lqw.eq(WaterfeeMeter::getDelFlag, "0");
            lqw.inSql(WaterfeeMeter::getUserId,
                "SELECT user_id FROM waterfee_user WHERE user_no = '" + userNo + "' AND del_flag = '0'");

            // 查询水表信息
            List<WaterfeeMeterVo> meterList = baseMapper.selectVoList(lqw);

            // 补充相关信息
            if (!meterList.isEmpty()) {
                enrichMeterBookInfo(meterList);
                enrichUserInfo(meterList);
                enrichAreaInfo(meterList);
            }

            return meterList;
        } catch (Exception e) {
            log.error("根据用户编号查询水表信息失败，用户编号：{}", userNo, e);
            throw new ServiceException("查询水表信息失败");
        }
    }

    /**
     * 根据多个水表ID查询水表信息
     *
     * @param meterIds 水表ID集合
     * @return 水表信息集合
     */
    @Override
    public List<WaterfeeMeterVo> queryByIds(Collection<Long> meterIds) {
        if (meterIds == null || meterIds.isEmpty()) {
            return new ArrayList<>();
        }
        return baseMapper.selectVoList(
            new LambdaQueryWrapper<WaterfeeMeter>()
                .in(WaterfeeMeter::getMeterId, meterIds)
                .eq(WaterfeeMeter::getDelFlag, "0")
        );
    }

    /**
     * 根据多个水表编号查询水表信息
     *
     * @param meterNos 水表编号集合
     * @return 水表信息集合
     */
    @Override
    public List<WaterfeeMeterVo> queryByNos(Collection<String> meterNos) {
        if (meterNos == null || meterNos.isEmpty()) {
            return new ArrayList<>();
        }
        List<WaterfeeMeterVo> list = baseMapper.selectVoList(
            new LambdaQueryWrapper<WaterfeeMeter>()
                .in(WaterfeeMeter::getMeterNo, meterNos)
                .eq(WaterfeeMeter::getDelFlag, "0")
        );

        // 补充相关信息
        if (!list.isEmpty()) {
            enrichMeterBookInfo(list);
            enrichUserInfo(list);
            enrichAreaInfo(list);
        }

        return list;
    }
}
