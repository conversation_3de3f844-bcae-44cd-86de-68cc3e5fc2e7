package org.dromara.waterfee.meterRelation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.waterfee.meter.domain.WaterfeeMeter;
import org.dromara.waterfee.meterRelation.domain.WaterfeeMeterRelation;

import java.util.List;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 09:59
 **/

public interface WaterfeeMeterRelationMapper extends BaseMapper<WaterfeeMeterRelation> {

    List<Long> selectChildMeterIds(@Param("meterId") Long meterId);

    List<Long> selectParentMeterIds(@Param("meterId") Long meterId);

    WaterfeeMeter selectMeterById(@Param("id") Long id);

    List<WaterfeeMeterRelation> selectAllRelations();
}
