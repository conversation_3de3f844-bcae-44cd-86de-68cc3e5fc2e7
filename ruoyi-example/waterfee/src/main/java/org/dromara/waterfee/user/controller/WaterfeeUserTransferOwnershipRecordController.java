package org.dromara.waterfee.user.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.waterfee.user.domain.qo.RecordQo;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.user.domain.WaterfeeUserTransferOwnershipRecord;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserTransferOwnershipRecordVo;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserTransferOwnershipRecordBo;
import org.dromara.waterfee.user.service.IWaterfeeUserTransferOwnershipRecordService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 用水用户过户记录
 * 前端访问路由地址为:/waterfee/userTransferOwnershipRecord
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/userTransferOwnershipRecord")
public class WaterfeeUserTransferOwnershipRecordController extends BaseController {

    private final IWaterfeeUserTransferOwnershipRecordService waterfeeUserTransferOwnershipRecordService;

    /**
     * 查询用水用户过户记录列表
     */
    @SaCheckPermission("waterfee:userTransferOwnershipRecord:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeUserTransferOwnershipRecordVo> list(RecordQo recordQo, PageQuery pageQuery) {
        return waterfeeUserTransferOwnershipRecordService.queryPageList(recordQo, pageQuery);
    }

    /**
     * 导出用水用户过户记录列表
     */
    @SaCheckPermission("waterfee:userTransferOwnershipRecord:export")
    @Log(title = "用水用户过户记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeUserTransferOwnershipRecordBo bo, HttpServletResponse response) {
        List<WaterfeeUserTransferOwnershipRecordVo> list = waterfeeUserTransferOwnershipRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "用水用户过户记录", WaterfeeUserTransferOwnershipRecordVo.class, response);
    }

    /**
     * 获取用水用户过户记录详细信息
     *
     * @param transferId 主键
     */
    @SaCheckPermission("waterfee:userTransferOwnershipRecord:query")
    @GetMapping("/{transferId}")
    public R<WaterfeeUserTransferOwnershipRecord> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("transferId") Long transferId) {
        return R.ok(waterfeeUserTransferOwnershipRecordService.queryById(transferId));
    }

    /**
     * 新增用水用户过户记录
     */
    @SaCheckPermission("waterfee:userTransferOwnershipRecord:add")
    @Log(title = "用水用户过户记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeUserTransferOwnershipRecordBo bo) {
        return toAjax(waterfeeUserTransferOwnershipRecordService.insertByBo(bo));
    }

    /**
     * 修改用水用户过户记录
     */
    @SaCheckPermission("waterfee:userTransferOwnershipRecord:edit")
    @Log(title = "用水用户过户记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeUserTransferOwnershipRecordBo bo) {
        return toAjax(waterfeeUserTransferOwnershipRecordService.updateByBo(bo));
    }

    /**
     * 删除用水用户过户记录
     *
     * @param transferIds 主键串
     */
    @SaCheckPermission("waterfee:userTransferOwnershipRecord:remove")
    @Log(title = "用水用户过户记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{transferIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("transferIds") Long[] transferIds) {
        return toAjax(waterfeeUserTransferOwnershipRecordService.deleteWithValidByIds(List.of(transferIds), true));
    }
}
