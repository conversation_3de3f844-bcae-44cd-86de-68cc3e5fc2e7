package org.dromara.waterfee.income.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.waterfee.income.domain.WaterfeeIncomeRecord;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 收入记录视图对象 waterfee_income_record
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeIncomeRecord.class)
public class WaterfeeIncomeRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "收入ID")
    private String incomeId;

    /**
     * 收入类型
     */
    @ExcelProperty(value = "收入类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_income_type")
    private String incomeType;

    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编号")
    private String userNo;

    /**
     * 收费员姓名
     */
    @ExcelProperty(value = "收费员姓名")
    private String collectorName;

    /**
     * 收入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ExcelProperty(value = "收入时间")
    private Date incomeTime;

    /**
     * 金额
     */
    @ExcelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 支付方式
     */
    @ExcelProperty(value = "支付方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_pay_method")
    private String payMethod;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "创建时间")
    private Date createTime;

}
