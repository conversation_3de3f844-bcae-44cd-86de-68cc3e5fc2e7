package org.dromara.waterfee.meter.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.meter.domain.bo.MechanicalMeterQueryBo;
import org.dromara.waterfee.meter.domain.vo.MechanicalMeterDetailVo;
import org.dromara.waterfee.meter.domain.vo.MechanicalMeterReadingVo;
import org.dromara.waterfee.meter.service.IMechanicalMeterService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 机械表控制器
 *
 * @author: wangjs
 * @update: 2025/4/18
 */

@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/meter/mechanical")
public class MechanicalMeterController extends BaseController {

    private final IMechanicalMeterService mechanicalMeterService;

    /**
     * 分页查询机械表列表
     */
    @SaCheckPermission("meter:mechanical:list")
    @GetMapping("/list")
    public TableDataInfo<MechanicalMeterReadingVo> list(MechanicalMeterQueryBo queryDto, PageQuery pageQuery) {
        return mechanicalMeterService.queryPageList(queryDto, pageQuery);
    }

    /**
     * 获取机械表详情
     */
    @SaCheckPermission("meter:mechanical:query")
    @GetMapping("/{meterId}")
    public R<MechanicalMeterDetailVo> getDetail(@NotNull(message = "水表ID不能为空") @PathVariable Long meterId) {
        return R.ok(mechanicalMeterService.getDetail(meterId));
    }

    /**
     * 导出机械表数据
     */
    @SaCheckPermission("meter:mechanical:export")
    @Log(title = "导出机械表数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MechanicalMeterQueryBo queryDto, HttpServletResponse response) {
        List<MechanicalMeterReadingVo> list = mechanicalMeterService.queryList(queryDto);
        ExcelUtil.exportExcel(list, "机械表数据", MechanicalMeterReadingVo.class, response);
    }

    /**
     * 高级查询机械表
     */
    @SaCheckPermission("meter:mechanical:list")
    @PostMapping("/advanced-search")
    public TableDataInfo<MechanicalMeterReadingVo> advancedSearch(@RequestBody MechanicalMeterQueryBo queryDto, PageQuery pageQuery) {
        return mechanicalMeterService.queryPageList(queryDto, pageQuery);
    }

    /**
     * 获取统计数据
     */
    @SaCheckPermission("meter:mechanical:list")
    @GetMapping("/statistics")
    public R<Map<String, Object>> getStatistics(MechanicalMeterQueryBo queryDto) {
        return R.ok(mechanicalMeterService.getStatistics(queryDto));
    }
}
