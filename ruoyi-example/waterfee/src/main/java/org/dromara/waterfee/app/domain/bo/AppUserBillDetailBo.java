package org.dromara.waterfee.app.domain.bo;

import lombok.Data;
import java.io.Serializable;

/**
 * App用户详细账单查询业务对象
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
public class AppUserBillDetailBo implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 用户ID
   */
  private Long userId;

  /**
   * 用户编号
   */
  private String userNo;

  /**
   * 年份
   */
  private String year;

  /**
   * 月份
   */
  private String month;

  /**
   * 年月(格式：yyyy-MM)
   */
  private String yearMonth;
}