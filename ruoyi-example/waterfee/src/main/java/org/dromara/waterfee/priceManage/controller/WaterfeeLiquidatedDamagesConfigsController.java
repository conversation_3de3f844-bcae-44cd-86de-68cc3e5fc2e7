package org.dromara.waterfee.priceManage.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.priceManage.domain.WaterfeeLiquidatedDamagesConfigs;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeeLiquidatedDamagesConfigsVo;
import org.dromara.waterfee.priceManage.domain.bo.WaterfeeLiquidatedDamagesConfigsBo;
import org.dromara.waterfee.priceManage.service.IWaterfeeLiquidatedDamagesConfigsService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 违约金配置
 * 前端访问路由地址为:/waterfee/liquidatedDamagesConfigs
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/liquidatedDamagesConfigs")
public class WaterfeeLiquidatedDamagesConfigsController extends BaseController {

    private final IWaterfeeLiquidatedDamagesConfigsService waterfeeLiquidatedDamagesConfigsService;

    /**
     * 查询违约金配置列表
     */
    @SaCheckPermission("waterfee:liquidatedDamagesConfigs:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeLiquidatedDamagesConfigsVo> list(WaterfeeLiquidatedDamagesConfigsBo bo, PageQuery pageQuery) {
        return waterfeeLiquidatedDamagesConfigsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出违约金配置列表
     */
    @SaCheckPermission("waterfee:liquidatedDamagesConfigs:export")
    @Log(title = "违约金配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeLiquidatedDamagesConfigsBo bo, HttpServletResponse response) {
        List<WaterfeeLiquidatedDamagesConfigsVo> list = waterfeeLiquidatedDamagesConfigsService.queryList(bo);
        ExcelUtil.exportExcel(list, "违约金配置", WaterfeeLiquidatedDamagesConfigsVo.class, response);
    }

    /**
     * 获取违约金配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("waterfee:liquidatedDamagesConfigs:query")
    @GetMapping("/{id}")
    public R<WaterfeeLiquidatedDamagesConfigs> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(waterfeeLiquidatedDamagesConfigsService.queryById(id));
    }

    /**
     * 新增违约金配置
     */
    @SaCheckPermission("waterfee:liquidatedDamagesConfigs:add")
    @Log(title = "违约金配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeLiquidatedDamagesConfigsBo bo) {
        return toAjax(waterfeeLiquidatedDamagesConfigsService.insertByBo(bo));
    }

    /**
     * 修改违约金配置
     */
    @SaCheckPermission("waterfee:liquidatedDamagesConfigs:edit")
    @Log(title = "违约金配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeLiquidatedDamagesConfigsBo bo) {
        return toAjax(waterfeeLiquidatedDamagesConfigsService.updateByBo(bo));
    }

    /**
     * 删除违约金配置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("waterfee:liquidatedDamagesConfigs:remove")
    @Log(title = "违约金配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(waterfeeLiquidatedDamagesConfigsService.deleteWithValidByIds(List.of(ids), true));
    }
}
