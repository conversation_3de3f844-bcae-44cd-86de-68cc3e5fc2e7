package org.dromara.waterfee.wechat.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.waterfee.wechat.domain.bo.WechatDraftBo;
import org.dromara.waterfee.wechat.domain.entity.WechatDraft;
import org.dromara.waterfee.wechat.domain.vo.WechatDraftVo;
import org.dromara.waterfee.wechat.enums.ArticleStatusEnum;
import org.dromara.waterfee.wechat.enums.DraftStatusEnum;
import org.dromara.waterfee.wechat.enums.DraftTypeEnum;
import org.dromara.waterfee.wechat.mapper.WechatDraftMapper;
import org.dromara.waterfee.wechat.service.AccessToken;
import org.dromara.waterfee.wechat.service.IWechatDraftService;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 微信草稿服务业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WechatDraftServiceImpl implements IWechatDraftService {

    private final WechatDraftMapper baseMapper;
    private final AccessToken accessTokenService;

    /**
     * 查询微信草稿
     */
    @Override
    public WechatDraftVo queryById(Long draftId) {
        WechatDraftVo vo = baseMapper.selectVoById(draftId);
        if (vo != null) {
            // 设置状态名称
            vo.setDraftStatusName(DraftStatusEnum.getNameByCode(vo.getDraftStatus()));
            vo.setArticleStatusName(ArticleStatusEnum.getNameByCode(vo.getArticleStatus()));
            vo.setDraftTypeName(DraftTypeEnum.getNameByCode(vo.getDraftType()));
        }
        return vo;
    }

    /**
     * 查询微信草稿列表
     */
    @Override
    public TableDataInfo<WechatDraftVo> queryPageList(WechatDraftBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WechatDraft> lqw = buildQueryWrapper(bo);
        Page<WechatDraftVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        // 设置状态名称
        result.getRecords().forEach(vo -> {
            vo.setDraftStatusName(DraftStatusEnum.getNameByCode(vo.getDraftStatus()));
            vo.setArticleStatusName(ArticleStatusEnum.getNameByCode(vo.getArticleStatus()));
            vo.setDraftTypeName(DraftTypeEnum.getNameByCode(vo.getDraftType()));
        });
        return TableDataInfo.build(result);
    }

    /**
     * 查询微信草稿列表
     */
    @Override
    public List<WechatDraftVo> queryList(WechatDraftBo bo) {
        LambdaQueryWrapper<WechatDraft> lqw = buildQueryWrapper(bo);
        List<WechatDraftVo> list = baseMapper.selectVoList(lqw);
        // 设置状态名称
        list.forEach(vo -> {
            vo.setDraftStatusName(DraftStatusEnum.getNameByCode(vo.getDraftStatus()));
            vo.setArticleStatusName(ArticleStatusEnum.getNameByCode(vo.getArticleStatus()));
            vo.setDraftTypeName(DraftTypeEnum.getNameByCode(vo.getDraftType()));
        });
        return list;
    }

    private LambdaQueryWrapper<WechatDraft> buildQueryWrapper(WechatDraftBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WechatDraft> lqw = Wrappers.lambdaQuery();
        lqw.like(ObjectUtil.isNotEmpty(bo.getTitle()), WechatDraft::getTitle, bo.getTitle());
        lqw.like(ObjectUtil.isNotEmpty(bo.getAuthor()), WechatDraft::getAuthor, bo.getAuthor());
        lqw.eq(ObjectUtil.isNotEmpty(bo.getDraftStatus()), WechatDraft::getDraftStatus, bo.getDraftStatus());
        lqw.between(params.get("beginTime") != null && params.get("endTime") != null,
            WechatDraft::getCreateTime, params.get("beginTime"), params.get("endTime"));
        lqw.orderByDesc(WechatDraft::getCreateTime);
        return lqw;
    }

    /**
     * 新增微信草稿
     */
    @Override
    public Boolean insertByBo(WechatDraftBo bo) {
        WechatDraft add = MapstructUtils.convert(bo, WechatDraft.class);
        validEntityBeforeSave(add);

        // 设置默认值
        add.setDraftStatus(DraftStatusEnum.DRAFT.getCode());
        add.setArticleStatus(ArticleStatusEnum.UNPUBLISHED.getCode());
//        add.setShowCoverPic(bo.getShowCoverPic() != null ? bo.getShowCoverPic() : false);
        add.setNeedOpenComment(bo.getNeedOpenComment() != null ? bo.getNeedOpenComment() : false);
        add.setOnlyFansCanComment(bo.getOnlyFansCanComment() != null ? bo.getOnlyFansCanComment() : false);

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setDraftId(add.getDraftId());
        }
        return flag;
    }

    /**
     * 新增微信草稿并同时发布到微信
     */
    @Override
    public Boolean insertAndPublishByBo(WechatDraftBo bo) {
        // 先新增草稿
        Boolean insertResult = insertByBo(bo);
        if (!insertResult) {
            throw new RuntimeException("新增草稿失败");
        }

        try {
            // 先设置状态为发布中
            WechatDraft draft = baseMapper.selectById(bo.getDraftId());
            draft.setDraftStatus(DraftStatusEnum.PUBLISHING.getCode());
            draft.setPublishTime(new Date());
            draft.setUpdateTime(DateUtils.getNowDate());
            draft.setUpdateBy(LoginHelper.getUserId());
            baseMapper.updateById(draft);

            // 创建微信草稿
            String mediaId = createWechatDraft(bo);
            if (mediaId != null) {
                // 更新草稿状态和媒体ID
                draft.setMediaId(mediaId);
                draft.setDraftStatus(DraftStatusEnum.PUBLISHED.getCode());
                draft.setUpdateTime(DateUtils.getNowDate());
                draft.setUpdateBy(LoginHelper.getUserId());

                return baseMapper.updateById(draft) > 0;
            } else {
                // 发布失败，重置状态
                draft.setDraftStatus(DraftStatusEnum.DRAFT.getCode());
                draft.setPublishTime(null);
                draft.setUpdateTime(DateUtils.getNowDate());
                baseMapper.updateById(draft);
                return false;
            }
        } catch (Exception e) {
            log.error("发布草稿失败", e);
            // 发布失败，重置状态
            try {
                WechatDraft draft = baseMapper.selectById(bo.getDraftId());
                if (draft != null) {
                    draft.setDraftStatus(DraftStatusEnum.DRAFT.getCode());
                    draft.setPublishTime(null);
                    draft.setUpdateTime(DateUtils.getNowDate());
                    baseMapper.updateById(draft);
                }
            } catch (Exception resetException) {
                log.error("重置草稿状态失败", resetException);
            }
            throw new RuntimeException("发布草稿失败：" + e.getMessage());
        }

//        return false;
    }

    /**
     * 修改微信草稿
     */
    @Override
    public Boolean updateByBo(WechatDraftBo bo) {
        WechatDraft update = MapstructUtils.convert(bo, WechatDraft.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 修改微信草稿并同步更新微信端草稿
     */
    @Override
    public Boolean updateAndSyncByBo(WechatDraftBo bo) {
        // 1. 先查询草稿信息
        WechatDraft draft = baseMapper.selectById(bo.getDraftId());
        if (draft == null) {
            throw new RuntimeException("草稿不存在");
        }

        // 2. 检查文章是否已发布，如果已发布则不允许编辑
        if (ArticleStatusEnum.PUBLISHED.getCode().equals(draft.getArticleStatus())) {
            throw new RuntimeException("文章已发布，不能编辑草稿");
        }

        // 3. 检查草稿状态，如果是发布中状态则不允许编辑
        if (DraftStatusEnum.PUBLISHING.getCode().equals(draft.getDraftStatus())) {
            throw new RuntimeException("草稿正在发布中，请稍后再试");
        }

        // 4. 更新本地草稿
        WechatDraft update = MapstructUtils.convert(bo, WechatDraft.class);
        validEntityBeforeSave(update);
        update.setUpdateTime(DateUtils.getNowDate());
        update.setUpdateBy(LoginHelper.getUserId());

        boolean localUpdateSuccess = baseMapper.updateById(update) > 0;
        if (!localUpdateSuccess) {
            return false;
        }

        // 5. 如果草稿已发布到微信（有mediaId），则同步更新微信草稿
        if (DraftStatusEnum.PUBLISHED.getCode().equals(draft.getDraftStatus()) && draft.getMediaId() != null) {
            try {
                // 使用微信更新接口直接更新草稿
                String updatedMediaId = updateWechatDraft(draft.getMediaId(), bo);
                if (updatedMediaId != null) {
                    log.info("同步更新微信草稿成功，草稿ID: {}, mediaId: {}", bo.getDraftId(), updatedMediaId);
                    return true;
                } else {
                    log.error("同步更新微信草稿失败，草稿ID: {}", bo.getDraftId());
                    return false;
                }
            } catch (Exception e) {
                log.error("同步更新微信草稿失败", e);
                throw new RuntimeException("同步更新微信草稿失败：" + e.getMessage());
            }
        }

        return true;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WechatDraft entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除微信草稿
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 发布草稿到微信公众号
     */
    @Override
    public Boolean publishDraft(Long draftId) {
        WechatDraft draft = baseMapper.selectById(draftId);
        if (draft == null) {
            throw new RuntimeException("草稿不存在");
        }

        if (DraftStatusEnum.PUBLISHED.getCode().equals(draft.getDraftStatus())) {
            throw new RuntimeException("草稿已发布，不能重复发布");
        }

        if (DraftStatusEnum.PUBLISHING.getCode().equals(draft.getDraftStatus())) {
            throw new RuntimeException("草稿正在发布中，请稍后再试");
        }

        try {
            // 先设置状态为发布中
            draft.setDraftStatus(DraftStatusEnum.PUBLISHING.getCode());
            draft.setPublishTime(new Date());
            draft.setUpdateTime(DateUtils.getNowDate());
            draft.setUpdateBy(LoginHelper.getUserId());
            baseMapper.updateById(draft);

            // 创建微信草稿
            WechatDraftBo bo = MapstructUtils.convert(draft, WechatDraftBo.class);
            String mediaId = createWechatDraft(bo);

            if (mediaId != null) {
                // 更新草稿状态
                draft.setMediaId(mediaId);
                draft.setDraftStatus(DraftStatusEnum.PUBLISHED.getCode());
                draft.setUpdateTime(DateUtils.getNowDate());
                draft.setUpdateBy(LoginHelper.getUserId());

                return baseMapper.updateById(draft) > 0;
            } else {
                // 发布失败，重置状态
                draft.setDraftStatus(DraftStatusEnum.DRAFT.getCode());
                draft.setPublishTime(null);
                draft.setUpdateTime(DateUtils.getNowDate());
                baseMapper.updateById(draft);
                return false;
            }
        } catch (Exception e) {
            log.error("发布草稿失败", e);
            // 发布失败，重置状态
            try {
                draft.setDraftStatus(DraftStatusEnum.DRAFT.getCode());
                draft.setPublishTime(null);
                draft.setUpdateTime(DateUtils.getNowDate());
                baseMapper.updateById(draft);
            } catch (Exception resetException) {
                log.error("重置草稿状态失败", resetException);
            }
            throw new RuntimeException("发布草稿失败：" + e.getMessage());
        }

//        return false;
    }

    /**
     * 正式发布草稿为订阅号消息推文
     */
    @Override
    public Boolean publishArticle(Long draftId) {
        WechatDraft draft = baseMapper.selectById(draftId);
        if (draft == null) {
            throw new RuntimeException("草稿不存在");
        }

        if (!DraftStatusEnum.PUBLISHED.getCode().equals(draft.getDraftStatus())) {
            throw new RuntimeException("草稿未发布到微信，请先发布草稿");
        }

        if (draft.getMediaId() == null) {
            throw new RuntimeException("草稿媒体ID不存在，无法发布文章");
        }

        try {
            // 调用微信API发布文章
            Boolean result = publishWechatArticle(draft.getMediaId());
            if (result) {
                // 更新文章状态为已发布
                draft.setArticleStatus(ArticleStatusEnum.PUBLISHED.getCode());
                draft.setArticlePublishTime(new Date());
                draft.setUpdateTime(DateUtils.getNowDate());
                draft.setUpdateBy(LoginHelper.getUserId());

                baseMapper.updateById(draft);
                log.info("草稿ID: {} 已成功发布为订阅号消息推文", draftId);
                return true;
            }
        } catch (Exception e) {
            log.error("发布文章失败", e);
            throw new RuntimeException("发布文章失败：" + e.getMessage());
        }

        return false;
    }

    /**
     * 创建微信草稿
     */
    @Override
    public String createWechatDraft(WechatDraftBo bo) {
        String accessToken = accessTokenService.getWechatAccessToken();
        if (accessToken == null) {
            throw new RuntimeException("获取微信访问令牌失败");
        }

        String url = "https://api.weixin.qq.com/cgi-bin/draft/add?access_token=" + accessToken;

        // 构建请求参数
        Map<String, Object> articles = new HashMap<>();
        List<Map<String, Object>> articleList = new ArrayList<>();

        Map<String, Object> article = new HashMap<>();
        article.put("title", bo.getTitle());
        article.put("author", bo.getAuthor() != null ? bo.getAuthor() : "");
        article.put("digest", bo.getDigest() != null ? bo.getDigest() : "");
        article.put("content", bo.getContent());
        article.put("content_source_url", bo.getContentSourceUrl() != null ? bo.getContentSourceUrl() : "");
        article.put("thumb_media_id", bo.getThumbMediaId() != null ? bo.getThumbMediaId() : "");
//        article.put("show_cover_pic", bo.getShowCoverPic() != null ? (bo.getShowCoverPic() ? 1 : 0) : 0);
        article.put("need_open_comment", bo.getNeedOpenComment() != null ? (bo.getNeedOpenComment() ? 1 : 0) : 0);
        article.put("only_fans_can_comment", bo.getOnlyFansCanComment() != null ? (bo.getOnlyFansCanComment() ? 1 : 0) : 0);

        articleList.add(article);
        articles.put("articles", articleList);

        try {
            String response = HttpUtil.createPost(url)
                .body(JsonUtils.toJsonString(articles))
                .execute().body();

            Map<String, Object> result = JsonUtils.parseMap(response);
            if (result.get("errcode") != null && !"0".equals(result.get("errcode").toString())) {
                throw new RuntimeException("创建微信草稿失败：" + result.get("errmsg"));
            }

            return result.get("media_id").toString();
        } catch (Exception e) {
            log.error("创建微信草稿失败", e);
            throw new RuntimeException("创建微信草稿失败：" + e.getMessage());
        }
    }

    /**
     * 删除微信草稿
     */
    @Override
    public Boolean deleteWechatDraft(String mediaId) {
        String accessToken = accessTokenService.getWechatAccessToken();
        if (accessToken == null) {
            throw new RuntimeException("获取微信访问令牌失败");
        }

        String url = "https://api.weixin.qq.com/cgi-bin/draft/delete?access_token=" + accessToken;

        Map<String, Object> params = new HashMap<>();
        params.put("media_id", mediaId);

        try {
            String response = HttpUtil.createPost(url)
                .body(JsonUtils.toJsonString(params))
                .execute().body();

            Map<String, Object> result = JsonUtils.parseMap(response);
            return result.get("errcode") != null && "0".equals(result.get("errcode").toString());
        } catch (Exception e) {
            log.error("删除微信草稿失败", e);
            return false;
        }
    }

    /**
     * 更新微信草稿
     */
    @Override
    public String updateWechatDraft(String mediaId, WechatDraftBo bo) {
        String accessToken = accessTokenService.getWechatAccessToken();
        if (accessToken == null) {
            throw new RuntimeException("获取微信访问令牌失败");
        }

        String url = "https://api.weixin.qq.com/cgi-bin/draft/update?access_token=" + accessToken;

        // 构建请求参数
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("media_id", mediaId);
        requestData.put("index", 0); // 要更新的图文消息的位置，第一篇为0

        Map<String, Object> articles = new HashMap<>();
        articles.put("title", bo.getTitle());
        articles.put("author", bo.getAuthor() != null ? bo.getAuthor() : "");
        articles.put("digest", bo.getDigest() != null ? bo.getDigest() : "");
        articles.put("content", bo.getContent());
        articles.put("content_source_url", bo.getContentSourceUrl() != null ? bo.getContentSourceUrl() : "");
        articles.put("thumb_media_id", bo.getThumbMediaId() != null ? bo.getThumbMediaId() : "");
//        articles.put("show_cover_pic", bo.getShowCoverPic() != null ? (bo.getShowCoverPic() ? 1 : 0) : 0);
        articles.put("need_open_comment", bo.getNeedOpenComment() != null ? (bo.getNeedOpenComment() ? 1 : 0) : 0);
        articles.put("only_fans_can_comment", bo.getOnlyFansCanComment() != null ? (bo.getOnlyFansCanComment() ? 1 : 0) : 0);

        requestData.put("articles", articles);

        try {
            String response = HttpUtil.createPost(url)
                .body(JsonUtils.toJsonString(requestData))
                .execute().body();

            Map<String, Object> result = JsonUtils.parseMap(response);
            if (result.get("errcode") != null && "0".equals(result.get("errcode").toString())) {
                log.info("更新微信草稿成功，mediaId: {}", mediaId);
                return mediaId; // 更新成功返回原mediaId
            } else {
                log.error("更新微信草稿失败：{}", result.get("errmsg"));
                throw new RuntimeException("更新微信草稿失败：" + result.get("errmsg"));
            }
        } catch (Exception e) {
            log.error("更新微信草稿失败", e);
            throw new RuntimeException("更新微信草稿失败：" + e.getMessage());
        }
    }

    /**
     * 获取微信草稿列表
     */
    @Override
    public String getWechatDraftList(Integer offset, Integer count) {
        String accessToken = accessTokenService.getWechatAccessToken();
        if (accessToken == null) {
            throw new RuntimeException("获取微信访问令牌失败");
        }

        String url = "https://api.weixin.qq.com/cgi-bin/draft/batchget?access_token=" + accessToken;

        Map<String, Object> params = new HashMap<>();
        params.put("offset", offset != null ? offset : 0);
        params.put("count", count != null ? count : 20);
        params.put("no_content", 1); // 不返回content字段

        try {
            String response = HttpUtil.createPost(url)
                .body(JsonUtils.toJsonString(params))
                .execute().body();

            return response;
        } catch (Exception e) {
            log.error("获取微信草稿列表失败", e);
            throw new RuntimeException("获取微信草稿列表失败：" + e.getMessage());
        }
    }

    /**
     * 发布微信文章
     */
    private Boolean publishWechatArticle(String mediaId) {
        String accessToken = accessTokenService.getWechatAccessToken();
        if (accessToken == null) {
            throw new RuntimeException("获取微信访问令牌失败");
        }

        String url = "https://api.weixin.qq.com/cgi-bin/freepublish/submit?access_token=" + accessToken;

        Map<String, Object> params = new HashMap<>();
        params.put("media_id", mediaId);

        try {
            String response = HttpUtil.createPost(url)
                .body(JsonUtils.toJsonString(params))
                .execute().body();

            Map<String, Object> result = JsonUtils.parseMap(response);
            if (result.get("errcode") != null && "0".equals(result.get("errcode").toString())) {
                log.info("文章发布成功，发布ID: {}", result.get("publish_id"));
                return true;
            } else {
                log.error("文章发布失败：{}", result.get("errmsg"));
                throw new RuntimeException("文章发布失败：" + result.get("errmsg"));
            }
        } catch (Exception e) {
            log.error("发布微信文章失败", e);
            throw new RuntimeException("发布微信文章失败：" + e.getMessage());
        }
    }

    /**
     * 删除发布的文章（同时删除本地草稿和微信端文章）
     */
    @Override
    public Boolean deletePublishedArticle(Long draftId) {
        WechatDraft draft = baseMapper.selectById(draftId);
        if (draft == null) {
            throw new RuntimeException("草稿不存在");
        }

        boolean success = true;
        StringBuilder errorMsg = new StringBuilder();

        try {
            // 1. 如果有微信媒体ID，先删除微信端的草稿
            if (draft.getMediaId() != null) {
                try {
                    Boolean wechatDeleteResult = deleteWechatDraft(draft.getMediaId());
                    if (!wechatDeleteResult) {
                        errorMsg.append("删除微信草稿失败; ");
                        success = false;
                    }
                } catch (Exception e) {
                    log.error("删除微信草稿失败，媒体ID: {}", draft.getMediaId(), e);
                    errorMsg.append("删除微信草稿异常: ").append(e.getMessage()).append("; ");
                    success = false;
                }
            }

            // 2. 如果文章已发布，尝试删除已发布的文章
            if (ArticleStatusEnum.PUBLISHED.getCode().equals(draft.getArticleStatus())) {
                try {
                    Boolean articleDeleteResult = deletePublishedWechatArticle(draft.getMediaId());
                    if (!articleDeleteResult) {
                        errorMsg.append("删除已发布文章失败; ");
                        success = false;
                    }
                } catch (Exception e) {
                    log.error("删除已发布文章失败，草稿ID: {}", draftId, e);
                    errorMsg.append("删除已发布文章异常: ").append(e.getMessage()).append("; ");
                    success = false;
                }
            }

            // 3. 删除本地草稿记录
            try {
                int deleteResult = baseMapper.deleteById(draftId);
                if (deleteResult <= 0) {
                    errorMsg.append("删除本地草稿失败; ");
                    success = false;
                }
            } catch (Exception e) {
                log.error("删除本地草稿失败，草稿ID: {}", draftId, e);
                errorMsg.append("删除本地草稿异常: ").append(e.getMessage()).append("; ");
                success = false;
            }

            if (!success) {
                throw new RuntimeException("删除操作部分失败: " + errorMsg.toString());
            }

            log.info("成功删除草稿及相关文章，草稿ID: {}", draftId);
            return true;

        } catch (Exception e) {
            log.error("删除发布文章失败，草稿ID: {}", draftId, e);
            throw new RuntimeException("删除发布文章失败：" + e.getMessage());
        }
    }

    /**
     * 删除已发布的微信文章
     */
    private Boolean deletePublishedWechatArticle(String mediaId) {
        if (mediaId == null) {
            return true; // 没有媒体ID，认为删除成功
        }

        String accessToken = accessTokenService.getWechatAccessToken();
        if (accessToken == null) {
            throw new RuntimeException("获取微信访问令牌失败");
        }

        String url = "https://api.weixin.qq.com/cgi-bin/freepublish/delete?access_token=" + accessToken;

        Map<String, Object> params = new HashMap<>();
        params.put("article_id", mediaId); // 注意：这里应该是文章ID，不是草稿媒体ID

        try {
            String response = HttpUtil.createPost(url)
                .body(JsonUtils.toJsonString(params))
                .execute().body();

            Map<String, Object> result = JsonUtils.parseMap(response);
            if (result.get("errcode") != null && "0".equals(result.get("errcode").toString())) {
                log.info("成功删除已发布文章，媒体ID: {}", mediaId);
                return true;
            } else {
                log.error("删除已发布文章失败：{}", result.get("errmsg"));
                return false;
            }
        } catch (Exception e) {
            log.error("删除已发布文章失败", e);
            throw new RuntimeException("删除已发布文章失败：" + e.getMessage());
        }
    }


}
