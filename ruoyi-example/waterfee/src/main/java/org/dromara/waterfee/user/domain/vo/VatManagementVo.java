package org.dromara.waterfee.user.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.waterfee.user.domain.VatManagement;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 增值税管理视图对象 vat_management
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = VatManagement.class)
public class VatManagementVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 关联账单编号
     */
    @ExcelProperty(value = "关联账单编号")
    private String billNumber;

    /**
     * 发票类型（普通/专用）（字典invoice_type）
     */
    @ExcelProperty(value = "发票类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "普=通/专用")
    private String invoiceType;

    /**
     * 发票代码
     */
    @ExcelProperty(value = "发票代码")
    private String invoiceCode;

    /**
     * 发票号码
     */
    @ExcelProperty(value = "发票号码")
    private String invoiceNumber;

    /**
     * 发票状态（已开具/已红冲/已作废）（字典invoice_status）
     */
    @ExcelProperty(value = "发票状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "已=开具/已红冲/已作废")
    private String invoiceStatus;

    /**
     * 价税合计金额
     */
    @ExcelProperty(value = "价税合计金额")
    private Long totalAmount;

    /**
     * 不含税金额
     */
    @ExcelProperty(value = "不含税金额")
    private Long taxExclusiveAmount;

    /**
     * 税率（如0.13）
     */
    @ExcelProperty(value = "税率", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=0.13")
    private Long taxRate;

    /**
     * 税额
     */
    @ExcelProperty(value = "税额")
    private Long taxAmount;

    /**
     * 是否含税（1含税/0不含税）（字典yes_no）
     */
    @ExcelProperty(value = "是否含税", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=含税/0不含税")
    private String isTaxIncluded;

    /**
     * 购方名称
     */
    @ExcelProperty(value = "购方名称")
    private String buyerName;

    /**
     * 购方纳税人识别号
     */
    @ExcelProperty(value = "购方纳税人识别号")
    private String buyerTaxId;

    /**
     * 购方电话
     */
    @ExcelProperty(value = "购方电话")
    private String buyerTel;

    /**
     * 购方地址
     */
    @ExcelProperty(value = "购方地址")
    private String buyerAddress;

    /**
     * 购方开户行及账号
     */
    @ExcelProperty(value = "购方开户行及账号")
    private String buyerBankAccount;

    /**
     * 销方名称
     */
    @ExcelProperty(value = "销方名称")
    private String sellerName;

    /**
     * 销方纳税人识别号
     */
    @ExcelProperty(value = "销方纳税人识别号")
    private String sellerTaxId;

    /**
     * 销方电话
     */
    @ExcelProperty(value = "销方电话")
    private String sellerTel;

    /**
     * 销方地址
     */
    @ExcelProperty(value = "销方地址")
    private String sellerAddress;

    /**
     * 销方开户行及账号
     */
    @ExcelProperty(value = "销方开户行及账号")
    private String sellerBankAccount;

    /**
     * 开票时间
     */
    @ExcelProperty(value = "开票时间")
    private Date issueTime;

    /**
     * 红冲时间
     */
    @ExcelProperty(value = "红冲时间")
    private Date redFlushTime;

    /**
     * 作废时间
     */
    @ExcelProperty(value = "作废时间")
    private Date cancelTime;

    /**
     * 操作人ID
     */
    @ExcelProperty(value = "操作人ID")
    private Long operatorId;

    /**
     * 红冲原因
     */
    @ExcelProperty(value = "红冲原因")
    private String redFlushReason;

    /**
     * 作废原因
     */
    @ExcelProperty(value = "作废原因")
    private String cancelReason;


}
