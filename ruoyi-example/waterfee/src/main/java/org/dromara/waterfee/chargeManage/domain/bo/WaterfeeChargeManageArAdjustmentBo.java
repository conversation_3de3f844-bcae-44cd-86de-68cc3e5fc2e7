package org.dromara.waterfee.chargeManage.domain.bo;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageArAdjustment;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 应收账追补记录业务对象 waterfee_charge_manage_ar_adjustment
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeChargeManageArAdjustment.class, reverseConvertGenerate = false)
public class WaterfeeChargeManageArAdjustmentBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 水表编号
     */
    @NotBlank(message = "水表编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String meterNo;

    /**
     * 账期
     */
    @NotBlank(message = "账期不能为空", groups = { AddGroup.class, EditGroup.class })
    private String billPeriod;

    /**
     * 原应收金额
     */
    @NotNull(message = "原应收金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long originalAmount;

    /**
     * 调整后金额
     */
    @NotNull(message = "调整后金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long adjustedAmount;

    /**
     * 调整原因
     */
    private String reason;

    /**
     * 
     */
    private String remark;


}
