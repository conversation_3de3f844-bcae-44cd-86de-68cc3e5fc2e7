package org.dromara.waterfee.income.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.util.Date;

import java.io.Serial;

/**
 * 收入记录对象 waterfee_income_record
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_income_record")
public class WaterfeeIncomeRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "income_id")
    private Long incomeId;

    /**
     * 收入类型（bill_payment：账单缴费，prestore：预存充值，refund：账单退费）
     */
    private String incomeType;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 收费员姓名
     */
    private String collectorName;

    /**
     * 收入时间
     */
    private Date incomeTime;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 支付方式（wechat：微信，alipay：支付宝，cash：现金，other：其他）
     */
    private String payMethod;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

} 