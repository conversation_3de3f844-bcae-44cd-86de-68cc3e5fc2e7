package org.dromara.waterfee.pay.domain;

import lombok.Data;

@Data
public class WechatRefundParam {

    /**
     * 微信支付订单号
     */
    private String transactionId;

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 退款原因 选填
     */
    private String reason;

    /**
     * 退款金额 必填，单位为分 必填
     */
    private Long refundFee;

    /**
     * 原支付交易的订单金额 必填，单位为分 必填
     */
    private Long totalFee;

}
