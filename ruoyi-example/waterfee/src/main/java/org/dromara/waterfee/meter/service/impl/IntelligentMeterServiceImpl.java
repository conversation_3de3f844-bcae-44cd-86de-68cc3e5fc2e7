package org.dromara.waterfee.meter.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterBo;
import org.dromara.waterfee.meter.domain.vo.IntelligentMeterDetailVo;
import org.dromara.waterfee.meter.domain.vo.MeterChangeRecordVo;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meter.mapper.MeterChangeRecordMapper;
import org.dromara.waterfee.meter.monitor.MeterDeviceAlertService;
import org.dromara.waterfee.meter.monitor.MeterDeviceMetrics;
import org.dromara.waterfee.meter.service.IIntelligentMeterService;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.domain.vo.MeterReadingRecordVo;
import org.dromara.waterfee.meterReading.service.IMeterReadingRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 智能表服务实现
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IntelligentMeterServiceImpl implements IIntelligentMeterService {

    private final IWaterfeeMeterService waterfeeMeterService;
    private final MeterChangeRecordMapper meterChangeRecordMapper;
    private final IMeterReadingRecordService meterReadingRecordService;
    private final MeterDeviceMetrics metrics;
    private final MeterDeviceAlertService alertService;

    @Override
    public TableDataInfo<WaterfeeMeterVo> queryPageList(WaterfeeMeterBo bo, PageQuery pageQuery) {
        // 设置水表类型为智能表
        bo.setMeterType(2);
        return waterfeeMeterService.queryPageList(bo, pageQuery);
    }

    @Override
    public List<WaterfeeMeterVo> queryList(WaterfeeMeterBo bo) {
        // 设置水表类型为智能表
        bo.setMeterType(2);
        return waterfeeMeterService.queryList(bo);
    }

    @Override
    public IntelligentMeterDetailVo queryDetailById(Long meterId) {
        // 获取水表基本信息
        WaterfeeMeterVo meterInfo = waterfeeMeterService.queryById(meterId);

        // 获取最近一次换表记录
        MeterChangeRecordVo lastChangeRecord = meterChangeRecordMapper.selectLastChangeRecord(meterInfo.getMeterNo());

        // 获取抄表记录
        MeterReadingRecordVo readingRecord = null;
        if (meterInfo != null && meterInfo.getMeterNo() != null) {
            readingRecord = meterReadingRecordService.queryLatestByMeterNo(meterInfo.getMeterNo());
        }

        // 组装详情信息
        IntelligentMeterDetailVo detailVo = new IntelligentMeterDetailVo();
        detailVo.setMeterInfo(meterInfo);
        detailVo.setLastChangeRecord(lastChangeRecord);
        detailVo.setReadingRecord(readingRecord);

        return detailVo;
    }

    @Override
    public MeterReadingRecordVo readMeter(Long meterId) {
        long startTime = System.nanoTime();
        try {
            // 获取水表信息
            WaterfeeMeterVo meter = waterfeeMeterService.queryById(meterId);
            if (meter == null || meter.getMeterNo() == null) {
                throw new RuntimeException("水表不存在");
            }

            // TODO: 调用设备接口读取表数据
            // 这里应该调用实际的设备接口获取读数

            // 暂时返回最新的抄表记录
            MeterReadingRecordVo result = meterReadingRecordService.queryLatestByMeterNo(meter.getMeterNo());
            metrics.recordSuccess();
            return result;
        } catch (Exception e) {
            log.error("读取表数据失败, meterId: {}", meterId, e);
            metrics.recordFailure();
            alertService.recordFailure();
            throw e;
        } finally {
            long duration = System.nanoTime() - startTime;
            metrics.recordLatency(duration, TimeUnit.NANOSECONDS);
        }
    }

    @Override
    public List<MeterReadingRecordVo> batchReadMeter(List<Long> meterIds) {
        long startTime = System.nanoTime();
        try {
            // TODO: 调用设备接口批量读取表数据
            // 这里应该实现批量读取逻辑

            metrics.recordSuccess();
            return List.of();
        } catch (Exception e) {
            log.error("批量读取表数据失败, meterIds: {}", meterIds, e);
            metrics.recordFailure();
            alertService.recordFailure();
            throw e;
        } finally {
            long duration = System.nanoTime() - startTime;
            metrics.recordLatency(duration, TimeUnit.NANOSECONDS);
        }
    }

    @Override
    public void openValve(Long meterId) {
        long startTime = System.nanoTime();
        try {
            // TODO: 调用设备接口开阀
            metrics.recordSuccess();
        } catch (Exception e) {
            log.error("开阀失败, meterId: {}", meterId, e);
            metrics.recordFailure();
            alertService.recordFailure();
            throw e;
        } finally {
            long duration = System.nanoTime() - startTime;
            metrics.recordLatency(duration, TimeUnit.NANOSECONDS);
        }
    }

    @Override
    public void closeValve(Long meterId) {
        long startTime = System.nanoTime();
        try {
            // TODO: 调用设备接口关阀
            metrics.recordSuccess();
        } catch (Exception e) {
            log.error("关阀失败, meterId: {}", meterId, e);
            metrics.recordFailure();
            alertService.recordFailure();
            throw e;
        } finally {
            long duration = System.nanoTime() - startTime;
            metrics.recordLatency(duration, TimeUnit.NANOSECONDS);
        }
    }

    @Override
    public void clearValve(Long meterId) {
        long startTime = System.nanoTime();
        try {
            // TODO: 调用设备接口清零
            metrics.recordSuccess();
        } catch (Exception e) {
            log.error("清零失败, meterId: {}", meterId, e);
            metrics.recordFailure();
            alertService.recordFailure();
            throw e;
        } finally {
            long duration = System.nanoTime() - startTime;
            metrics.recordLatency(duration, TimeUnit.NANOSECONDS);
        }
    }

    @Override
    public void swingValve(Long meterId) {
        long startTime = System.nanoTime();
        try {
            // TODO: 调用设备接口开关阀
            metrics.recordSuccess();
        } catch (Exception e) {
            log.error("开关阀失败, meterId: {}", meterId, e);
            metrics.recordFailure();
            alertService.recordFailure();
            throw e;
        } finally {
            long duration = System.nanoTime() - startTime;
            metrics.recordLatency(duration, TimeUnit.NANOSECONDS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertByBo(WaterfeeMeterBo bo) {
        // 设置水表类型为智能表
        bo.setMeterType(2);
        return waterfeeMeterService.insertByBo(bo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByBo(WaterfeeMeterBo bo) {
        // 设置水表类型为智能表
        bo.setMeterType(2);
        return waterfeeMeterService.updateByBo(bo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteWithValidByIds(List<Long> ids, Boolean isValid) {
        return waterfeeMeterService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordMeterReading(Long meterId, Double readingValue, String operator, String remark) {
        // 获取水表信息
        WaterfeeMeterVo meter = waterfeeMeterService.queryById(meterId);
        if (meter == null || meter.getMeterNo() == null) {
            throw new RuntimeException("水表不存在");
        }

        // 查询该水表的最新抄表记录
        WaterfeeMeterReadingRecord latestRecord = meterReadingRecordService.queryLatestEntityByMeterNo(meter.getMeterNo());

        // 创建新的抄表记录
        WaterfeeMeterReadingRecord newRecord = new WaterfeeMeterReadingRecord();
        newRecord.setMeterNo(meter.getMeterNo());

        // 设置水表类型
        if (meter.getMeterType() != null) {
            newRecord.setMeterType(meter.getMeterType().toString());
            newRecord.setMeterBookId(meter.getMeterBookId());
        }

        // 设置上次抄表信息
        if (latestRecord != null) {
            newRecord.setLastReading(latestRecord.getCurrentReading());
            newRecord.setLastReadingTime(latestRecord.getReadingTime());
        } else {
            // 如果没有上次抄表记录，使用水表的初始读数
            if (meter.getInitialReading() != null) {
                newRecord.setLastReading(meter.getInitialReading().doubleValue());
            } else {
                newRecord.setLastReading(0.0);
            }
            newRecord.setLastReadingTime(meter.getInstallDate());
        }

        // 设置本次抄表信息
        newRecord.setCurrentReading(readingValue);
        newRecord.setOldMeterStopReading(0.0); // 默认旧表止数为0

//        // 计算水量
//        double waterUsage = newRecord.getOldMeterStopReading() + newRecord.getCurrentReading() -
//            (newRecord.getLastReading() != null ? newRecord.getLastReading() : 0.0);
//        newRecord.setWaterUsage(waterUsage);

        // 设置来源和时间
        newRecord.setSourceType("normal"); // 来源为设备自动上报
        newRecord.setReadingTime(new Date());

        // 保存抄表记录
        meterReadingRecordService.insertByEntity(newRecord);
    }
}
