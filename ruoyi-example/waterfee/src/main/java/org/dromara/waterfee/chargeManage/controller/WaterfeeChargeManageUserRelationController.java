package org.dromara.waterfee.chargeManage.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageUserRelation;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageUserRelationVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageUserRelationBo;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManageUserRelationService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 依托用户关系维护
 * 前端访问路由地址为:/waterfee/chargeManageUserRelation
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/chargeManageUserRelation")
public class WaterfeeChargeManageUserRelationController extends BaseController {

    private final IWaterfeeChargeManageUserRelationService waterfeeChargeManageUserRelationService;

    /**
     * 查询依托用户关系维护列表
     */
    @SaCheckPermission("waterfee:chargeManageUserRelation:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeChargeManageUserRelationVo> list(WaterfeeChargeManageUserRelationBo bo, PageQuery pageQuery) {
        return waterfeeChargeManageUserRelationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出依托用户关系维护列表
     */
    @SaCheckPermission("waterfee:chargeManageUserRelation:export")
    @Log(title = "依托用户关系维护", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeChargeManageUserRelationBo bo, HttpServletResponse response) {
        List<WaterfeeChargeManageUserRelationVo> list = waterfeeChargeManageUserRelationService.queryList(bo);
        ExcelUtil.exportExcel(list, "依托用户关系维护", WaterfeeChargeManageUserRelationVo.class, response);
    }

    /**
     * 获取依托用户关系维护详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("waterfee:chargeManageUserRelation:query")
    @GetMapping("/{id}")
    public R<WaterfeeChargeManageUserRelation> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(waterfeeChargeManageUserRelationService.queryById(id));
    }

    /**
     * 新增依托用户关系维护
     */
    @SaCheckPermission("waterfee:chargeManageUserRelation:add")
    @Log(title = "依托用户关系维护", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeChargeManageUserRelationBo bo) {
        return toAjax(waterfeeChargeManageUserRelationService.insertByBo(bo));
    }

    /**
     * 修改依托用户关系维护
     */
    @SaCheckPermission("waterfee:chargeManageUserRelation:edit")
    @Log(title = "依托用户关系维护", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeChargeManageUserRelationBo bo) {
        return toAjax(waterfeeChargeManageUserRelationService.updateByBo(bo));
    }

    /**
     * 删除依托用户关系维护
     *
     * @param ids 主键串
     */
    @SaCheckPermission("waterfee:chargeManageUserRelation:remove")
    @Log(title = "依托用户关系维护", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(waterfeeChargeManageUserRelationService.deleteWithValidByIds(List.of(ids), true));
    }
}
