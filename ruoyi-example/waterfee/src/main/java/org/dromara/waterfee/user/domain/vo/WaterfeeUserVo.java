package org.dromara.waterfee.user.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.waterfee.user.domain.WaterfeeUser;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用水用户管理视图对象 waterfee_user
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeUser.class)
public class WaterfeeUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编号")
    private String userNo;

    /**
     * 客户性质（字典waterfee_user_customer_nature）
     */
    @ExcelProperty(value = "客户性质", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_user_customer_nature")
    private String customerNature;

    /**
     * 用水性质（字典waterfee_user_use_water_nature）
     */
    @ExcelProperty(value = "用水性质", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_user_use_water_nature")
    private String useWaterNature;

    /**
     * 用水户名称
     */
    @ExcelProperty(value = "用水户名称")
    private String userName;

    /**
     * 用户状态（字典waterfee_user_user_status）
     */
    @ExcelProperty(value = "用户状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_user_user_status")
    private String userStatus;

    /**
     * 审核状态（字典audit_status）
     */
    @ExcelProperty(value = "审核状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "audit_status")
    private String auditStatus;

    /**
     * 水表编号
     */
    @ExcelProperty(value = "水表编号")
//    private String meterNo;

    /**
     * 抄表手册ID
     */
    private Long meterBookId;

    /**
     * 抄表手册名称
     */
    @ExcelProperty(value = "抄表手册名称")
    private String bookName;

    /**
     * 当年累计用水量（用于阶梯水价计算）
     */
    private Double ladderUsage;

    /**
     * 总用水量
     */
    private Double totalUsage;

    /**
     * 社区名称
     */
    @ExcelProperty(value = "社区名称")
    private String communityName;
}
