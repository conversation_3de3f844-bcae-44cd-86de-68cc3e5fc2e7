package org.dromara.waterfee.complaint.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 投诉建议对象 complaint_suggestion
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("complaint_suggestion")
public class ComplaintSuggestion extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "complaint_suggestion_id")
    private Long complaintSuggestionId;

    /**
     * 提交人姓名
     */
    private String submitterName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 投诉内容
     */
    private String content;

    /**
     * 投诉时间
     */
    private Date submitTime;

    /**
     * 备注
     */
    private String remark;


}
