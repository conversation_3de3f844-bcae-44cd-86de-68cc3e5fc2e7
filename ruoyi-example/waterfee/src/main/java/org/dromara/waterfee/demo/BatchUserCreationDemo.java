package org.dromara.waterfee.demo;

import org.dromara.common.core.domain.R;
import org.dromara.waterfee.counter.domain.bo.WaterfeeDepositBo;
import org.dromara.waterfee.counter.service.IWaterfeeCounterPaymentService;
import org.dromara.waterfee.demo.utils.RandomDataGenerator;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterBo;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserBo;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 批量用户创建和充值演示程序
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/demo")
public class BatchUserCreationDemo {

    @Autowired
    private IWaterfeeUserService waterfeeUserService;

    @Autowired
    private IWaterfeeMeterService waterfeeMeterService;

    @Autowired
    private IWaterfeeCounterPaymentService counterPaymentService;

    /**
     * 批量创建用户并充值
     * @param count 创建用户数量，默认为5
     * @param depositAmount 充值金额，默认为10000
     * @return 创建结果
     */
    @GetMapping("/batchCreateUsersWithDeposit")
    @Transactional(rollbackFor = Exception.class)
    public R<String> batchCreateUsersWithDeposit(
            @RequestParam(defaultValue = "5") Integer count,
            @RequestParam(defaultValue = "10000") BigDecimal depositAmount) {
        
        if (count <= 0 || count > 20) {
            return R.fail("创建数量必须在1-20之间");
        }
        
        if (depositAmount.compareTo(BigDecimal.ZERO) < 0) {
            return R.fail("充值金额不能为负数");
        }

        List<String> successUsers = new ArrayList<>();
        List<String> failedUsers = new ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            try {
                // 创建用户数据
                WaterfeeUserBo userBo = new WaterfeeUserBo();
                String randomUserName = RandomDataGenerator.generateRandomUserName();
                userBo.setUserName(randomUserName);
                userBo.setPhoneNumber(RandomDataGenerator.generateRandomPhoneNumber());
                userBo.setCertificateType("identity_card");
                userBo.setCertificateNumber(RandomDataGenerator.generateRandomIdCard());
                userBo.setUserStatus("normal");
                userBo.setAddress(RandomDataGenerator.generateRandomAddress());
                userBo.setSupplyDate(new Date());
                userBo.setAreaId(1905058024441294849L);
                userBo.setCommunityId(1L);
                userBo.setUseWaterNature("resident");
                userBo.setUseWaterNumber(3L);
                userBo.setInvoiceType("VAT_invoice");
                userBo.setPriceUseWaterNature("resident");
                userBo.setBillingMethod("1933328427904176130");

                // 插入用户
                Boolean userResult = waterfeeUserService.insertByBo(userBo);
                if (!userResult) {
                    failedUsers.add("用户" + i + "[" + randomUserName + "]: 用户创建失败");
                    continue;
                }

                // 创建水表数据
                WaterfeeMeterBo meterBo = new WaterfeeMeterBo();
                meterBo.setMeterNo(RandomDataGenerator.generateRandomMeterNo());
                meterBo.setMeterType(2);
                meterBo.setBusinessAreaId(1905058024441294849L);
                meterBo.setMeterBookId(2L);
                meterBo.setUserId(userBo.getUserId());
                meterBo.setUserNo(userBo.getUserNo());
                meterBo.setInstallDate(new Date());
                meterBo.setInstallAddress(userBo.getAddress());

                // 插入水表
                Boolean meterResult = waterfeeMeterService.insertByBo(meterBo);
                if (!meterResult) {
                    failedUsers.add("用户" + i + "[" + randomUserName + "]: 水表创建失败");
                    continue;
                }

                // 充值
                if (depositAmount.compareTo(BigDecimal.ZERO) > 0) {
                    WaterfeeDepositBo depositBo = new WaterfeeDepositBo();
                    depositBo.setUserId(userBo.getUserId());
                    depositBo.setAmount(depositAmount);
                    depositBo.setPaymentMethod("CASH");
                    depositBo.setRemark("批量创建用户赠送");
                    depositBo.setTollCollector("系统自动");

                    Boolean depositResult = counterPaymentService.addDeposit(depositBo);
                    if (depositResult) {
                        successUsers.add("用户" + i + "[" + randomUserName + "]: 创建成功，充值" + depositAmount + "元");
                    } else {
                        successUsers.add("用户" + i + "[" + randomUserName + "]: 创建成功，但充值失败");
                    }
                } else {
                    successUsers.add("用户" + i + "[" + randomUserName + "]: 创建成功，未充值");
                }

            } catch (Exception e) {
                failedUsers.add("用户" + i + ": 创建异常 - " + e.getMessage());
            }
        }

        // 构建返回结果
        StringBuilder result = new StringBuilder();
        result.append("批量创建用户完成！\n\n");
        result.append("成功创建: ").append(successUsers.size()).append(" 个用户\n");
        result.append("失败: ").append(failedUsers.size()).append(" 个用户\n\n");
        
        if (!successUsers.isEmpty()) {
            result.append("成功列表:\n");
            for (String user : successUsers) {
                result.append("✓ ").append(user).append("\n");
            }
        }
        
        if (!failedUsers.isEmpty()) {
            result.append("\n失败列表:\n");
            for (String user : failedUsers) {
                result.append("✗ ").append(user).append("\n");
            }
        }

        return R.ok(result.toString());
    }

    /**
     * 为指定用户充值
     * @param userId 用户ID
     * @param amount 充值金额
     * @return 充值结果
     */
    @GetMapping("/depositToUser")
    @Transactional(rollbackFor = Exception.class)
    public R<String> depositToUser(@RequestParam Long userId, @RequestParam BigDecimal amount) {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return R.fail("充值金额必须大于0");
        }

        try {
            WaterfeeDepositBo depositBo = new WaterfeeDepositBo();
            depositBo.setUserId(userId);
            depositBo.setAmount(amount);
            depositBo.setPaymentMethod("CASH");
            depositBo.setRemark("手动充值");
            depositBo.setTollCollector("系统管理员");

            Boolean result = counterPaymentService.addDeposit(depositBo);
            if (result) {
                BigDecimal currentBalance = waterfeeUserService.getUserBalance(userId);
                return R.ok("充值成功！用户ID: " + userId + "，充值金额: " + amount + " 元，当前余额: " + currentBalance + " 元");
            } else {
                return R.fail("充值失败");
            }
        } catch (Exception e) {
            return R.fail("充值异常: " + e.getMessage());
        }
    }

    /**
     * 查询用户详细信息（包含余额）
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/getUserInfo")
    public R<String> getUserInfo(@RequestParam Long userId) {
        try {
            // 查询用户基本信息
            var user = waterfeeUserService.queryById(userId);
            if (user == null) {
                return R.fail("用户不存在");
            }

            // 查询用户余额
            BigDecimal balance = waterfeeUserService.getUserBalance(userId);

            StringBuilder info = new StringBuilder();
            info.append("用户详细信息:\n");
            info.append("用户ID: ").append(user.getUserId()).append("\n");
            info.append("用户编号: ").append(user.getUserNo()).append("\n");
            info.append("用户名称: ").append(user.getUserName()).append("\n");
            info.append("手机号码: ").append(user.getPhoneNumber()).append("\n");
            info.append("用户地址: ").append(user.getAddress()).append("\n");
            info.append("用户状态: ").append(user.getUserStatus()).append("\n");
            info.append("账户余额: ").append(balance).append(" 元\n");

            return R.ok(info.toString());
        } catch (Exception e) {
            return R.fail("查询用户信息失败: " + e.getMessage());
        }
    }
}
