package org.dromara.waterfee.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.dromara.common.core.utils.DateUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * Jackson配置类，处理日期格式等问题
 */
//@Configuration
public class JacksonConfig {

    // ISO 8601格式的日期解析模式
    private static final String[] ISO_PATTERNS = {
        "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
        "yyyy-MM-dd'T'HH:mm:ss'Z'",
        "yyyy-MM-dd'T'HH:mm:ss.SSSXXX",
        "yyyy-MM-dd'T'HH:mm:ssXXX"
    };

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 设置日期格式和时区
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        objectMapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));

        // 注册Java 8日期时间模块
        objectMapper.registerModule(new JavaTimeModule());

        // 配置自定义日期反序列化器，支持多种格式
        SimpleModule dateModule = new SimpleModule();
        dateModule.addDeserializer(Date.class, new DateDeserializer());
        objectMapper.registerModule(dateModule);

        // 不将日期写为时间戳
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 忽略未知属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 允许接受空对象
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

        return objectMapper;
    }

    /**
     * 自定义日期反序列化器，使用DateUtils工具类处理多种日期格式
     */
    public static class DateDeserializer extends com.fasterxml.jackson.databind.JsonDeserializer<Date> {
        @Override
        public Date deserialize(com.fasterxml.jackson.core.JsonParser p, com.fasterxml.jackson.databind.DeserializationContext ctxt)
            throws java.io.IOException {
            String dateStr = p.getText();
            if (dateStr == null || dateStr.isEmpty()) {
                return null;
            }

            // 尝试使用DateUtils解析日期
            Date date = DateUtils.parseDate(dateStr);
            if (date != null) {
                return date;
            }

            // 如果DateUtils无法解析，尝试ISO格式
            try {
                return org.apache.commons.lang3.time.DateUtils.parseDate(dateStr, ISO_PATTERNS);
            } catch (java.text.ParseException e) {
                throw new java.io.IOException("无法解析日期: " + dateStr, e);
            }
        }
    }
}
