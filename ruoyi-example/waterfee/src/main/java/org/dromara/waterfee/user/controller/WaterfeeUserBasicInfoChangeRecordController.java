package org.dromara.waterfee.user.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.waterfee.user.domain.qo.RecordQo;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.user.domain.WaterfeeUserBasicInfoChangeRecord;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserBasicInfoChangeRecordVo;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserBasicInfoChangeRecordBo;
import org.dromara.waterfee.user.service.IWaterfeeUserBasicInfoChangeRecordService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 用水用户基础信息变更记录
 * 前端访问路由地址为:/waterfee/userBasicInfoChangeRecord
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/userBasicInfoChangeRecord")
public class WaterfeeUserBasicInfoChangeRecordController extends BaseController {

    private final IWaterfeeUserBasicInfoChangeRecordService waterfeeUserBasicInfoChangeRecordService;

    /**
     * 查询用水用户基础信息变更记录列表
     */
    @SaCheckPermission("waterfee:userBasicInfoChangeRecord:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeUserBasicInfoChangeRecordVo> list(RecordQo recordQo, PageQuery pageQuery) {
        return waterfeeUserBasicInfoChangeRecordService.queryPageList(recordQo, pageQuery);
    }

    /**
     * 导出用水用户基础信息变更记录列表
     */
    @SaCheckPermission("waterfee:userBasicInfoChangeRecord:export")
    @Log(title = "用水用户基础信息变更记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeUserBasicInfoChangeRecordBo bo, HttpServletResponse response) {
        List<WaterfeeUserBasicInfoChangeRecordVo> list = waterfeeUserBasicInfoChangeRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "用水用户基础信息变更记录", WaterfeeUserBasicInfoChangeRecordVo.class, response);
    }

    /**
     * 获取用水用户基础信息变更记录详细信息
     *
     * @param basicInfoChangeId 主键
     */
    @SaCheckPermission("waterfee:userBasicInfoChangeRecord:query")
    @GetMapping("/{basicInfoChangeId}")
    public R<WaterfeeUserBasicInfoChangeRecord> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("basicInfoChangeId") Long basicInfoChangeId) {
        return R.ok(waterfeeUserBasicInfoChangeRecordService.queryById(basicInfoChangeId));
    }

    /**
     * 新增用水用户基础信息变更记录
     */
    @SaCheckPermission("waterfee:userBasicInfoChangeRecord:add")
    @Log(title = "用水用户基础信息变更记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeUserBasicInfoChangeRecordBo bo) {
        return toAjax(waterfeeUserBasicInfoChangeRecordService.insertByBo(bo));
    }

    /**
     * 修改用水用户基础信息变更记录
     */
    @SaCheckPermission("waterfee:userBasicInfoChangeRecord:edit")
    @Log(title = "用水用户基础信息变更记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeUserBasicInfoChangeRecordBo bo) {
        return toAjax(waterfeeUserBasicInfoChangeRecordService.updateByBo(bo));
    }

    /**
     * 删除用水用户基础信息变更记录
     *
     * @param basicInfoChangeIds 主键串
     */
    @SaCheckPermission("waterfee:userBasicInfoChangeRecord:remove")
    @Log(title = "用水用户基础信息变更记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{basicInfoChangeIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("basicInfoChangeIds") Long[] basicInfoChangeIds) {
        return toAjax(waterfeeUserBasicInfoChangeRecordService.deleteWithValidByIds(List.of(basicInfoChangeIds), true));
    }
}
