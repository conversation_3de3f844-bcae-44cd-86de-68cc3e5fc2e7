package org.dromara.waterfee.income.domain.vo;

import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 收入统计视图对象
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@Data
public class WaterfeeIncomeSummaryVo implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 账单缴费总金额
   */
  private BigDecimal billPaymentAmount;

  /**
   * 账单缴费笔数
   */
  private Long billPaymentCount;

  /**
   * 预存充值总金额
   */
  private BigDecimal prestoreAmount;

  /**
   * 预存充值笔数
   */
  private Long prestoreCount;

  /**
   * 账单退费总金额 (通常为负值，表示支出)
   */
  private BigDecimal refundAmount;

  /**
   * 账单退费笔数
   */
  private Long refundCount;

  /**
   * 总收入金额 (billPaymentAmount + prestoreAmount - refundAmount)
   */
  private BigDecimal totalIncomeAmount;

  /**
   * 总交易笔数
   */
  private Long totalTransactionCount;
}