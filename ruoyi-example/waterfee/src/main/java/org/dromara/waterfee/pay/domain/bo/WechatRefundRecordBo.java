package org.dromara.waterfee.pay.domain.bo;

import org.dromara.waterfee.pay.domain.WechatRefundRecord;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 微信退款记录业务对象 wechat_refund_record
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WechatRefundRecord.class, reverseConvertGenerate = false)
public class WechatRefundRecordBo extends BaseEntity {

    /**
     * 退款单号 (主键)
     */
    @NotBlank(message = "退款单号 (主键)不能为空", groups = { EditGroup.class })
    private String refundId;

    /**
     * 缴费明细ID (订单号)
     */
    private String paymentDetailId;

    /**
     * 微信退款单号
     */
    private String wechatRefundId;

    /**
     * 退款状态SUCCESS—退款成功 CLOSED—退款关闭 PROCESSING—退款处理中 ABNORMAL—退款异常
     */
    private String refundStatus;

    /**
     * 退款成功时间
     */
    private Date successTime;

    /**
     * 退款入账账户
     */
    private String userReceivedAccount;

    /**
     * 交易流水号 (支付平台返回)
     */
    private String transactionId;

    /**
     * 订单总金额
     */
    private BigDecimal paymentAmount;

    /**
     * 退款金额
     */
    private BigDecimal paymentRefund;

    /**
     * 实际支付金额（不包含代金券）
     */
    private BigDecimal payerAmount;

    /**
     * 用户实际退款金额（不包含代金券）
     */
    private BigDecimal payerRefund;

    /**
     * 备注
     */
    private String remark;


}
