package org.dromara.waterfee.user.mapper;

import org.dromara.waterfee.user.domain.ConsolidatedAccountItem;
import org.dromara.waterfee.user.domain.vo.ConsolidatedAccountItemVo;
import org.dromara.waterfee.user.domain.bo.ConsolidatedAccountItemBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 合收户关系Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface ConsolidatedAccountItemMapper extends BaseMapperPlus<ConsolidatedAccountItem, ConsolidatedAccountItemVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<ConsolidatedAccountItemVo>> P selectVoPage(IPage<ConsolidatedAccountItem> page, Wrapper<ConsolidatedAccountItem> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询合收户关系列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<ConsolidatedAccountItemVo> queryList(@Param("page") Page<ConsolidatedAccountItem> page, @Param("query") ConsolidatedAccountItemBo query);

}
