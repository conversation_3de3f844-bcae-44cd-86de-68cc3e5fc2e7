package org.dromara.waterfee.priceManage.domain.bo;

import org.dromara.waterfee.priceManage.domain.WaterfeeSurchargeConfigs;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 附加费配置 (Surcharge Configurations)业务对象 waterfee_surcharge_configs
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeSurchargeConfigs.class, reverseConvertGenerate = false)
public class WaterfeeSurchargeConfigsBo extends BaseEntity {

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 附加费名称
     */
    @NotBlank(message = "附加费名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 计算方式 (e.g., fixed_amount, meter_reading)
     */
    @NotBlank(message = "计算方式 (e.g., fixed_amount, meter_reading)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String calculationMethod;

    /**
     * 固定金额 (if calculation_method is fixed_amount)
     */
    private Long fixedAmount;

    /**
     * 比例(%) (e.g., if calculation_method is meter_reading)
     */
    private Long ratePercent;

    /**
     * 附加费类别
     */
    @NotBlank(message = "附加费类别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String category;

    /**
     * 备注
     */
    private String remarks;


}
