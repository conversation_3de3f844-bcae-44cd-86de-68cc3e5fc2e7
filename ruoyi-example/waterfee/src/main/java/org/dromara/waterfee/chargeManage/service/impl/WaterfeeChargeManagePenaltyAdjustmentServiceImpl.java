package org.dromara.waterfee.chargeManage.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManagePenaltyAdjustmentBo;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManagePenaltyAdjustmentVo;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManagePenaltyAdjustment;
import org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManagePenaltyAdjustmentMapper;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManagePenaltyAdjustmentService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 违约金调整/减免Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RequiredArgsConstructor
@Service
public class WaterfeeChargeManagePenaltyAdjustmentServiceImpl implements IWaterfeeChargeManagePenaltyAdjustmentService {

    private final WaterfeeChargeManagePenaltyAdjustmentMapper baseMapper;

    /**
     * 查询违约金调整/减免
     *
     * @param id 主键
     * @return 违约金调整/减免
     */
    @Override
    public WaterfeeChargeManagePenaltyAdjustment queryById(Long id){
        return baseMapper.selectById(id);
    }

    /**
     * 分页查询违约金调整/减免列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 违约金调整/减免分页列表
     */
    @Override
    public TableDataInfo<WaterfeeChargeManagePenaltyAdjustmentVo> queryPageList(WaterfeeChargeManagePenaltyAdjustmentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeChargeManagePenaltyAdjustment> lqw = buildQueryWrapper(bo);
        Page<WaterfeeChargeManagePenaltyAdjustmentVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的违约金调整/减免列表
     *
     * @param bo 查询条件
     * @return 违约金调整/减免列表
     */
    @Override
    public List<WaterfeeChargeManagePenaltyAdjustmentVo> queryList(WaterfeeChargeManagePenaltyAdjustmentBo bo) {
        LambdaQueryWrapper<WaterfeeChargeManagePenaltyAdjustment> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeChargeManagePenaltyAdjustment> buildQueryWrapper(WaterfeeChargeManagePenaltyAdjustmentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeChargeManagePenaltyAdjustment> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeeChargeManagePenaltyAdjustment::getCreateTime);
        lqw.eq(bo.getUserId() != null, WaterfeeChargeManagePenaltyAdjustment::getUserId, bo.getUserId());
        lqw.eq(bo.getBillId() != null, WaterfeeChargeManagePenaltyAdjustment::getBillId, bo.getBillId());
        lqw.eq(bo.getOriginalPenalty() != null, WaterfeeChargeManagePenaltyAdjustment::getOriginalPenalty, bo.getOriginalPenalty());
        lqw.eq(bo.getAdjustedPenalty() != null, WaterfeeChargeManagePenaltyAdjustment::getAdjustedPenalty, bo.getAdjustedPenalty());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), WaterfeeChargeManagePenaltyAdjustment::getReason, bo.getReason());
        return lqw;
    }

    /**
     * 新增违约金调整/减免
     *
     * @param bo 违约金调整/减免
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeChargeManagePenaltyAdjustmentBo bo) {
        WaterfeeChargeManagePenaltyAdjustment add = MapstructUtils.convert(bo, WaterfeeChargeManagePenaltyAdjustment.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改违约金调整/减免
     *
     * @param bo 违约金调整/减免
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeChargeManagePenaltyAdjustmentBo bo) {
        WaterfeeChargeManagePenaltyAdjustment update = MapstructUtils.convert(bo, WaterfeeChargeManagePenaltyAdjustment.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeChargeManagePenaltyAdjustment entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除违约金调整/减免信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
