package org.dromara.waterfee.app.domain.vo;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 阶梯水价信息视图对象
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
public class WaterPriceTierInfoVo implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 阶梯等级
   */
  private Integer tierNumber;

  /**
   * 起始用量
   */
  private BigDecimal startQuantity;

  /**
   * 结束用量
   */
  private BigDecimal endQuantity;

  /**
   * 价格
   */
  private BigDecimal price;

  /**
   * 水资源税
   */
  private BigDecimal waterResourceTax;

  /**
   * 污水处理费
   */
  private BigDecimal sewageTreatmentFee;

  /**
   * 垃圾处理费
   */
  private BigDecimal garbageDisposalFee;

  /**
   * 卫生费
   */
  private BigDecimal sanitationFee;

  /**
   * 维护基金
   */
  private BigDecimal maintenanceFund;
}