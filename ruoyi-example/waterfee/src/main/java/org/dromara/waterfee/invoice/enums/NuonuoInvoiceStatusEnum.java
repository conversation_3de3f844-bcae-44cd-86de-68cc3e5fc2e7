package org.dromara.waterfee.invoice.enums;

import lombok.Data;

/**
 * 诺诺发票状态枚举
 * 备注：22、24状态时无需再查询，请确认开票失败原因以及签章失败原因
 */
public enum NuonuoInvoiceStatusEnum {
    // 开票完成
    INVOICED_COMPLETED("2", "开票完成"),
    // 开票中
    INVOICING("20", "开票中"),
    // 开票成功签章中
    SIGNING_AFTER_SUCCESS("21", "开票成功签章中"),
    // 开票失败
    INVOICE_FAILED("22", "开票失败"),
    // 开票成功签章失败
    SIGN_FAILED_AFTER_SUCCESS("24", "开票成功签章失败"),
    // 发票已作废
    INVOICE_VOIDED("3", "发票已作废"),
    // 发票作废中
    VOIDING_IN_PROGRESS("31", "发票作废中");

    private final String code;
    private final String description;

    NuonuoInvoiceStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据状态码获取枚举实例
     * @param code 状态码
     * @return 枚举实例
     */
    public static NuonuoInvoiceStatusEnum getValueByCode(String code) {
        for (NuonuoInvoiceStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 是否需要处理失败原因
     * @return true 需要处理
     */
    public boolean isNeedHandleFailureReason() {
        return this == INVOICE_FAILED || this == SIGN_FAILED_AFTER_SUCCESS;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return "NuonuoInvoiceStatusEnums{" +
                "code=" + code +
                ", description='" + description + '\'' +
                '}';
    }
}

