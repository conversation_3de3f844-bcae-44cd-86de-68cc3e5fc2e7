package org.dromara.waterfee.user.mapper;

import org.dromara.waterfee.user.domain.VatManagement;
import org.dromara.waterfee.user.domain.vo.VatManagementVo;
import org.dromara.waterfee.user.domain.bo.VatManagementBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 增值税管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface VatManagementMapper extends BaseMapperPlus<VatManagement, VatManagementVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<VatManagementVo>> P selectVoPage(IPage<VatManagement> page, Wrapper<VatManagement> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询增值税管理列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<VatManagementVo> queryList(@Param("page") Page<VatManagement> page, @Param("query") VatManagementBo query);

}
