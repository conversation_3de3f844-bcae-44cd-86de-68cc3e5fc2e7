package org.dromara.waterfee.wechat.schedule;

import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.waterfee.wechat.domain.entity.WechatDraft;
import org.dromara.waterfee.wechat.enums.DraftStatusEnum;
import org.dromara.waterfee.wechat.mapper.WechatDraftMapper;
import org.dromara.waterfee.wechat.service.AccessToken;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 微信草稿定时任务
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Slf4j
@Component
@EnableScheduling
@RequiredArgsConstructor
public class WechatDraftSchedule {

    private final WechatDraftMapper wechatDraftMapper;
    private final AccessToken accessTokenService;

    /**
     * 每5分钟执行一次，查询发布中的草稿，更新发布状态
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300000毫秒
    public void syncDraftPublishStatus() {
        log.info("开始执行微信草稿发布状态同步定时任务");
        try {
            // 查询状态为发布中的草稿记录
            LambdaQueryWrapper<WechatDraft> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WechatDraft::getDraftStatus, DraftStatusEnum.PUBLISHING.getCode());
            List<WechatDraft> publishingDrafts = wechatDraftMapper.selectList(queryWrapper);

            if (publishingDrafts.isEmpty()) {
                log.info("没有处于发布中状态的草稿记录");
                return;
            }

            log.info("找到 {} 个发布中的草稿，开始检查发布状态", publishingDrafts.size());

            // 遍历每个发布中的草稿，检查其发布状态
            for (WechatDraft draft : publishingDrafts) {
                try {
                    checkAndUpdateDraftStatus(draft);
                } catch (Exception e) {
                    log.error("检查草稿发布状态失败，草稿ID: {}, 错误: {}", draft.getDraftId(), e.getMessage(), e);
                }
            }

            log.info("微信草稿发布状态同步任务执行完成");
        } catch (Exception e) {
            log.error("微信草稿发布状态同步任务执行异常", e);
        }
    }

    /**
     * 检查并更新草稿发布状态
     *
     * @param draft 草稿对象
     */
    private void checkAndUpdateDraftStatus(WechatDraft draft) {
        if (draft.getMediaId() == null) {
            log.warn("草稿ID: {} 没有媒体ID，跳过状态检查", draft.getDraftId());
            return;
        }

        try {
            // 调用微信API检查草稿是否存在
            Boolean draftExists = checkWechatDraftExists(draft.getMediaId());

            if (draftExists != null) {
                if (draftExists) {
                    // 草稿发布成功，更新状态
                    updateDraftStatusToPublished(draft);
                    log.info("草稿ID: {} 发布成功，状态已更新", draft.getDraftId());
                } else {
                    // 草稿不存在，可能发布失败
                    checkTimeoutAndReset(draft);
                }
            } else {
                // API调用异常，检查超时
                checkTimeoutAndReset(draft);
            }
        } catch (Exception e) {
            log.error("检查草稿发布状态时发生错误，草稿ID: {}", draft.getDraftId(), e);
            checkTimeoutAndReset(draft);
        }
    }

    /**
     * 检查超时并重置状态
     */
    private void checkTimeoutAndReset(WechatDraft draft) {
        // 检查是否超过发布超时时间（比如30分钟）
        Date publishTime = draft.getPublishTime();
        if (publishTime != null) {
            long timeDiff = System.currentTimeMillis() - publishTime.getTime();
            long timeoutMinutes = 30; // 30分钟超时

            if (timeDiff > timeoutMinutes * 60 * 1000) {
                // 超时，将状态重置为草稿
                updateDraftStatusToDraft(draft);
                log.warn("草稿ID: {} 发布超时，状态已重置为草稿", draft.getDraftId());
            }
        }
    }

    /**
     * 检查微信草稿是否存在
     *
     * @param mediaId 媒体ID
     * @return 是否存在，null表示检查异常
     */
    private Boolean checkWechatDraftExists(String mediaId) {
        String accessToken = accessTokenService.getWechatAccessToken();
        if (accessToken == null) {
            log.error("获取微信访问令牌失败");
            return null;
        }

        String url = "https://api.weixin.qq.com/cgi-bin/draft/get?access_token=" + accessToken;

        Map<String, Object> params = new HashMap<>();
        params.put("media_id", mediaId);

        try {
            String response = HttpUtil.createPost(url)
                .body(JsonUtils.toJsonString(params))
                .execute().body();

            Map<String, Object> result = JsonUtils.parseMap(response);

            // 如果errcode为0，说明草稿存在
            if (result.get("errcode") != null && "0".equals(result.get("errcode").toString())) {
                return true;
            }

            // 如果errcode为40007，说明草稿不存在或已被删除
            if (result.get("errcode") != null && "40007".equals(result.get("errcode").toString())) {
                return false;
            }

            // 其他错误码，记录日志但不更新状态
            log.warn("检查微信草稿状态返回错误码: {}, 错误信息: {}",
                result.get("errcode"), result.get("errmsg"));
            return null;

        } catch (Exception e) {
            log.error("检查微信草稿是否存在时发生错误", e);
            return null;
        }
    }

    /**
     * 更新草稿状态为已发布
     *
     * @param draft 草稿对象
     */
    private void updateDraftStatusToPublished(WechatDraft draft) {
        draft.setDraftStatus(DraftStatusEnum.PUBLISHED.getCode());
        draft.setUpdateTime(DateUtils.getNowDate());
//        draft.setUpdateBy("SYSTEM"); // 系统自动更新

        wechatDraftMapper.updateById(draft);
    }

    /**
     * 更新草稿状态为草稿（发布失败）
     *
     * @param draft 草稿对象
     */
    private void updateDraftStatusToDraft(WechatDraft draft) {
        draft.setDraftStatus(DraftStatusEnum.DRAFT.getCode());
        draft.setMediaId(null); // 清除媒体ID
        draft.setPublishTime(null); // 清除发布时间
        draft.setUpdateTime(DateUtils.getNowDate());
//        draft.setUpdateBy("SYSTEM"); // 系统自动更新

        wechatDraftMapper.updateById(draft);
    }
}
