package org.dromara.waterfee.meterBook.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 抄表手册对象 wf_meter_book
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_meter_book")
public class MeterBook extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 管辖区域ID
     */
    private Long areaId;

    /**
     * 管辖区域名称
     */
    @TableField(exist = false)
    private String areaName;

    /**
     * 手册编号
     */
    private String bookNo;

    /**
     * 手册名称
     */
    private String bookName;

    /**
     * 抄表方式（人工抄表、远传抄表）
     */
    private String readType;

    /**
     * 抄表周期（一月一抄、两月一抄等）
     */
    private String readCycle;

    /**
     * 抄表日期
     */
    private Integer readDay;

    /**
     * 抄表基准日
     */
    private Integer readBaseDay;

    /**
     * 抄表员
     */
    private Long reader;

    /**
     * 抄表员姓名
     */
    private String readerName;

    /**
     * 抄表组长
     */
    private Long readerLeader;

    /**
     * 抄表组长姓名
     */
    private String readerLeaderName;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

}
