package org.dromara.waterfee.meter.service;

import org.dromara.waterfee.meter.domain.WaterfeeMeter;
import org.dromara.waterfee.meter.domain.adjustment.MeterBookAdjustmentBo;
import org.dromara.waterfee.meter.domain.adjustment.MeterSortAdjustmentBo;

import java.util.List;

/**
 * 水表调整Service接口
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
public interface IMeterAdjustmentService {

    /**
     * 册本调整
     *
     * @param bo 册本调整业务对象
     * @return 结果
     */
    Boolean adjustMeterBook(MeterBookAdjustmentBo bo);

    /**
     * 册内调整
     *
     * @param bo 册内调整业务对象
     * @return 结果
     */
    Boolean adjustMeterSort(MeterSortAdjustmentBo bo);

    /**
     * 查询表册中的水表列表（按排序号排序）
     *
     * @param bookId 表册ID
     * @return 水表列表
     */
    List<WaterfeeMeter> getMetersByBookId(Long bookId);
}
