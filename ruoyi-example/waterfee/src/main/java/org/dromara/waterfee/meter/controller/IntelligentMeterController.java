package org.dromara.waterfee.meter.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterBo;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meter.service.IIntelligentMeterService;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.meterReading.domain.vo.MeterReadingRecordVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 智能表控制器
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/meter/intelligent")
public class IntelligentMeterController extends BaseController {

    private final IWaterfeeMeterService waterfeeMeterService;
    private final IIntelligentMeterService intelligentMeterService;

    /**
     * 查询智能表列表
     */
    @SaCheckPermission("meter:intelligent:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeMeterVo> list(WaterfeeMeterBo bo, PageQuery pageQuery) {
        bo.setMeterType(2); // 2表示智能表
        return waterfeeMeterService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出智能表列表
     */
    @SaCheckPermission("meter:intelligent:export")
    @Log(title = "智能表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeMeterBo bo, HttpServletResponse response) {
        bo.setMeterType(2); // 2表示智能表
        List<WaterfeeMeterVo> list = waterfeeMeterService.queryList(bo);
        ExcelUtil.exportExcel(list, "智能表信息", WaterfeeMeterVo.class, response);
    }

    /**
     * 获取智能表详细信息
     */
    @SaCheckPermission("meter:intelligent:query")
    @GetMapping("/{meterId}")
    public R<WaterfeeMeterVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long meterId) {
        return R.ok(waterfeeMeterService.queryById(meterId));
    }

    /**
     * 读取表数据
     */
    @SaCheckPermission("meter:intelligent:read")
    @Log(title = "智能表读数", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping("/read/{meterId}")
    public R<MeterReadingRecordVo> readMeter(@NotNull(message = "水表ID不能为空") @PathVariable Long meterId) {
        return R.ok(intelligentMeterService.readMeter(meterId));
    }

    /**
     * 批量读取表数据
     */
    @SaCheckPermission("meter:intelligent:read")
    @Log(title = "智能表批量读数", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping("/read/batch")
    public R<List<MeterReadingRecordVo>> batchReadMeter(
        @NotEmpty(message = "水表ID列表不能为空") @RequestBody List<Long> meterIds) {
        return R.ok(intelligentMeterService.batchReadMeter(meterIds));
    }

    /**
     * 开阀
     */
    @SaCheckPermission("meter:intelligent:control")
    @Log(title = "智能表开阀", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping("/open/{meterId}")
    public R<Void> openValve(@NotNull(message = "水表ID不能为空") @PathVariable Long meterId) {
        intelligentMeterService.openValve(meterId);
        return R.ok();
    }

    /**
     * 关阀
     */
    @SaCheckPermission("meter:intelligent:control")
    @Log(title = "智能表关阀", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping("/close/{meterId}")
    public R<Void> closeValve(@NotNull(message = "水表ID不能为空") @PathVariable Long meterId) {
        intelligentMeterService.closeValve(meterId);
        return R.ok();
    }

    /**
     * 关阀
     */
    @SaCheckPermission("meter:intelligent:control")
    @Log(title = "智能表清零", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping("/clear/{meterId}")
    public R<Void> clearValve(@NotNull(message = "水表ID不能为空") @PathVariable Long meterId) {
        intelligentMeterService.clearValve(meterId);
        return R.ok();
    }

    /**
     * 阀门摆动
     */
    @SaCheckPermission("meter:intelligent:control")
    @Log(title = "智能表阀门摆动", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping("/swing/{meterId}")
    public R<Void> swingValve(@NotNull(message = "水表ID不能为空") @PathVariable Long meterId) {
        intelligentMeterService.swingValve(meterId);
        return R.ok();
    }

    /**
     * 记录抄表数据
     */
    @SaCheckPermission("meter:intelligent:record")
    @Log(title = "智能表抄表记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/reading")
    public R<Void> recordMeterReading(
        @NotNull(message = "水表ID不能为空") @RequestParam Long meterId,
        @NotNull(message = "读数值不能为空") @RequestParam Double readingValue,
        @RequestParam(required = false) String operator,
        @RequestParam(required = false) String remark) {
        intelligentMeterService.recordMeterReading(meterId, readingValue, operator, remark);
        return R.ok();
    }
}
