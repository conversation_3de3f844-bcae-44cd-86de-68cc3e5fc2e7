package org.dromara.waterfee.app.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.waterfee.app.domain.bo.AppUserBillConsumptionBo;
import org.dromara.waterfee.app.domain.bo.AppUserBillDetailBo;
import org.dromara.waterfee.app.domain.vo.AppUserBillConsumptionVo;
import org.dromara.waterfee.app.domain.vo.AppUserBillDetailVo;
import org.dromara.waterfee.app.service.IAppUserBillService;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * App用户账单查询接口
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/user/bill")
public class WaterfeeAppUserBillController {

    private final IWaterfeeUserService userService;
    private final IAppUserBillService appUserBillService;

    /**
     * 根据用户编号和年份获取用水量信息
     *
     * @param userNo 用户编号
     * @param year   查询年份
     * @return 用水量信息列表
     */
//    @SaCheckLogin
    @GetMapping("/consumption")
    public R<List<AppUserBillConsumptionVo>> getUserConsumption(
        @NotBlank(message = "用户编号不能为空") @RequestParam String userNo,
        @NotBlank(message = "年份不能为空") @RequestParam String year) {

        // 1. 根据用户编号获取用户信息
        WaterfeeUser user = userService.queryByUserNo(userNo);
        if (user == null) {
            return R.fail("未找到该用户");
        }

        // 2. 构建查询业务对象
        AppUserBillConsumptionBo bo = new AppUserBillConsumptionBo();
        bo.setUserId(user.getUserId());
        bo.setYear(year);

        // 3. 调用服务层获取用水量信息
        List<AppUserBillConsumptionVo> consumptionList = appUserBillService.getUserConsumption(bo);

        return R.ok(consumptionList);
    }

    /**
     * 根据用户编号和年月获取详细账单信息
     *
     * @param userNo    用户编号
     * @param yearMonth 年月(格式：yyyy-MM)
     * @return 详细账单信息
     */
//    @SaCheckLogin
    @GetMapping("/detail")
    public R<AppUserBillDetailVo> getUserBillDetail(
        @NotBlank(message = "用户编号不能为空") @RequestParam String userNo,
        @NotBlank(message = "年月不能为空") @RequestParam String yearMonth) throws JsonProcessingException {

        // 1. 根据用户编号获取用户信息
        WaterfeeUser user = userService.queryByUserNo(userNo);
        if (user == null) {
            return R.fail("未找到该用户");
        }

        // 2. 构建查询业务对象
        AppUserBillDetailBo bo = new AppUserBillDetailBo();
        bo.setUserId(user.getUserId());
        bo.setUserNo(userNo);
        bo.setYearMonth(yearMonth);

        // 3. 调用服务层获取详细账单信息
        AppUserBillDetailVo detailVo = appUserBillService.getUserBillDetail(bo);
        if (detailVo == null) {
            return R.fail("未找到账单信息");
        }

        return R.ok(detailVo);
    }
}
