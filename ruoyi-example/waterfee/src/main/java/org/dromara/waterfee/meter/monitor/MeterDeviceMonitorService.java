package org.dromara.waterfee.meter.monitor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MeterDeviceMonitorService {

    private final MeterDeviceMetrics metrics;
    private final MeterDeviceAlertService alertService;

    @Scheduled(fixedRate = 300000) // 每5分钟检查一次
    public void checkDeviceStatus() {
        try {
            double successRate = metrics.getSuccessRate();
            if (successRate < 0.8) { // 成功率低于80%触发告警
//                alertService.sendAlert(
//                    "智能水表通信异常告警",
//                    String.format("当前通信成功率: %.2f%%，请检查设备状态", successRate * 100)
//                );
            }
        } catch (Exception e) {
            // 捕获并记录异常，防止定时任务失败
            log.error("检查设备状态时发生异常", e);
        }
    }
}
