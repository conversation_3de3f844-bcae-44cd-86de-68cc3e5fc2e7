package org.dromara.waterfee.invoice.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 诺诺发票平台访问令牌对象 waterfee_nuonuo_token
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("invoice_nuonuo_token")
public class NuonuoToken extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 令牌ID
     */
    @TableId
    private Long tokenId;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 过期时间（秒）
     */
    private Integer expiresIn;

    /**
     * 过期时间点
     */
    private Date expireTime;

    /**
     * 是否有效（0有效 1无效）
     */
    private int isValid;

}
