package org.dromara.waterfee.meter.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterAlertBo;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterAlertVo;

import java.util.Collection;
import java.util.List;

/**
 * 设备告警Service接口
 *
 * <AUTHOR>
 * @date 2025-04-20
 */
public interface IWaterfeeMeterAlertService {

    /**
     * 查询设备告警
     *
     * @param alertId 主键
     * @return 设备告警
     */
    WaterfeeMeterAlertVo queryById(Long alertId);

    /**
     * 查询设备告警列表
     *
     * @param bo 设备告警
     * @return 设备告警集合
     */
    TableDataInfo<WaterfeeMeterAlertVo> queryPageList(WaterfeeMeterAlertBo bo, PageQuery pageQuery);

    /**
     * 查询设备告警列表
     *
     * @param bo 设备告警
     * @return 设备告警集合
     */
    List<WaterfeeMeterAlertVo> queryList(WaterfeeMeterAlertBo bo);

    /**
     * 新增设备告警
     *
     * @param bo 设备告警
     * @return 结果
     */
    Boolean insertByBo(WaterfeeMeterAlertBo bo);

    /**
     * 修改设备告警
     *
     * @param bo 设备告警
     * @return 结果
     */
    Boolean updateByBo(WaterfeeMeterAlertBo bo);

    /**
     * 校验并批量删除设备告警
     *
     * @param ids 需要删除的设备告警主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 处理设备告警
     *
     * @param alertId 告警ID
     * @param processResult 处理结果
     * @return 结果
     */
    Boolean processAlert(Long alertId, String processResult);
}
