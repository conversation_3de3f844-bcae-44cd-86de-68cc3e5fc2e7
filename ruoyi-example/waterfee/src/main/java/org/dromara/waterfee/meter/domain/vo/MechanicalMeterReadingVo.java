package org.dromara.waterfee.meter.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.waterfee.meter.domain.WaterfeeMeter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 机械表读数视图对象
 *
 * @author: wangjs
 * @update: 2025-04-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeMeter.class)
public class MechanicalMeterReadingVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 水表ID
     */
    @ExcelProperty(value = "水表ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long meterId;

    /**
     * 水表编号
     */
    @ExcelProperty(value = "水表编号")
    private String meterNo;

    /**
     * 读数
     */
    @ExcelProperty(value = "读数")
    private BigDecimal reading;

    /**
     * 上次抄表日期
     */
    @ExcelProperty(value = "上次抄表日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date lastReadDate;

    /**
     * 抄表周期
     */
    @ExcelProperty(value = "抄表周期")
    private String readCycle;

    /**
     * 抄表员
     */
    @ExcelProperty(value = "抄表员")
    private String readerName;

    /**
     * 安装地址
     */
    @ExcelProperty(value = "安装地址")
    private String installAddress;

    /**
     * 业务区域ID
     */
    @ExcelProperty(value = "业务区域ID")
    private Long businessAreaId;

    /**
     * 业务区域
     */
    @ExcelProperty(value = "业务区域")
    private String businessAreaName;

    /**
     * 抄表手册ID
     */
    @ExcelProperty(value = "抄表手册ID")
    private Long meterBookId;
 
    /**
     * 抄表手册
     */
    @ExcelProperty(value = "抄表手册")
    private String meterBookName;

    /**
     * 生产厂家
     */
    @ExcelProperty(value = "生产厂家", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "meter_factory")
    private String manufacturer;

    /**
     * 水表类别(自来水表、中水表、直饮水表)
     */
    @ExcelProperty(value = "水表类别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_meter_category")
    private String meterCategory;

    /**
     * 水表分类(总表、分表)
     */
    @ExcelProperty(value = "水表分类", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_meter_classification")
    private String meterClassification;

    /**
     * 计量用途(居民用水、非居民用水)
     */
    @ExcelProperty(value = "计量用途", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_measurement_purpose")
    private String measurementPurpose;

    /**
     * 口径
     */
    @ExcelProperty(value = "口径", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "dnmm")
    private String caliber;

    /**
     * 精度
     */
    @ExcelProperty(value = "精度", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "water_meter_accuracy")
    private String accuracy;

    /**
     * 初始读数
     */
    @ExcelProperty(value = "初始读数")
    private BigDecimal initialReading;

    /**
     * 安装日期
     */
    @ExcelProperty(value = "安装日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date installDate;

    /**
     * 表倍率
     */
    @ExcelProperty(value = "表倍率")
    private BigDecimal meterRatio;

    /**
     * 关联用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 关联用户编号
     */
    @ExcelProperty(value = "关联用户编号")
    private String userNo;

    /**
     * 关联用户名称
     */
    @ExcelProperty(value = "关联用户名称")
    private String userName;

    /**
     * 用水性质(居民、商业、工业、特种、洗浴)
     */
    @ExcelProperty(value = "用水性质", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_water_nature")
    private String waterNature;

    /**
     * 价格名称
     */
    @ExcelProperty(value = "价格名称")
    private String priceName;

    /**
     * 是否启用违约金
     */
    @ExcelProperty(value = "是否启用违约金", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "false=否,true=是")
    private Boolean penaltyEnabled;

    /**
     * 违约金类型(固定金额、固定利率、日利率)
     */
    @ExcelProperty(value = "违约金类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_penalty_type")
    private String penaltyType;

    /**
     * 违约金值
     */
    @ExcelProperty(value = "违约金值")
    private BigDecimal penaltyValue;

    /**
     * 是否启用附加费
     */
    @ExcelProperty(value = "是否启用附加费", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "false=否,true=是")
    private Boolean additionalFeesEnabled;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}
