package org.dromara.waterfee.pay.mapper;

import org.dromara.waterfee.pay.domain.WechatRefundRecord;
import org.dromara.waterfee.pay.domain.vo.WechatRefundRecordVo;
import org.dromara.waterfee.pay.domain.bo.WechatRefundRecordBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 微信退款记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
public interface WechatRefundRecordMapper extends BaseMapperPlus<WechatRefundRecord, WechatRefundRecordVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WechatRefundRecordVo>> P selectVoPage(IPage<WechatRefundRecord> page, Wrapper<WechatRefundRecord> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询微信退款记录列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WechatRefundRecordVo> queryList(@Param("page") Page<WechatRefundRecord> page, @Param("query") WechatRefundRecordBo query);

}
