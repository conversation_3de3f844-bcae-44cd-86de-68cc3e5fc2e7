package org.dromara.waterfee.app.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.app.domain.vo.UserMeterInfoVo;
import org.dromara.waterfee.app.service.IAppUserMeterService;
import org.dromara.waterfee.meterReading.domain.bo.MeterReadingRecordBo;
import org.dromara.waterfee.meterReading.service.IMeterReadingRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 客户端返回接口集合
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app")
public class AppApi extends BaseController {

    private final IAppUserMeterService appUserMeterService;
    private final IMeterReadingRecordService meterReadingRecordService;

    /**
     * 根据用户编号或用户名称查询用户和水表信息
     *
     * @param keyword 关键字（用户编号或用户名称）
     * @return 用户和水表信息
     */
    @GetMapping("/user/info/{keyword}")
    public R<UserMeterInfoVo> getUserInfo(@PathVariable("keyword") String keyword) {
        UserMeterInfoVo userInfo = appUserMeterService.queryUserInfo(keyword);
        if (userInfo == null) {
            return R.fail("未找到用户信息");
        }
        return R.ok(userInfo);
    }

    /**
     * 添加抄表记录
     *
     * @param bo 抄表记录业务对象
     * @return 结果
     */
    @Log(title = "添加抄表记录", businessType = BusinessType.INSERT)
    @PostMapping("/meter/reading/add")
    public R<Void> addMeterReading(@Validated @RequestBody MeterReadingRecordBo bo) {
        return toAjax(meterReadingRecordService.insertByBo(bo));
    }
}
