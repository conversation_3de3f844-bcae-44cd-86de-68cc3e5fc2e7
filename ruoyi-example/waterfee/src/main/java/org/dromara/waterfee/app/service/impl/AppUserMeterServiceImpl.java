package org.dromara.waterfee.app.service.impl;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.waterfee.app.domain.vo.UserMeterInfoVo;
import org.dromara.waterfee.app.service.IAppUserMeterService;
import org.dromara.waterfee.area.service.IWaterfeeAreaService;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.meterBook.service.IMeterBookService;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.service.IMeterReadingCommonService;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.springframework.stereotype.Service;

/**
 * App用户水表信息服务实现类
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
@RequiredArgsConstructor
public class AppUserMeterServiceImpl implements IAppUserMeterService {

    private final IWaterfeeUserService waterfeeUserService;
    private final IMeterReadingCommonService meterReadingCommonService;
    private final IWaterfeeMeterService waterfeeMeterService;
    private final IWaterfeeAreaService waterfeeAreaService;
    private final IMeterBookService meterBookService;

    /**
     * 根据用户编号或用户名称查询用户和水表信息
     *
     * @param keyword 关键字（用户编号或用户名称）
     * @return 用户和水表信息
     */
    @Override
    public UserMeterInfoVo queryUserInfo(String keyword) {
        if (StringUtils.isEmpty(keyword)) {
            return null;
        }

        // 先尝试按用户编号查询
        WaterfeeUser user = waterfeeUserService.queryByUserNo(keyword);
        if (user == null) {
            return null;
        }
        return buildUserMeterInfo(user);
    }

    /**
     * 构建用户水表信息
     *
     * @param userVo 用户信息
     * @return 用户水表信息
     */
    private UserMeterInfoVo buildUserMeterInfo(WaterfeeUser userVo) {
        UserMeterInfoVo result = new UserMeterInfoVo();

        // 设置用户信息
        result.setUserId(userVo.getUserId());
        result.setUserNo(userVo.getUserNo());
        result.setUserName(userVo.getUserName());
        result.setAddress(userVo.getAddress());
        result.setPhoneNumber(userVo.getPhoneNumber());
        String meterNo = waterfeeMeterService.queryByUserNo(userVo.getUserNo()).get(0).getMeterNo();

        result.setMeterNo(meterNo);

        // 如果有水表编号，查询水表信息
        if (StringUtils.isNotEmpty(meterNo)) {
            // 查询水表信息
            WaterfeeMeterVo meterVo = waterfeeMeterService.queryByNo(meterNo);
            if (meterVo != null) {
                // 设置水表ID和类型
                result.setMeterId(meterVo.getMeterId());
                result.setMeterType(String.valueOf(meterVo.getMeterType()));

                // 查询营业区域信息
                if (meterVo.getBusinessAreaId() != null) {
                    result.setAreaId(meterVo.getBusinessAreaId());
                    // 查询区域名称
                    var areaVo = waterfeeAreaService.queryById(meterVo.getBusinessAreaId());
                    if (areaVo != null) {
                        result.setAreaName(areaVo.getAreaName());
                    }
                }

                // 查询抄表手册信息
                if (meterVo.getMeterBookId() != null) {
                    result.setMeterBookId(meterVo.getMeterBookId());
                    // 查询手册名称
                    var bookVo = meterBookService.queryById(meterVo.getMeterBookId());
                    if (bookVo != null) {
                        result.setBookName(bookVo.getBookName());
                    }
                }
            }

            // 查询水表最新抄表记录
            WaterfeeMeterReadingRecord record = meterReadingCommonService.queryLatestEntityByMeterNo(meterNo);
            if (record != null) {
                result.setLastReading(record.getLastReading());
                result.setLastReadingTime(record.getLastReadingTime());
                result.setCurrentReading(record.getCurrentReading());
                result.setReadingTime(record.getReadingTime());
                result.setWaterUsage(record.getWaterUsage());
            }
        }

        return result;
    }
}
