package org.dromara.waterfee.priceManage.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.waterfee.priceManage.domain.WaterfeePriceConfig;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeePriceConfigVo;

/**
 * 阶梯价格配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface WaterfeePriceConfigMapper extends BaseMapperPlus<WaterfeePriceConfig, WaterfeePriceConfigVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeePriceConfigVo>> P selectVoPage(IPage<WaterfeePriceConfig> page, Wrapper<WaterfeePriceConfig> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }


}
