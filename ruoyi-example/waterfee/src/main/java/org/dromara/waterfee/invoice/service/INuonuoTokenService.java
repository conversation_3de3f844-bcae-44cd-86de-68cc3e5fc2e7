package org.dromara.waterfee.invoice.service;

import org.dromara.waterfee.invoice.domain.bo.NuonuoTokenBo;
import org.dromara.waterfee.invoice.domain.vo.NuonuoTokenVo;

/**
 * 诺诺发票平台访问令牌Service接口
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
public interface INuonuoTokenService {

    /**
     * 查询最新的有效令牌
     *
     * @return 令牌信息
     */
    NuonuoTokenVo getLatestValidToken();

    /**
     * 保存令牌信息
     *
     * @param bo 令牌信息业务对象
     * @return 结果
     */
    Boolean saveToken(NuonuoTokenBo bo);

    /**
     * 将令牌标记为无效
     *
     * @param tokenId 令牌ID
     * @return 结果
     */
    Boolean invalidateToken(Long tokenId);

    /**
     * 初始化令牌
     * 在服务启动时调用，检查令牌是否有效，如果无效则重新获取
     */
    void initToken();

    /**
     * 从Redis获取令牌
     *
     * @return 令牌信息
     */
    String getTokenFromRedis();

    /**
     * 获取有效的访问令牌
     * 优先从Redis获取，如果Redis中不存在，则从数据库获取
     *
     * @return 访问令牌
     */
    String getValidToken();
}
