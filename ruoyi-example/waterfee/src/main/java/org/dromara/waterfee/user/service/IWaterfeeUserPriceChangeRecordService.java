package org.dromara.waterfee.user.service;

import org.dromara.waterfee.user.domain.WaterfeeUserPriceChangeRecord;
import org.dromara.waterfee.user.domain.qo.RecordQo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserPriceChangeRecordVo;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserPriceChangeRecordBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 用水用户用水价格变更记录Service接口
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface IWaterfeeUserPriceChangeRecordService {

    /**
     * 查询用水用户用水价格变更记录
     *
     * @param priceChangeId 主键
     * @return 用水用户用水价格变更记录
     */
    WaterfeeUserPriceChangeRecord queryById(Long priceChangeId);

    /**
     * 分页查询用水用户用水价格变更记录列表
     *
     * @param recordQo        查询条件
     * @param pageQuery 分页参数
     * @return 用水用户用水价格变更记录分页列表
     */
    TableDataInfo<WaterfeeUserPriceChangeRecordVo> queryPageList(RecordQo recordQo, PageQuery pageQuery);

    /**
     * 查询符合条件的用水用户用水价格变更记录列表
     *
     * @param bo 查询条件
     * @return 用水用户用水价格变更记录列表
     */
    List<WaterfeeUserPriceChangeRecordVo> queryList(WaterfeeUserPriceChangeRecordBo bo);

    /**
     * 新增用水用户用水价格变更记录
     *
     * @param bo 用水用户用水价格变更记录
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeUserPriceChangeRecordBo bo);

    /**
     * 修改用水用户用水价格变更记录
     *
     * @param bo 用水用户用水价格变更记录
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeUserPriceChangeRecordBo bo);

    /**
     * 校验并批量删除用水用户用水价格变更记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
