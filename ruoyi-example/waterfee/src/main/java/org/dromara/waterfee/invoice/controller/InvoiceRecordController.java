package org.dromara.waterfee.invoice.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.invoice.domain.InvoiceRecord;
import org.dromara.waterfee.invoice.domain.vo.InvoiceRecordVo;
import org.dromara.waterfee.invoice.domain.bo.InvoiceRecordBo;
import org.dromara.waterfee.invoice.service.IInvoiceRecordService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 发票记录信息
 * 前端访问路由地址为:/invoice/invoiceRecord
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/invoiceRecord")
public class InvoiceRecordController extends BaseController {

    private final IInvoiceRecordService invoiceRecordService;

    /**
     * 查询发票记录信息列表
     */
    @SaCheckPermission("invoice:invoiceRecord:list")
    @GetMapping("/list")
    public TableDataInfo<InvoiceRecordVo> list(InvoiceRecordBo bo, PageQuery pageQuery) {
        return invoiceRecordService.queryPageList(bo, pageQuery);
    }

    @SaCheckPermission("invoice:invoiceRecord:list")
    @GetMapping("/selectList")
    public R selectList(InvoiceRecordBo bo) {
        return R.ok(invoiceRecordService.queryList(bo));
    }

    /**
     * 导出发票记录信息列表
     */
    @SaCheckPermission("invoice:invoiceRecord:export")
    @Log(title = "发票记录信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InvoiceRecordBo bo, HttpServletResponse response) {
        List<InvoiceRecordVo> list = invoiceRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "发票记录信息", InvoiceRecordVo.class, response);
    }

    /**
     * 获取发票记录信息详细信息
     *
     * @param invoiceId 主键
     */
    @SaCheckPermission("invoice:invoiceRecord:query")
    @GetMapping("/{invoiceId}")
    public R<InvoiceRecord> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("invoiceId") Long invoiceId) {
        return R.ok(invoiceRecordService.queryById(invoiceId));
    }

    /**
     * 新增发票记录信息
     */
    @SaCheckPermission("invoice:invoiceRecord:add")
    @Log(title = "发票记录信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InvoiceRecordBo bo) {
        return toAjax(invoiceRecordService.insertByBo(bo));
    }

    /**
     * 修改发票记录信息
     */
    @SaCheckPermission("invoice:invoiceRecord:edit")
    @Log(title = "发票记录信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InvoiceRecordBo bo) {
        return toAjax(invoiceRecordService.updateByBo(bo));
    }

    /**
     * 删除发票记录信息
     *
     * @param invoiceIds 主键串
     */
    @SaCheckPermission("invoice:invoiceRecord:remove")
    @Log(title = "发票记录信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{invoiceIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("invoiceIds") Long[] invoiceIds) {
        return toAjax(invoiceRecordService.deleteWithValidByIds(List.of(invoiceIds), true));
    }
}
