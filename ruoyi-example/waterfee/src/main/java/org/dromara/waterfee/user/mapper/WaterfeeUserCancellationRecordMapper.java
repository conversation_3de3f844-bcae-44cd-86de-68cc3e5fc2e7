package org.dromara.waterfee.user.mapper;

import org.dromara.waterfee.user.domain.WaterfeeUserCancellationRecord;
import org.dromara.waterfee.user.domain.qo.RecordQo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserCancellationRecordVo;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserCancellationRecordBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 用水用户销户记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface WaterfeeUserCancellationRecordMapper extends BaseMapperPlus<WaterfeeUserCancellationRecord, WaterfeeUserCancellationRecordVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeUserCancellationRecordVo>> P selectVoPage(IPage<WaterfeeUserCancellationRecord> page, Wrapper<WaterfeeUserCancellationRecord> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询用水用户销户记录列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WaterfeeUserCancellationRecordVo> queryList(@Param("page") Page<WaterfeeUserCancellationRecord> page, @Param("query") RecordQo query);

}
