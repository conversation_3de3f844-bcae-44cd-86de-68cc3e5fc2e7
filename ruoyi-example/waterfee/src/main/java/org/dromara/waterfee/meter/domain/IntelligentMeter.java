package org.dromara.waterfee.meter.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;
import java.util.Date;

/**
 * 智能表对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_meter")
public class IntelligentMeter extends TenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 水表ID
     */
    @TableId
    private Long meterId;

    /**
     * 水表编号
     */
    private String meterNo;

    /**
     * 水表名称
     */
    private String meterName;

    /**
     * 水表类型（1：机械表 2：智能表）
     */
    private Integer meterType;

    /**
     * 水表口径
     */
    private String caliber;

    /**
     * 水表状态（0：正常 1：停用）
     */
    private String status;

    /**
     * 安装地址
     */
    private String installAddress;

    /**
     * 安装时间
     */
    private Date installTime;

    /**
     * 备注
     */
    private String remark;
} 