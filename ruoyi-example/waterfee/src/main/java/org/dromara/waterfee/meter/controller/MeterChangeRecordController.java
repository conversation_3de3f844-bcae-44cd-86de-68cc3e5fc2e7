package org.dromara.waterfee.meter.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.meter.domain.bo.MeterChangeRecordBo;
import org.dromara.waterfee.meter.domain.vo.MeterChangeRecordVo;
import org.dromara.waterfee.meter.service.IMeterChangeRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 表具变更记录管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/meter/change")
public class MeterChangeRecordController extends BaseController {

    private final IMeterChangeRecordService meterChangeRecordService;

    /**
     * 查询表具变更记录列表
     */
    @SaCheckPermission("meter:change:list")
    @GetMapping("/list")
    public TableDataInfo<MeterChangeRecordVo> list(MeterChangeRecordBo bo, PageQuery pageQuery) {
        return meterChangeRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取表具变更记录详细信息
     */
    @SaCheckPermission("meter:change:query")
    @GetMapping("/{changeId}")
    public R<MeterChangeRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                          @PathVariable Long changeId) {
        return R.ok(meterChangeRecordService.queryById(changeId));
    }

    /**
     * 新增表具变更记录
     */
    @SaCheckPermission("meter:change:add")
    @Log(title = "表具变更记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MeterChangeRecordBo bo) {
        return toAjax(meterChangeRecordService.insertByBo(bo));
    }

    /**
     * 修改表具变更记录
     */
    @SaCheckPermission("meter:change:edit")
    @Log(title = "表具变更记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MeterChangeRecordBo bo) {
        return toAjax(meterChangeRecordService.updateByBo(bo));
    }

    /**
     * 审核表具变更记录
     */
    @SaCheckPermission("meter:change:audit")
    @Log(title = "表具变更记录", businessType = BusinessType.UPDATE)
    @PutMapping("/audit/{changeId}")
    public R<Void> audit(@NotNull(message = "主键不能为空")
                         @PathVariable Long changeId) {
        return toAjax(meterChangeRecordService.auditRecord(changeId));
    }

    /**
     * 删除表具变更记录
     */
    @SaCheckPermission("meter:change:remove")
    @Log(title = "表具变更记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{changeIds}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long[] changeIds) {
        return toAjax(meterChangeRecordService.deleteWithValidByIds(List.of(changeIds)));
    }

    /**
     * 根据水表编号查询变更记录列表
     */
    @SaCheckPermission("meter:change:query")
    @GetMapping("/meter/{meterNo}")
    public R<List<MeterChangeRecordVo>> getByMeterNo(
        @NotBlank(message = "水表编号不能为空")
        @PathVariable String meterNo) {
        return R.ok(meterChangeRecordService.queryByMeterNo(meterNo));
    }
}
