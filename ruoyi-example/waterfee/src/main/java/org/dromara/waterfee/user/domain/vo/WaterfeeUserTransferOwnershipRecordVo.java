package org.dromara.waterfee.user.domain.vo;

import org.dromara.waterfee.user.domain.WaterfeeUserTransferOwnershipRecord;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 用水用户过户记录视图对象 waterfee_user_transfer_ownership_record
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeUserTransferOwnershipRecord.class)
public class WaterfeeUserTransferOwnershipRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long transferId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 原用水人数
     */
    @ExcelProperty(value = "原用水人数")
    private Long beforeUseWaterNumber;

    /**
     * 原用水户名称
     */
    @ExcelProperty(value = "原用水户名称")
    private String beforeUserName;

    /**
     * 原手机号码
     */
    @ExcelProperty(value = "原手机号码")
    private String beforePhoneNumber;

    /**
     * 原证件类型
     */
    @ExcelProperty(value = "原证件类型")
    private String beforeCertificateType;

    /**
     * 原证件号码
     */
    @ExcelProperty(value = "原证件号码")
    private String beforeCertificateNumber;

    /**
     * 原电子邮箱
     */
    @ExcelProperty(value = "原电子邮箱")
    private String beforeEmail;

    /**
     * 新用水人数
     */
    @ExcelProperty(value = "新用水人数")
    private Long afterUseWaterNumber;

    /**
     * 新用水户名称
     */
    @ExcelProperty(value = "新用水户名称")
    private String afterUserName;

    /**
     * 新手机号码
     */
    @ExcelProperty(value = "新手机号码")
    private String afterPhoneNumber;

    /**
     * 新证件类型
     */
    @ExcelProperty(value = "新证件类型")
    private String afterCertificateType;

    /**
     * 新证件号码
     */
    @ExcelProperty(value = "新证件号码")
    private String afterCertificateNumber;

    /**
     * 新电子邮箱
     */
    @ExcelProperty(value = "新电子邮箱")
    private String afterEmail;

    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编号")
    private String userNo;

    /**
     * 创建人名称
     */
    @ExcelProperty(value = "处理人")
    private String CreateByUserName;
}
