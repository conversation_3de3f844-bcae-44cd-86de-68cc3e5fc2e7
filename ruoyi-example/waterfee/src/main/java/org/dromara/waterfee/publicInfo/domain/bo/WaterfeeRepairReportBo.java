package org.dromara.waterfee.repair.domain.bo;

import org.dromara.waterfee.repair.domain.WaterfeeRepairReport;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 报修管理业务对象 waterfee_repair_report
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeRepairReport.class, reverseConvertGenerate = false)
public class WaterfeeRepairReportBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long repairId;

    /**
     * 上报问题
     */
    private String reportContent;

    /**
     * 报修人姓名
     */
    private String reporterName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 报修时间
     */
    private Date reportTime;

    /**
     * 备注
     */
    private String remark;


}
