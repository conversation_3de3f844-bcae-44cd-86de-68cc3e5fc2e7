package org.dromara.waterfee.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.enums.BusinessStatusEnum;
import org.dromara.common.core.enums.FormatsType;
import org.dromara.common.core.enums.WaterfeeUserDictEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.api.RemoteDictService;
import org.dromara.system.api.domain.vo.RemoteDictDataVo;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterBo;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.domain.bo.*;
import org.dromara.waterfee.user.domain.dto.WaterfeeMeterAddDto;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserImportVo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserVo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserWorkFlowVo;
import org.dromara.waterfee.user.listener.WaterfeeUserImportListener;
import org.dromara.waterfee.user.mapper.WaterfeeUserMapper;
import org.dromara.waterfee.user.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 用水用户管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@RequiredArgsConstructor
@Service
public class WaterfeeUserServiceImpl extends ServiceImpl<WaterfeeUserMapper, WaterfeeUser> implements IWaterfeeUserService {

    private final WaterfeeUserMapper baseMapper;

    @Autowired
    private IWaterfeeMeterService waterfeeMeterService;

    @Autowired
    private RemoteDictService remoteDictService;

    /**
     * 查询用水用户管理
     *
     * @param userId 主键
     * @return 用水用户管理
     */
    @Override
    public WaterfeeUser queryById(Long userId) {
        return baseMapper.selectById(userId);
    }

    /**
     * 分页查询用水用户管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用水用户管理分页列表
     */
    @Override
    public TableDataInfo<WaterfeeUserVo> queryPageList(WaterfeeUserBo bo, PageQuery pageQuery) {
        Page<WaterfeeUserVo> result = baseMapper.queryList(pageQuery.build(), bo);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用水用户管理列表
     *
     * @param bo 查询条件
     * @return 用水用户管理列表
     */
    @Override
    public List<WaterfeeUserVo> queryList(WaterfeeUserBo bo) {
        LambdaQueryWrapper<WaterfeeUser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 根据用户编号查询用户信息
     *
     * @param userNo 用户编号
     * @return 用户信息
     */
    @Override
    public WaterfeeUser queryByUserNo(String userNo) {
        if (StringUtils.isEmpty(userNo)) {
            return null;
        }

        LambdaQueryWrapper<WaterfeeUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(WaterfeeUser::getUserNo, userNo);
        lqw.eq(WaterfeeUser::getDelFlag, "0"); // 只查询未删除的用户

        return baseMapper.selectOne(lqw);
    }

    /**
     * 根据用户名称查询用户信息
     *
     * @param userName 用户名称
     * @return 用户信息
     */
    @Override
    public WaterfeeUser queryByUserName(String userName) {
        if (StringUtils.isEmpty(userName)) {
            return null;
        }

        LambdaQueryWrapper<WaterfeeUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(WaterfeeUser::getUserName, userName);
        lqw.eq(WaterfeeUser::getDelFlag, "0"); // 只查询未删除的用户

        return baseMapper.selectOne(lqw);
    }

    private LambdaQueryWrapper<WaterfeeUser> buildQueryWrapper(WaterfeeUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeUser> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(WaterfeeUser::getUserId);
        lqw.eq(StringUtils.isNotBlank(bo.getUserNo()), WaterfeeUser::getUserNo, bo.getUserNo());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerNature()), WaterfeeUser::getCustomerNature, bo.getCustomerNature());
        lqw.eq(StringUtils.isNotBlank(bo.getUseWaterNature()), WaterfeeUser::getUseWaterNature, bo.getUseWaterNature());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), WaterfeeUser::getUserName, bo.getUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getUserStatus()), WaterfeeUser::getUserStatus, bo.getUserStatus());
        return lqw;
    }

    /**
     * 新增用水用户管理
     *
     * @param bo 用水用户管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeUserBo bo) {
        WaterfeeUser add = MapstructUtils.convert(bo, WaterfeeUser.class);
        validEntityBeforeSave(add);
        // 默认审核状态为待审核，即0
//        add.setAuditStatus("0");
        add.setAuditStatus("finish");
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = false;
        //增加重试机制防止编号重复导致新增失败
        int retryCount = 3;
        while (retryCount-- > 0) {
            try {
                // 生成用户编号
                String userNo = generateUserNo(bo.getAreaId(), bo.getCommunityId());
                add.setUserNo(userNo);
                flag = baseMapper.insert(add) > 0;
                break;
            } catch (DuplicateKeyException e) {
                throw new RuntimeException("生成用户编号失败，请重试");
            }
        }
        if (!flag) {
            throw new ServiceException("新增用水用户基本信息失败");
        }
        if (flag) {
            bo.setUserId(add.getUserId());
        }
        return flag;
    }

    /**
     * 修改用水用户管理
     *
     * @param bo 用水用户管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeUserBo bo) {
        WaterfeeUser update = MapstructUtils.convert(bo, WaterfeeUser.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeUser entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除用水用户管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditUser(Long userId) {
        WaterfeeUser user = baseMapper.selectById(userId);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }

        // 检查当前审核状态
        if ("1".equals(user.getAuditStatus())) {
            throw new ServiceException("用户已审核，不能重复审核");
        }

        // 更新审核信息
        WaterfeeUser update = new WaterfeeUser();
        update.setUserId(userId);
        update.setAuditStatus("1"); // 1表示已审核通过，0表示待审核

        // 可以添加审核人、审核时间、审核意见等字段
        // 如果WaterfeeUser中没有这些字段，需要先添加这些字段
        // update.setAuditor(auditor);
        // update.setAuditOpinion(auditOpinion);
        // update.setAuditTime(DateUtils.getNowDate());

        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());

        return baseMapper.updateById(update) > 0;
    }

    @Autowired
    private IWaterfeeUserTransferOwnershipRecordService transferRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean transferOwnership(WaterfeeUserTransferOwnershipRecordBo bo) {
        // 1. 验证用户信息
        WaterfeeUser user = baseMapper.selectById(bo.getUserId());
        if (user == null) {
            throw new ServiceException("用户不存在");
        }
        if (!user.getAuditStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
            throw new ServiceException("用户未审核通过，不能进行过户");
        }

        // 2. 记录原始用户信息到过户记录中
        bo.setBeforeUserName(user.getUserName());
        bo.setBeforeCertificateType(user.getCertificateType());
        bo.setBeforeCertificateNumber(user.getCertificateNumber());
        bo.setBeforePhoneNumber(user.getPhoneNumber());
        bo.setBeforeEmail(user.getEmail());
        bo.setBeforeUseWaterNumber(user.getUseWaterNumber());

        // 3. 更新用户信息
        WaterfeeUser updateUser = new WaterfeeUser();
        updateUser.setUserId(bo.getUserId());
        updateUser.setUserName(bo.getAfterUserName());
        updateUser.setCertificateType(bo.getAfterCertificateType());
        updateUser.setCertificateNumber(bo.getAfterCertificateNumber());
        updateUser.setPhoneNumber(bo.getAfterPhoneNumber());
        updateUser.setEmail(bo.getAfterEmail());
        updateUser.setUseWaterNumber(bo.getAfterUseWaterNumber());
        updateUser.setUpdateBy(LoginHelper.getUserId());
        updateUser.setUpdateTime(DateUtils.getNowDate());

        boolean updateResult = baseMapper.updateById(updateUser) > 0;
        if (!updateResult) {
            throw new ServiceException("更新用户信息失败");
        }

        // 4. 插入过户记录
        boolean recordResult = transferRecordService.insertByBo(bo);
        if (!recordResult) {
            throw new ServiceException("创建过户记录失败");
        }

        return true;
    }

    @Autowired
    private IWaterfeeUserPriceChangeRecordService priceChangeRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateUserPrice(WaterfeeUserPriceChangeRecordBo bo) {
        // 1. 验证用户信息
        WaterfeeUser user = baseMapper.selectById(bo.getUserId());
        if (user == null) {
            throw new ServiceException("用户不存在");
        }
        if (!user.getAuditStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
            throw new ServiceException("用户未审核通过，不能进行价格变更");
        }

        // 2. 记录原始价格信息到变更记录中
//        bo.setBeforePriceUseWaterNature(user.getPriceUseWaterNature());
        bo.setBeforeBillingMethod(user.getBillingMethod());
//        bo.setBeforeIfPenalty(user.getIfPenalty());
//        bo.setBeforePenaltyType(user.getPenaltyType());
//        bo.setBeforeIfExtraCharge(user.getIfExtraCharge());
//        bo.setBeforeExtraChargeType(user.getExtraChargeType());

        // 3. 更新用户价格信息
        WaterfeeUser updateUser = new WaterfeeUser();
        updateUser.setUserId(bo.getUserId());
//        updateUser.setPriceUseWaterNature(bo.getAfterPriceUseWaterNature());
        updateUser.setBillingMethod(bo.getAfterBillingMethod());
//        updateUser.setIfPenalty(bo.getAfterIfPenalty());
//        updateUser.setPenaltyType(bo.getAfterPenaltyType());
//        updateUser.setIfExtraCharge(bo.getAfterIfExtraCharge());
//        updateUser.setExtraChargeType(bo.getAfterExtraChargeType());
        updateUser.setUpdateBy(LoginHelper.getUserId());
        updateUser.setUpdateTime(DateUtils.getNowDate());

        boolean updateResult = baseMapper.updateById(updateUser) > 0;
        if (!updateResult) {
            throw new ServiceException("更新用户价格信息失败");
        }

        // 4. 插入价格变更记录
        boolean recordResult = priceChangeRecordService.insertByBo(bo);
        if (!recordResult) {
            throw new ServiceException("创建价格变更记录失败");
        }

        return true;
    }

    @Autowired
    private IWaterfeeUserDeactivateRecordService deactivateRecordService;

    @Autowired
    private IWaterfeeUserCancellationRecordService cancellationRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean activateUser(Long userId) {
        // 1. 验证用户信息
        WaterfeeUser user = baseMapper.selectById(userId);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }
        if (!user.getAuditStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
            throw new ServiceException("用户未审核通过，不能进行启用");
        }

        // 检查当前状态
        if ("normal".equals(user.getUserStatus())) {
            throw new ServiceException("用户已处于启用状态，无需重复操作");
        }

        // 2. 更新用户状态为"正常"
        WaterfeeUser updateUser = new WaterfeeUser();
        updateUser.setUserId(userId);
        updateUser.setUserStatus("normal");
        updateUser.setUpdateBy(LoginHelper.getUserId());
        updateUser.setUpdateTime(DateUtils.getNowDate());

        return baseMapper.updateById(updateUser) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deactivateUser(WaterfeeUserDeactivateRecordBo bo) {
        // 1. 验证用户信息
        Long userId = bo.getUserId();
        WaterfeeUser user = baseMapper.selectById(userId);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }
        if (!user.getAuditStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
            throw new ServiceException("用户未审核通过，不能进行报停");
        }

        // 检查当前状态
        if ("deactivate".equals(user.getUserStatus())) {
            throw new ServiceException("用户已处于报停状态，无需重复操作");
        }

        // 2. 更新用户状态为"报停"
        WaterfeeUser updateUser = new WaterfeeUser();
        updateUser.setUserId(userId);
        updateUser.setUserStatus("deactivate");
        updateUser.setUpdateBy(LoginHelper.getUserId());
        updateUser.setUpdateTime(DateUtils.getNowDate());

        boolean updateResult = baseMapper.updateById(updateUser) > 0;
        if (!updateResult) {
            throw new ServiceException("更新用户状态失败");
        }

        // 3. 插入报停记录
        bo.setCancellationTime(DateUtils.getNowDate()); // 设置报停时间为当前时间
        bo.setCreateBy(LoginHelper.getUserId());
        bo.setCreateTime(DateUtils.getNowDate());
        bo.setCreateDept(LoginHelper.getDeptId());

        boolean recordResult = deactivateRecordService.insertByBo(bo);
        if (!recordResult) {
            throw new ServiceException("创建报停记录失败");
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancellationUser(WaterfeeUserCancellationRecordBo bo) {
        // 1. 验证用户信息
        Long userId = bo.getUserId();
        WaterfeeUser user = baseMapper.selectById(userId);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }
        if (!user.getAuditStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
            throw new ServiceException("用户未审核通过，不能进行报停");
        }

        // 检查当前状态
        if ("cancellation".equals(user.getUserStatus())) {
            throw new ServiceException("用户已处于销户状态，无需重复操作");
        }

        // 2. 更新用户状态为"销户"
        WaterfeeUser updateUser = new WaterfeeUser();
        updateUser.setDelFlag(String.valueOf(userId));
        updateUser.setUserId(userId);
        updateUser.setUserStatus("cancellation");
        updateUser.setUpdateBy(LoginHelper.getUserId());
        updateUser.setUpdateTime(DateUtils.getNowDate());

        boolean updateResult = baseMapper.updateById(updateUser) > 0;
        if (!updateResult) {
            throw new ServiceException("更新用户状态失败");
        }

        // 3. 插入销户记录
        bo.setCancellationTime(DateUtils.getNowDate()); // 设置销户时间为当前时间
        bo.setCreateBy(LoginHelper.getUserId());
        bo.setCreateTime(DateUtils.getNowDate());
        bo.setCreateDept(LoginHelper.getDeptId());

        boolean recordResult = cancellationRecordService.insertByBo(bo);
        if (!recordResult) {
            throw new ServiceException("创建销户记录失败");
        }

        return true;
    }

    @Autowired
    private IWaterfeeUserBasicInfoChangeRecordService basicInfoChangeRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateUserBasicInfo(WaterfeeUserBasicInfoChangeRecordBo bo) {
        // 1. 验证用户信息
        WaterfeeUser user = baseMapper.selectById(bo.getUserId());
        if (user == null) {
            throw new ServiceException("用户不存在");
        }
        if (!user.getAuditStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
            throw new ServiceException("用户未审核通过，不能进行基础信息变更");
        }

        // 2. 记录原始基础信息到变更记录中
        bo.setBeforeCustomerNature(user.getCustomerNature());
        bo.setBeforeUseWaterNature(user.getUseWaterNature());
        bo.setBeforeUseWaterNumber(user.getUseWaterNumber());
        bo.setBeforePhoneNumber(user.getPhoneNumber());
        bo.setBeforeAddress(user.getAddress());
        bo.setBeforeEmail(user.getEmail());
        bo.setBeforeTaxpayerIdentificationNumber(user.getTaxpayerIdentificationNumber());
        bo.setBeforeInvoiceName(user.getInvoiceName());
        bo.setBeforeInvoiceType(user.getInvoiceType());

        // 3. 构建变更内容描述
        StringBuilder changeContent = new StringBuilder();

        // 获取字典翻译值
        String beforeCustomerNatureLabel = getDictLabel(WaterfeeUserDictEnum.CUSTOMER_NATURE.getDictType(), user.getCustomerNature());
        String afterCustomerNatureLabel = getDictLabel(WaterfeeUserDictEnum.CUSTOMER_NATURE.getDictType(), bo.getAfterCustomerNature());
        String beforeUseWaterNatureLabel = getDictLabel(WaterfeeUserDictEnum.USE_WATER_NATURE.getDictType(), user.getUseWaterNature());
        String afterUseWaterNatureLabel = getDictLabel(WaterfeeUserDictEnum.USE_WATER_NATURE.getDictType(), bo.getAfterUseWaterNature());
        String beforeInvoiceTypeLabel = getDictLabel(WaterfeeUserDictEnum.INVOICE_TYPE.getDictType(), user.getInvoiceType());
        String afterInvoiceTypeLabel = getDictLabel(WaterfeeUserDictEnum.INVOICE_TYPE.getDictType(), bo.getAfterInvoiceType());

        if (!StringUtils.equals(user.getCustomerNature(), bo.getAfterCustomerNature())) {
            changeContent.append("原客户性质：").append(beforeCustomerNatureLabel)
                .append("，变更为：").append(afterCustomerNatureLabel).append("；");
        }

        if (!StringUtils.equals(user.getUseWaterNature(), bo.getAfterUseWaterNature())) {
            changeContent.append("原用水性质：").append(beforeUseWaterNatureLabel)
                .append("，变更为：").append(afterUseWaterNatureLabel).append("；");
        }

        if (!Objects.equals(user.getUseWaterNumber(), bo.getAfterUseWaterNumber())) {
            changeContent.append("原用水人数：").append(user.getUseWaterNumber())
                .append("，变更为：").append(bo.getAfterUseWaterNumber()).append("；");
        }

        if (!StringUtils.equals(user.getPhoneNumber(), bo.getAfterPhoneNumber())) {
            changeContent.append("原手机号码：").append(user.getPhoneNumber())
                .append("，变更为：").append(bo.getAfterPhoneNumber()).append("；");
        }

        if (!StringUtils.equals(user.getAddress(), bo.getAfterAddress())) {
            changeContent.append("原用水地址：").append(user.getAddress())
                .append("，变更为：").append(bo.getAfterAddress()).append("；");
        }

        if (!StringUtils.equals(user.getEmail(), bo.getAfterEmail())) {
            changeContent.append("原电子邮箱：").append(user.getEmail())
                .append("，变更为：").append(bo.getAfterEmail()).append("；");
        }

        if (!StringUtils.equals(user.getTaxpayerIdentificationNumber(), bo.getAfterTaxpayerIdentificationNumber())) {
            changeContent.append("原纳税人识别号：").append(user.getTaxpayerIdentificationNumber())
                .append("，变更为：").append(bo.getAfterTaxpayerIdentificationNumber()).append("；");
        }

        if (!StringUtils.equals(user.getInvoiceName(), bo.getAfterInvoiceName())) {
            changeContent.append("原开票名称：").append(user.getInvoiceName())
                .append("，变更为：").append(bo.getAfterInvoiceName()).append("；");
        }

        if (!StringUtils.equals(user.getInvoiceType(), bo.getAfterInvoiceType())) {
            changeContent.append("原发票类型：").append(beforeInvoiceTypeLabel)
                .append("，变更为：").append(afterInvoiceTypeLabel).append("；");
        }

        // 设置变更内容
        bo.setChangeContent(changeContent.toString());

        // 4. 更新用户基础信息
        WaterfeeUser updateUser = new WaterfeeUser();
        updateUser.setUserId(bo.getUserId());
        updateUser.setCustomerNature(bo.getAfterCustomerNature());
        updateUser.setUseWaterNature(bo.getAfterUseWaterNature());
        updateUser.setUseWaterNumber(bo.getAfterUseWaterNumber());
        updateUser.setPhoneNumber(bo.getAfterPhoneNumber());
        updateUser.setAddress(bo.getAfterAddress());
        updateUser.setEmail(bo.getAfterEmail());
        updateUser.setTaxpayerIdentificationNumber(bo.getAfterTaxpayerIdentificationNumber());
        updateUser.setInvoiceName(bo.getAfterInvoiceName());
        updateUser.setInvoiceType(bo.getAfterInvoiceType());
        updateUser.setUpdateBy(LoginHelper.getUserId());
        updateUser.setUpdateTime(DateUtils.getNowDate());

        boolean updateResult = baseMapper.updateById(updateUser) > 0;
        if (!updateResult) {
            throw new ServiceException("更新用户基础信息失败");
        }

        // 5. 插入基础信息变更记录
        boolean recordResult = basicInfoChangeRecordService.insertByBo(bo);
        if (!recordResult) {
            throw new ServiceException("创建基础信息变更记录失败");
        }

        return true;
    }

    /**
     * 获取字典标签
     *
     * @param dictType  字典类型
     * @param dictValue 字典值
     * @return 字典标签
     */
    private String getDictLabel(String dictType, String dictValue) {
        if (StringUtils.isAnyBlank(dictType, dictValue)) {
            return dictValue;
        }

        List<RemoteDictDataVo> dictDataList = remoteDictService.selectDictDataByType(dictType);
        if (dictDataList == null || dictDataList.isEmpty()) {
            return dictValue;
        }

        for (RemoteDictDataVo dict : dictDataList) {
            if (dictValue.equals(dict.getDictValue())) {
                return dict.getDictLabel();
            }
        }

        return dictValue;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchActivateUsers(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            throw new ServiceException("用户ID列表不能为空");
        }

        for (Long userId : userIds) {
            // 1. 验证用户信息
            WaterfeeUser user = baseMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }
            if (!user.getAuditStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
                throw new ServiceException("用户未审核通过，不能进行启用");
            }

            // 检查当前状态
            if ("normal".equals(user.getUserStatus())) {
                throw new RuntimeException("用户编号" + user.getUserNo() + "的用户已处于启用状态");
            }

            // 2. 更新用户状态为"正常"
            WaterfeeUser updateUser = new WaterfeeUser();
            updateUser.setUserId(userId);
            updateUser.setUserStatus("normal");
            updateUser.setUpdateBy(LoginHelper.getUserId());
            updateUser.setUpdateTime(DateUtils.getNowDate());

            boolean updateResult = baseMapper.updateById(updateUser) > 0;
            if (!updateResult) {
                throw new RuntimeException("用户编号" + user.getUserNo() + "的用户状态更新失败");
            }
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeactivateUsers(WaterfeeUserBatchDeactivateBo bo) {
        List<Long> userIds = bo.getUserIds();
        if (userIds == null || userIds.isEmpty()) {
            throw new ServiceException("用户ID列表不能为空");
        }

        for (Long userId : userIds) {
            // 1. 验证用户信息
            WaterfeeUser user = baseMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }
            if (!user.getAuditStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
                throw new ServiceException("用户未审核通过，不能进行报停");
            }

            // 检查当前状态
            if ("deactivate".equals(user.getUserStatus())) {
                throw new RuntimeException("用户编号" + user.getUserNo() + "的用户已处于报停状态");
            }

            // 2. 更新用户状态为"报停"
            WaterfeeUser updateUser = new WaterfeeUser();
            updateUser.setUserId(userId);
            updateUser.setUserStatus("deactivate");
            updateUser.setUpdateBy(LoginHelper.getUserId());
            updateUser.setUpdateTime(DateUtils.getNowDate());

            boolean updateResult = baseMapper.updateById(updateUser) > 0;
            if (!updateResult) {
                throw new RuntimeException("用户编号" + user.getUserNo() + "的用户状态更新失败");
            }

            // 3. 插入报停记录
            WaterfeeUserDeactivateRecordBo recordBo = new WaterfeeUserDeactivateRecordBo();
            recordBo.setUserId(userId);
            recordBo.setDeactivateReason(bo.getDeactivateReason());
            recordBo.setCancellationTime(DateUtils.getNowDate()); // 设置报停时间为当前时间
            recordBo.setCreateBy(LoginHelper.getUserId());
            recordBo.setCreateTime(DateUtils.getNowDate());
            recordBo.setCreateDept(LoginHelper.getDeptId());

            boolean recordResult = deactivateRecordService.insertByBo(recordBo);
            if (!recordResult) {
                throw new RuntimeException("用户编号" + user.getUserNo() + "的用户创建报停记录失败");
            }
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchCancellationUsers(WaterfeeUserBatchCancellationBo bo) {
        List<Long> userIds = bo.getUserIds();
        if (userIds == null || userIds.isEmpty()) {
            throw new ServiceException("用户ID列表不能为空");
        }

        for (Long userId : userIds) {
            // 1. 验证用户信息
            WaterfeeUser user = baseMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }
            if (!user.getAuditStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
                throw new ServiceException("用户未审核通过，不能进行销户");
            }

            // 检查当前状态
            if ("cancellation".equals(user.getUserStatus())) {
                throw new RuntimeException("用户编号" + user.getUserNo() + "的用户已处于销户状态");
            }

            // 2. 更新用户状态为"销户"
            WaterfeeUser updateUser = new WaterfeeUser();
            updateUser.setUserId(userId);
            updateUser.setUserStatus("cancellation");
            updateUser.setUpdateBy(LoginHelper.getUserId());
            updateUser.setUpdateTime(DateUtils.getNowDate());

            boolean updateResult = baseMapper.updateById(updateUser) > 0;
            if (!updateResult) {
                throw new RuntimeException("用户编号" + user.getUserNo() + "的用户状态更新失败");
            }

            // 3. 插入销户记录
            WaterfeeUserCancellationRecordBo recordBo = new WaterfeeUserCancellationRecordBo();
            recordBo.setUserId(userId);
            recordBo.setCancellationReason(bo.getCancellationReason());
            recordBo.setCancellationTime(DateUtils.getNowDate()); // 设置销户时间为当前时间
            recordBo.setCreateBy(LoginHelper.getUserId());
            recordBo.setCreateTime(DateUtils.getNowDate());
            recordBo.setCreateDept(LoginHelper.getDeptId());

            boolean recordResult = cancellationRecordService.insertByBo(recordBo);
            if (!recordResult) {
                throw new RuntimeException("用户编号" + user.getUserNo() + "的用户创建销户记录失败");
            }
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WaterfeeUser insertUserBasicInfo(WaterfeeUserBasicInfoBo bo) {
        // 1. 验证基本信息
        if (bo == null) {
            throw new ServiceException("用户基本信息不能为空");
        }

        WaterfeeUser user = new WaterfeeUser();
        BeanUtils.copyProperties(bo, user);

        // 3. 设置额外信息
        // 设置审核状态默认为草稿 后续变化通过工作流监听器变更
        user.setAuditStatus(BusinessStatusEnum.DRAFT.getStatus());

        // 设置创建信息
        user.setCreateBy(LoginHelper.getUserId());
        user.setCreateTime(DateUtils.getNowDate());
        user.setCreateDept(LoginHelper.getDeptId());

        // 4. 插入用户记录
        boolean result = false;
        //增加重试机制防止编号重复导致新增失败
//        int retryCount = 3;
//        while (retryCount-- > 0) {
//            try {
//                // 生成用户编号
//                String userNo = generateUserNo(bo.getAreaId(), bo.getCommunityId());
//                user.setUserNo(userNo);
//                result = baseMapper.insert(user) > 0;
//                break;
//            } catch (DuplicateKeyException e) {
//                throw new RuntimeException("生成用户编号失败，请重试");
//            }
//        }
        result = baseMapper.insert(user) > 0;
        if (!result) {
            throw new ServiceException("新增用水用户基本信息失败");
        }

        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WaterfeeUser updateUserDraftBasicInfo(WaterfeeUserBasicInfoBo bo) {
        // 1. 验证用户信息
        WaterfeeUser user = baseMapper.selectById(bo.getUserId());
        if (user == null) {
            throw new ServiceException("用户不存在");
        }
        if (!user.getAuditStatus().equals(BusinessStatusEnum.DRAFT.getStatus()) && !user.getUserStatus().equals(BusinessStatusEnum.WAITING_INPUT_INFO.getStatus())) {
            throw new ServiceException("流程已开始或完成，不能直接修改用户信息");
        }

        WaterfeeUser editUser = new WaterfeeUser();
        BeanUtils.copyProperties(bo, editUser);

        // 3. 设置额外信息
        // 设置审核状态默认为草稿 后续变化通过工作流监听器变更
        editUser.setAuditStatus(BusinessStatusEnum.DRAFT.getStatus());

        // 设置创建信息
        editUser.setUpdateBy(LoginHelper.getUserId());
        editUser.setUpdateTime(DateUtils.getNowDate());

        baseMapper.updateById(editUser);
        return user;
    }

    /**
     * 生成用户编号
     *
     * @return 用户编号
     */
    public String generateUserNo(Long areaId, Long communityId) {
        // 获取当前日期，格式为yyyyMMdd
        String dateStr = DateUtils.dateTimeNow(FormatsType.YYYYMMDD);

        // 查询当天最大的用户编号
        String prefix = dateStr + String.format("%03d", areaId) + String.format("%05d", communityId);
        LambdaQueryWrapper<WaterfeeUser> lqw = Wrappers.lambdaQuery();
        lqw.likeRight(WaterfeeUser::getUserNo, prefix)
            .orderByDesc(WaterfeeUser::getUserNo)
            .last("limit 1");
        WaterfeeUser maxUser = baseMapper.selectOne(lqw);

        int sequence = 1;
        if (maxUser != null && maxUser.getUserNo() != null) {
            // 提取序列号并加1
            String maxUserNo = maxUser.getUserNo();
            if (maxUserNo.length() > prefix.length()) {
                String sequenceStr = maxUserNo.substring(prefix.length());
                try {
                    sequence = Integer.parseInt(sequenceStr) + 1;
                } catch (NumberFormatException e) {
                    // 如果解析失败，使用默认值1
                    sequence = 1;
                }
            }
        }

        // 生成新的用户编号：日期(8位) + 区域ID(3位) + 小区ID(5位) + 序列号(4位)
        return prefix + String.format("%04d", sequence);
    }
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public Boolean bindUserMeter(WaterfeeMeterAddDto dto) {
//        // 1. 验证用户是否存在
//        Long userId = dto.getUserId();
//        if (userId == null) {
//            throw new ServiceException("用户ID不能为空");
//        }
//
//        WaterfeeUser user = baseMapper.selectById(userId);
//        if (user == null) {
//            throw new ServiceException("用户不存在");
//        }
//
//        // 2. 处理水表信息
//        String meterNo;
//        if (dto.isFlag()) {
//            // 水表已存在，直接使用
//            meterNo = dto.getWaterfeeMeterBo().getMeterNo();
//            if (StringUtils.isBlank(meterNo)) {
//                throw new ServiceException("水表编号不能为空");
//            }
//            WaterfeeUser meterUser = this.selectUserByMeterNo(meterNo);
//            if (meterUser != null && !meterUser.getUserId().equals(userId)) {
//                throw new ServiceException("该水表已被其他用户绑定");
//            }
//        } else {
//            // 需要新增水表
//            WaterfeeMeterBo meterBo = dto.getWaterfeeMeterBo();
//            if (meterBo == null) {
//                throw new ServiceException("水表信息不能为空");
//            }
//            if (StringUtils.isBlank(meterBo.getMeterNo())) {
//                throw new ServiceException("水表编号不能为空");
//            }
//            WaterfeeUser meterUser = this.selectUserByMeterNo(meterBo.getMeterNo());
//            if (meterUser != null && !meterUser.getUserId().equals(userId)) {
//                throw new ServiceException("该水表已被其他用户绑定");
//            }
//            // 设置创建者信息
//            meterBo.setCreateBy(LoginHelper.getUserId());
//            meterBo.setCreateTime(DateUtils.getNowDate());
//            meterBo.setCreateDept(LoginHelper.getDeptId());
//
//            // 新增水表
//            boolean result = waterfeeMeterService.insertByBo(meterBo);
//            if (!result) {
//                throw new ServiceException("新增水表失败");
//            }
//            meterNo = meterBo.getMeterNo();
//        }
//        WaterfeeUser waterfeeUser = new WaterfeeUser() {{
//            setUserId(userId);
////            setMeterNo(meterNo);
//        }};
//
//        return baseMapper.updateById(waterfeeUser) > 0;
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateUserPriceInfo(WaterfeeUserPriceBo bo) {
        // 1. 验证用户是否存在
        Long userId = bo.getUserId();
        if (userId == null) {
            throw new ServiceException("用户ID不能为空");
        }

        WaterfeeUser user = baseMapper.selectById(userId);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }

        WaterfeeUser updateUser = new WaterfeeUser();
        BeanUtils.copyProperties(bo, updateUser);

        // 3. 设置更新信息
        updateUser.setUpdateBy(LoginHelper.getUserId());
        updateUser.setUpdateTime(DateUtils.getNowDate());

        // 4. 执行更新操作
        boolean result = baseMapper.updateById(updateUser) > 0;
        if (!result) {
            throw new ServiceException("设定用户价格信息失败");
        }

        return true;
    }

    @Override
    public WaterfeeUserWorkFlowVo workFlowGetDetail(Long userId) {
        WaterfeeUser user = baseMapper.selectById(userId);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }
        WaterfeeUserWorkFlowVo vo = new WaterfeeUserWorkFlowVo();
        BeanUtils.copyProperties(user, vo.getUserBasicInfo());
        BeanUtils.copyProperties(user, vo.getUserPrice());
//        WaterfeeMeterVo meterVo = waterfeeMeterService.queryByNo(user.getMeterNo());
//        if (meterVo != null) {
//            BeanUtils.copyProperties(meterVo, vo.getWaterfeeMeterBo());
//        }
        return vo;
    }

    /**
     * 根据水表编号查询用户信息 未删除的数据
     *
     * @param meterNo
     * @return
     */
//    @Override
//    public WaterfeeUser selectUserByMeterNo(String meterNo) {
//        return baseMapper.selectOne(new LambdaQueryWrapper<WaterfeeUser>().eq(WaterfeeUser::getMeterNo, meterNo)
//            .eq(WaterfeeUser::getDelFlag, "0"));
//    }

    /**
     * 根据证件号和水表编号查询用户 未删除的数据
     *
     * @param
     * @return 用户对象信息
     */
//    @Override
//    public WaterfeeUser selectUserByCertificateTypeAndCertificateNumberAndMeterNo(String certificateType, String certificateNumber, String meterNo) {
//        return baseMapper.selectOne(new LambdaQueryWrapper<WaterfeeUser>()
//            .eq(WaterfeeUser::getCertificateType, certificateType)
//            .eq(WaterfeeUser::getCertificateNumber, certificateNumber)
//            .eq(WaterfeeUser::getMeterNo, meterNo)
//            .eq(WaterfeeUser::getDelFlag, "0")
//            .and(
//                wq -> wq.ne(WaterfeeUser::getAuditStatus, BusinessStatusEnum.INVALID.getStatus()
//                    ).or().ne(WaterfeeUser::getAuditStatus, BusinessStatusEnum.CANCEL.getStatus()).or()
//                    .ne(WaterfeeUser::getAuditStatus, BusinessStatusEnum.TERMINATION.getStatus())
//            ));
//    }

    /**
     * 导入用水用户数据
     *
     * @param file          导入文件
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     * @throws Exception 异常
     */
    @Override
    public String importUser(MultipartFile file, Boolean updateSupport) throws Exception {
        WaterfeeUserImportListener listener = new WaterfeeUserImportListener(updateSupport);
        ExcelUtil.importExcel(file.getInputStream(), WaterfeeUserImportVo.class, listener);
        return listener.getExcelResult().getAnalysis();
    }

    /**
     * 批量设定为重点户
     *
     * @param userIds 用户ID集合
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSetSpecificUsers(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            throw new ServiceException("用户ID列表不能为空");
        }

        // 更新用户为重点户
        for (Long userId : userIds) {
            // 1. 验证用户信息
            WaterfeeUser user = baseMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }
            WaterfeeUser updateUser = new WaterfeeUser();
            updateUser.setUserId(userId);
            updateUser.setIfSpecific("1");
            updateUser.setUpdateBy(LoginHelper.getUserId());
            updateUser.setUpdateTime(DateUtils.getNowDate());
            boolean updateResult = baseMapper.updateById(updateUser) > 0;
            if (!updateResult) {
                throw new RuntimeException("用户编号" + user.getUserNo() + "的用户设为重点户失败");
            }
        }
        return true;
    }

    /**
     * 批量取消重点户
     *
     * @param userIds 用户ID集合
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCancelSpecificUsers(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            throw new ServiceException("用户ID列表不能为空");
        }

        // 更新用户为非重点户
        for (Long userId : userIds) {
            // 1. 验证用户信息
            WaterfeeUser user = baseMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }
            WaterfeeUser updateUser = new WaterfeeUser();
            updateUser.setUserId(userId);
            updateUser.setIfSpecific("0");
            updateUser.setUpdateBy(LoginHelper.getUserId());
            updateUser.setUpdateTime(DateUtils.getNowDate());
            boolean updateResult = baseMapper.updateById(updateUser) > 0;
            if (!updateResult) {
                throw new RuntimeException("用户编号" + user.getUserNo() + "的用户设为非重点户失败");
            }
        }
        return true;
    }

    /**
     * 返回用户余额
     */
    @Override
    public BigDecimal getUserBalance(Long userId) {
        if (userId == null) {
            throw new ServiceException("用户ID不能为空");
        }

        WaterfeeUser user = baseMapper.selectById(userId);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }

        // 获取用户余额
        BigDecimal balance = user.getBalance();
        if (balance == null) {
            balance = BigDecimal.ZERO; // 如果余额为null，返回0
        }
        return balance;
    }
}

