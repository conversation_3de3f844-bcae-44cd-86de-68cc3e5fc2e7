package org.dromara.waterfee.user.lock;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

@Component
public class UserLockManager {

    // 使用ReentrantLock作为用户余额更新的锁
    private final Map<String, ReentrantLock> userBalanceLocks = new ConcurrentHashMap<>();

    // 获取指定用户的锁
    public ReentrantLock getUserLock(String userNo) {
        return userBalanceLocks.computeIfAbsent(userNo, k -> new ReentrantLock());
    }
}
