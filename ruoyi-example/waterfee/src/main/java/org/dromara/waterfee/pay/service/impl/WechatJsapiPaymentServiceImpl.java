package org.dromara.waterfee.pay.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.google.gson.Gson;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.exception.ValidationException;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import com.wechat.pay.java.service.payments.jsapi.model.*;
import com.wechat.pay.java.service.payments.model.Transaction;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.enums.FormatsType;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.waterfee.bill.domain.bo.PaymentDetailBo;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.waterfee.bill.service.IPaymentDetailService;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.dromara.waterfee.mq.config.RabbitConfig;
import org.dromara.waterfee.pay.domain.WechatCloseOrderParam;
import org.dromara.waterfee.pay.domain.WechatPrePayParam;
import org.dromara.waterfee.pay.domain.WechatQueryOrderParam;
import org.dromara.waterfee.pay.service.IWechatJsapiPaymentService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.UUID;

@Slf4j
@Service
public class WechatJsapiPaymentServiceImpl implements IWechatJsapiPaymentService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private IPaymentDetailService paymentDetailService;

    @Autowired
    private IWaterfeeBillService waterfeeBillService;

    private final JsapiServiceExtension jsapiService;

    private final NotificationParser notificationParser;

    /**
     * 商户号
     */
    @Value("${wechatPay.merchant-id}")
    private String merchantId;

    /**
     * 应用ID
     */
    @Value("${wechatPay.app-id}")
    private String appId;

    /**
     * 通知地址
     */
    @Value("${wechatPay.pay-notify-url}")
    private String payNotifyUrl;

    /**
     * 加载配置
     */
    @Autowired
    public WechatJsapiPaymentServiceImpl(Config wechatPayConfig, NotificationConfig notificationConfig) {
        this.jsapiService = new JsapiServiceExtension.Builder()
            .config(wechatPayConfig)
            .build();
        this.notificationParser = new NotificationParser(notificationConfig);
    }

    /**
     * 关闭订单
     */
    public void closeOrder(WechatCloseOrderParam wechatCloseOrderParam) {
        if (wechatCloseOrderParam == null) {
            throw new IllegalArgumentException("关闭订单参数不能为空");
        }
        if (wechatCloseOrderParam.getOutTradeNo() == null || wechatCloseOrderParam.getOutTradeNo().trim().isEmpty()) {
            throw new IllegalArgumentException("商户订单号不能为空");
        }
        CloseOrderRequest request = new CloseOrderRequest();
        // 调用request.setXxx(val)设置所需参数，具体参数可见Request定义
        // 调用接口
        request.setOutTradeNo(wechatCloseOrderParam.getOutTradeNo());
        request.setMchid(merchantId);
        jsapiService.closeOrder(request);
    }

    /**
     * JSAPI支付下单，并返回JSAPI调起支付数据
     */
    @Transactional(rollbackFor = Exception.class)
    public PrepayWithRequestPaymentResponse prepayWithRequestPayment(HttpServletRequest request, HttpServletResponse response, WechatPrePayParam wechatPrePayParam) {
        // 参数校验
        if (wechatPrePayParam == null) {
            throw new IllegalArgumentException("支付参数不能为空");
        }
        if (wechatPrePayParam.getDescription() == null || wechatPrePayParam.getDescription().trim().isEmpty()) {
            throw new IllegalArgumentException("商品描述不能为空");
        }
        if (wechatPrePayParam.getAmount() == null || wechatPrePayParam.getAmount() < 0) {
            throw new IllegalArgumentException("支付金额必须>=0");
        }
        if (wechatPrePayParam.getOpenid() == null || wechatPrePayParam.getOpenid().trim().isEmpty()) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            throw new IllegalArgumentException("用户openid不能为空");
        }
        if (wechatPrePayParam.getUserNo() == null || wechatPrePayParam.getUserNo().trim().isEmpty()) {
            throw new IllegalArgumentException("户号不能为空");
        }

        //商户订单信息创建
        PaymentDetailBo paymentDetailBo = new PaymentDetailBo();
        paymentDetailBo.setPaymentAmount(new BigDecimal(wechatPrePayParam.getAmount()).divide(new BigDecimal(100)));
        paymentDetailBo.setPaymentMethod("WXGZH");
        paymentDetailBo.setPaymentStatus("PENDING");
        paymentDetailBo.setUserNo(wechatPrePayParam.getUserNo());
        paymentDetailBo.setCreateTime(DateUtils.getNowDate());
        paymentDetailBo.setPaymentTime(DateUtils.getNowDate());
        paymentDetailBo.setPaymentDetailId(generatePaymentNumber());
        paymentDetailBo.setOpenid(wechatPrePayParam.getOpenid());
        if(wechatPrePayParam.getIfPayBill().equals(1)) {
            if(ObjectUtil.isNull(wechatPrePayParam.getBillId())) {
                throw new IllegalArgumentException("账单ID不能为空");
            }
            WaterfeeBillVo waterfeeBillVo = waterfeeBillService.queryById(wechatPrePayParam.getBillId());
            if(waterfeeBillVo == null) {
                throw new IllegalArgumentException("账单不存在");
            }
            if(!waterfeeBillVo.getBillStatus().equals("ISSUED")) {
                throw new IllegalArgumentException("账单状态不正确");
            }
            if(waterfeeBillVo.getBalanceDue().compareTo(paymentDetailBo.getPaymentAmount()) != 0) {
                throw new IllegalArgumentException("账单余额与支付金额不一致");
            }
            //设定关联账单id
            paymentDetailBo.setBillId(waterfeeBillVo.getBillNumber());
        }
        paymentDetailService.insertByBo(paymentDetailBo);
        //创建微信支付参数
        PrepayRequest prepayRequest = new PrepayRequest();
        // 调用request.setXxx(val)设置所需参数，具体参数可见Request定义
        // 调用接口
        prepayRequest.setAppid(appId);
        prepayRequest.setMchid(merchantId);
        prepayRequest.setDescription(wechatPrePayParam.getDescription());
        prepayRequest.setOutTradeNo(paymentDetailBo.getPaymentDetailId());

        // 设置支付金额
        Amount amount = new Amount();
        amount.setTotal(wechatPrePayParam.getAmount());
//        amount.setCurrency("CNY"); // 默认人民币
        prepayRequest.setAmount(amount);

        // 设置支付者信息
        Payer payer = new Payer();
        payer.setOpenid(wechatPrePayParam.getOpenid());
        prepayRequest.setPayer(payer);

        // 设置通知地址
        prepayRequest.setNotifyUrl(payNotifyUrl);
        //todo 需要维护未支付订单的数据，方便继续支付 不涉及到未完成订单暂时先不做

        return jsapiService.prepayWithRequestPayment(prepayRequest);
    }


    /**
     * 微信支付订单号查询订单 只能查询支付成功的订单
     */
    public Transaction queryOrderById(WechatQueryOrderParam wechatQueryOrderParam) {
        if (wechatQueryOrderParam == null) {
            throw new IllegalArgumentException("查询订单参数不能为空");
        }
        if (wechatQueryOrderParam.getTransactionId() == null || wechatQueryOrderParam.getTransactionId().trim().isEmpty()) {
            throw new IllegalArgumentException("微信支付订单号不能为空");
        }
        QueryOrderByIdRequest request = new QueryOrderByIdRequest();
        // 调用request.setXxx(val)设置所需参数，具体参数可见Request定义
        // 调用接口
        request.setTransactionId(wechatQueryOrderParam.getTransactionId());
        request.setMchid(merchantId);
        return jsapiService.queryOrderById(request);
    }

    /**
     * 商户订单号查询订单
     */
    public Transaction queryOrderByOutTradeNo(WechatQueryOrderParam wechatQueryOrderParam) {
        if (wechatQueryOrderParam == null) {
            throw new IllegalArgumentException("查询订单参数不能为空");
        }
        if (wechatQueryOrderParam.getOutTradeNo() == null || wechatQueryOrderParam.getOutTradeNo().trim().isEmpty()) {
            throw new IllegalArgumentException("商户订单号不能为空");
        }
        QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
        // 调用request.setXxx(val)设置所需参数，具体参数可见Request定义
        // 调用接口
        request.setOutTradeNo(wechatQueryOrderParam.getOutTradeNo());
        request.setMchid(merchantId);
        return jsapiService.queryOrderByOutTradeNo(request);
    }

    /**
     * 微信支付回调
     */
    public String wechatPayCallBack(HttpServletRequest request, HttpServletResponse response) {
        // 构造 RequestParam
        RequestParam requestParam = new RequestParam.Builder()
            .serialNumber(request.getHeader("Wechatpay-Serial"))
            .nonce(request.getHeader("Wechatpay-Nonce"))
            .signature(request.getHeader("Wechatpay-Signature"))
            .timestamp(request.getHeader("Wechatpay-Timestamp"))
            .body(getRequestBody(request))
            .build();
        Transaction transaction;
        Gson gson = new Gson();
        try {
            // 以支付通知回调为例，验签、解密并转换成 Transaction
            transaction = notificationParser.parse(requestParam, Transaction.class);
        } catch (ValidationException e) {
            // 签名验证失败，返回 401 UNAUTHORIZED 状态码
            log.error("sign verification failed", e);
            response.setStatus(500);
            final HashMap<String, Object> map = new HashMap<>();
            map.put("code", "ERROR");
            map.put("message", "验签失败");
            return gson.toJson(map);
        }
        //根据回调结果更新业务表状态
        final HashMap<String, Object> map = new HashMap<>();
        if (transaction != null) {
            if (transaction.getMchid().equals(merchantId) && transaction.getAppid().equals(appId)) {
                // 推送回调状态到消息队列 异步更新业务信息
                rabbitTemplate.convertAndSend(RabbitConfig.PAY_EXCHANGE, RabbitConfig.PAY_CALL_BACK_ROUTING_KEY, gson.toJson(transaction).getBytes(StandardCharsets.UTF_8));
                response.setStatus(200);
                map.put("code", "SUCCESS");
                map.put("message", "成功");
                return gson.toJson(map);
            } else {
                response.setStatus(500);
                map.put("code", "ERROR");
                map.put("message", "商户号或公众号ID不一致");
                return gson.toJson(map);
            }
        } else {
            response.setStatus(500);
            map.put("code", "ERROR");
            map.put("message", "解析微信支付回调结果失败");
            return gson.toJson(map);
        }
    }

    /**
     * 获取requestBody
     *
     * @param request
     * @return
     */
    private String getRequestBody(HttpServletRequest request) {

        StringBuffer sb = new StringBuffer();

        try (ServletInputStream inputStream = request.getInputStream();
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        ) {
            String line;

            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }

        } catch (IOException e) {
            log.error("读取数据流异常:{}", e);
        }

        return sb.toString();

    }


    /**
     * 生成支付编号
     *
     * @return 支付编号
     */
    private String generatePaymentNumber() {
        Date now = DateUtils.getNowDate();
        String dateStr = DateUtils.parseDateToStr(FormatsType.YYYY_MM_DD, now);
        String uuid = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 8);
        return "PAY" + dateStr + uuid;
    }

}
