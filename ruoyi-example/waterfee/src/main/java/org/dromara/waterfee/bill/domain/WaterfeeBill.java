package org.dromara.waterfee.bill.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

import java.io.Serial;

/**
 * 账单主表对象 waterfee_bills
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_bills")
public class WaterfeeBill extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账单唯一ID
     */
    @TableId(value = "bill_id")
    private Long billId;

    /**
     * 账单编号 (业务)
     */
    private String billNumber;

    /**
     * 关联客户ID
     */
    private Long customerId;

    /**
     * 关联水表ID
     */
    private Long meterId;

    /**
     * 表册ID
     */
    private Long meterBookId;

    /**
     * 价格方案ID
     */
    private Long pricePlanId;

    /**
     * 计费周期开始日期
     */
    private Date billingPeriodStart;

    /**
     * 计费周期结束日期
     */
    private Date billingPeriodEnd;

    /**
     * 账单发行日期
     */
    private Date billingIssueDate;

    /**
     * 账单到期日期
     */
    private Date billingDueDate;

    /**
     * 上期结算读数ID
     */
    private Long previousReadingId;

    /**
     * 本期结算读数ID
     */
    private Long currentReadingId;

    /**
     * 上期读数值
     */
    private BigDecimal previousReadingValue;

    /**
     * 本期读数值
     */
    private BigDecimal currentReadingValue;

    /**
     * 本期消费量
     */
    private BigDecimal consumptionVolume;

    /**
     * 消费量单位
     */
    private String consumptionUnit;

    /**
     * 标准水费 (标准用量费用)
     */
    private BigDecimal baseChargeAmount;

    /**
     * 调整金额 (正数为增加, 负数为减免)
     */
    private BigDecimal adjustmentsAmount;

    /**
     * 账单总金额 (含调整)
     */
    private BigDecimal totalAmount;

    /**
     * 已支付金额
     */
    private BigDecimal amountPaid;

    /**
     * 应付余额 (total_amount - amount_paid)
     */
    private BigDecimal balanceDue;

    /**
     * 账单状态
     */
    private String billStatus;

    /**
     * 账单月份(格式：YYYYMM)
     */
    private String billMonth;

    /**
     * 账单备注
     */
    private String notes;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 用户编号（非数据库字段）
     */
    @TableField(exist = false)
    private String userNo;

    /**
     * 用户名称（非数据库字段）
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 用水地址（非数据库字段）
     */
    @TableField(exist = false)
    private String address;

    /**
     * 详情JSON
     */
    private String detailJson;

    /**
     * 水资源税
     */
    private BigDecimal waterResourceTax;

    /**
     * 污水处理费
     */
    private BigDecimal sewageTreatmentFee;

    /**
     * 仅水费，不含其他
     */
    private BigDecimal waterBillOnly;
}
