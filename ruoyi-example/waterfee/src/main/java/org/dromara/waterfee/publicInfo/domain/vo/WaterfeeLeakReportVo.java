package org.dromara.waterfee.leak.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.waterfee.leak.domain.WaterfeeLeakReport;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 偷漏水举报视图对象 waterfee_leak_report
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeLeakReport.class)
public class WaterfeeLeakReportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long reportId;

    /**
     * 上报人姓名
     */
    @ExcelProperty(value = "上报人姓名")
    private String reporterName;

    /**
     * 上报人电话
     */
    @ExcelProperty(value = "上报人电话")
    private String reporterPhone;

    /**
     * 举报时间
     */
    @ExcelProperty(value = "举报时间")
    private Date reportTime;

    /**
     * 问题描述
     */
    @ExcelProperty(value = "问题描述")
    private String description;

    /**
     * 附件
     */
    @ExcelProperty(value = "附件")
    private String file;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private String lon;

    /**
     * 维度
     */
    @ExcelProperty(value = "维度")
    private String lat;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
