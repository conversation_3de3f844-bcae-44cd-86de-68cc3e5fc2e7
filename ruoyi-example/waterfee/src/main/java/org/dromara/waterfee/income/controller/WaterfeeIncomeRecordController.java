package org.dromara.waterfee.income.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.income.domain.bo.WaterfeeIncomeRecordBo;
import org.dromara.waterfee.income.domain.vo.WaterfeeIncomeRecordVo;
import org.dromara.waterfee.income.domain.vo.WaterfeeIncomeSummaryVo;
import org.dromara.waterfee.income.service.IWaterfeeIncomeRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 收入记录管理
 * 前端访问路由地址为:/income/record
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/income/record")
public class WaterfeeIncomeRecordController extends BaseController {

    private final IWaterfeeIncomeRecordService waterfeeIncomeRecordService;

    /**
     * 查询收入记录列表
     */
    @SaCheckPermission("waterfee:income:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeIncomeRecordVo> list(WaterfeeIncomeRecordBo bo, PageQuery pageQuery) {
        return waterfeeIncomeRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出收入记录列表
     */
    @SaCheckPermission("waterfee:income:export")
    @Log(title = "收入记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeIncomeRecordBo bo, HttpServletResponse response) {
        List<WaterfeeIncomeRecordVo> list = waterfeeIncomeRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "收入记录", WaterfeeIncomeRecordVo.class, response);
    }

    /**
     * 获取收入记录详细信息
     *
     * @param incomeId 主键
     */
    @SaCheckPermission("waterfee:income:query")
    @GetMapping("/{incomeId}")
    public R<WaterfeeIncomeRecordVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long incomeId) {
        return R.ok(waterfeeIncomeRecordService.queryById(incomeId));
    }

    /**
     * 新增收入记录
     */
    @SaCheckPermission("waterfee:income:add")
    @Log(title = "收入记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeIncomeRecordBo bo) {
        return toAjax(waterfeeIncomeRecordService.insertByBo(bo));
    }

    /**
     * 修改收入记录
     */
    @SaCheckPermission("waterfee:income:edit")
    @Log(title = "收入记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeIncomeRecordBo bo) {
        return toAjax(waterfeeIncomeRecordService.updateByBo(bo));
    }

    /**
     * 删除收入记录
     *
     * @param incomeIds 主键串
     */
    @SaCheckPermission("waterfee:income:remove")
    @Log(title = "收入记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{incomeIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] incomeIds) {
        return toAjax(waterfeeIncomeRecordService.deleteWithValidByIds(List.of(incomeIds), true));
    }

    /**
     * 根据用户编号查询收入记录
     *
     * @param userNo 用户编号
     */
    @SaCheckPermission("waterfee:income:query")
    @GetMapping("/user/{userNo}")
    public R<List<WaterfeeIncomeRecordVo>> getListByUserNo(
        @NotBlank(message = "用户编号不能为空") @PathVariable String userNo) {
        return R.ok(waterfeeIncomeRecordService.queryListByUserNo(userNo));
    }

    /**
     * 统计不同收入类型的金额
     */
    @SaCheckPermission("waterfee:income:summary")
    @GetMapping("/summary")
    public R<WaterfeeIncomeSummaryVo> getIncomeSummary() {
        return R.ok(waterfeeIncomeRecordService.getIncomeSummary());
    }
}
