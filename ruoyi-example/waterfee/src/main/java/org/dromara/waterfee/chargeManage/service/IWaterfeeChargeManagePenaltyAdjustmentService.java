package org.dromara.waterfee.chargeManage.service;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManagePenaltyAdjustment;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManagePenaltyAdjustmentVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManagePenaltyAdjustmentBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 违约金调整/减免Service接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface IWaterfeeChargeManagePenaltyAdjustmentService {

    /**
     * 查询违约金调整/减免
     *
     * @param id 主键
     * @return 违约金调整/减免
     */
    WaterfeeChargeManagePenaltyAdjustment queryById(Long id);

    /**
     * 分页查询违约金调整/减免列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 违约金调整/减免分页列表
     */
    TableDataInfo<WaterfeeChargeManagePenaltyAdjustmentVo> queryPageList(WaterfeeChargeManagePenaltyAdjustmentBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的违约金调整/减免列表
     *
     * @param bo 查询条件
     * @return 违约金调整/减免列表
     */
    List<WaterfeeChargeManagePenaltyAdjustmentVo> queryList(WaterfeeChargeManagePenaltyAdjustmentBo bo);

    /**
     * 新增违约金调整/减免
     *
     * @param bo 违约金调整/减免
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeChargeManagePenaltyAdjustmentBo bo);

    /**
     * 修改违约金调整/减免
     *
     * @param bo 违约金调整/减免
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeChargeManagePenaltyAdjustmentBo bo);

    /**
     * 校验并批量删除违约金调整/减免信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
