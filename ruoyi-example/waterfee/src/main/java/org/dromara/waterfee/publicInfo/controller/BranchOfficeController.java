package org.dromara.waterfee.branch.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.branch.domain.BranchOffice;
import org.dromara.waterfee.branch.domain.vo.BranchOfficeVo;
import org.dromara.waterfee.branch.domain.bo.BranchOfficeBo;
import org.dromara.waterfee.branch.service.IBranchOfficeService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 营业网点
 * 前端访问路由地址为:/branch/branchOffice
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/branchOffice")
public class BranchOfficeController extends BaseController {

    private final IBranchOfficeService branchOfficeService;

    /**
     * 查询营业网点列表
     */
    @SaCheckPermission("waterfee:branchOffice:list")
    @GetMapping("/list")
    public TableDataInfo<BranchOfficeVo> list(BranchOfficeBo bo, PageQuery pageQuery) {
        return branchOfficeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出营业网点列表
     */
    @SaCheckPermission("waterfee:branchOffice:export")
    @Log(title = "营业网点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BranchOfficeBo bo, HttpServletResponse response) {
        List<BranchOfficeVo> list = branchOfficeService.queryList(bo);
        ExcelUtil.exportExcel(list, "营业网点", BranchOfficeVo.class, response);
    }

    /**
     * 获取营业网点详细信息
     *
     * @param branchId 主键
     */
    @SaCheckPermission("waterfee:branchOffice:query")
    @GetMapping("/{branchId}")
    public R<BranchOffice> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("branchId") Long branchId) {
        return R.ok(branchOfficeService.queryById(branchId));
    }

    /**
     * 新增营业网点
     */
    @SaCheckPermission("waterfee:branchOffice:add")
    @Log(title = "营业网点", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BranchOfficeBo bo) {
        return toAjax(branchOfficeService.insertByBo(bo));
    }

    /**
     * 修改营业网点
     */
    @SaCheckPermission("waterfee:branchOffice:edit")
    @Log(title = "营业网点", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BranchOfficeBo bo) {
        return toAjax(branchOfficeService.updateByBo(bo));
    }

    /**
     * 删除营业网点
     *
     * @param branchIds 主键串
     */
    @SaCheckPermission("waterfee:branchOffice:remove")
    @Log(title = "营业网点", businessType = BusinessType.DELETE)
    @DeleteMapping("/{branchIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("branchIds") Long[] branchIds) {
        return toAjax(branchOfficeService.deleteWithValidByIds(List.of(branchIds), true));
    }
}
