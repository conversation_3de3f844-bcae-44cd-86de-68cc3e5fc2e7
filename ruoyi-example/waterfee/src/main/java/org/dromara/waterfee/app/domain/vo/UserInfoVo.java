package org.dromara.waterfee.app.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户基本信息视图对象
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
public class UserInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 地址
     */
    private String address;

    /**
     * 季度用水量
     */
    private Double ladderUsage;
}
