package org.dromara.waterfee.user.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.user.domain.bo.ConsolidatedAccountItemBo;
import org.dromara.waterfee.user.domain.vo.ConsolidatedAccountItemVo;
import org.dromara.waterfee.user.domain.ConsolidatedAccountItem;
import org.dromara.waterfee.user.mapper.ConsolidatedAccountItemMapper;
import org.dromara.waterfee.user.service.IConsolidatedAccountItemService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.ArrayList;

/**
 * 合收户关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@RequiredArgsConstructor
@Service
public class ConsolidatedAccountItemServiceImpl implements IConsolidatedAccountItemService {

    private final ConsolidatedAccountItemMapper baseMapper;

    /**
     * 查询合收户关系
     *
     * @param consolidatedAccountItemId 主键
     * @return 合收户关系
     */
    @Override
    public ConsolidatedAccountItem queryById(Long consolidatedAccountItemId){
        return baseMapper.selectById(consolidatedAccountItemId);
    }

    /**
     * 分页查询合收户关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 合收户关系分页列表
     */
    @Override
    public TableDataInfo<ConsolidatedAccountItemVo> queryPageList(ConsolidatedAccountItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ConsolidatedAccountItem> lqw = buildQueryWrapper(bo);
        Page<ConsolidatedAccountItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的合收户关系列表
     *
     * @param bo 查询条件
     * @return 合收户关系列表
     */
    @Override
    public List<ConsolidatedAccountItemVo> queryList(ConsolidatedAccountItemBo bo) {
        LambdaQueryWrapper<ConsolidatedAccountItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ConsolidatedAccountItem> buildQueryWrapper(ConsolidatedAccountItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ConsolidatedAccountItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(ConsolidatedAccountItem::getCreateTime);
        lqw.eq(bo.getConsolidatedAccountId() != null, ConsolidatedAccountItem::getConsolidatedAccountId, bo.getConsolidatedAccountId());
        lqw.eq(bo.getUserId() != null, ConsolidatedAccountItem::getUserId, bo.getUserId());
        return lqw;
    }

    /**
     * 新增合收户关系
     *
     * @param bo 合收户关系
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ConsolidatedAccountItemBo bo) {
        ConsolidatedAccountItem add = MapstructUtils.convert(bo, ConsolidatedAccountItem.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setConsolidatedAccountItemId(add.getConsolidatedAccountItemId());
        }
        return flag;
    }
    
    /**
     * 批量新增合收户关系
     *
     * @param boList 合收户关系列表
     * @return 是否批量新增成功
     */
    @Override
    public Boolean insertByBoList(List<ConsolidatedAccountItemBo> boList) {
        if (boList == null || boList.isEmpty()) {
            return false;
        }
        
        List<ConsolidatedAccountItem> entityList = new ArrayList<>(boList.size());
        for (ConsolidatedAccountItemBo bo : boList) {
            ConsolidatedAccountItem entity = MapstructUtils.convert(bo, ConsolidatedAccountItem.class);
            validEntityBeforeSave(entity);
            entity.setCreateBy(LoginHelper.getUserId());
            entity.setCreateTime(DateUtils.getNowDate());
            entity.setCreateDept(LoginHelper.getDeptId());
            entityList.add(entity);
        }
        
        // 批量插入数据
        boolean success = baseMapper.insertBatch(entityList);
        
        // 如果插入成功，将生成的ID回填到BO对象中
        if (success) {
            for (int i = 0; i < boList.size(); i++) {
                boList.get(i).setConsolidatedAccountItemId(entityList.get(i).getConsolidatedAccountItemId());
            }
        }
        
        return success;
    }

    /**
     * 修改合收户关系
     *
     * @param bo 合收户关系
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ConsolidatedAccountItemBo bo) {
        ConsolidatedAccountItem update = MapstructUtils.convert(bo, ConsolidatedAccountItem.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ConsolidatedAccountItem entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除合收户关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据合收户ID批量删除合收户关系
     *
     * @param consolidatedAccountIds 合收户ID集合
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteByConsolidatedAccountIds(Collection<Long> consolidatedAccountIds) {
        if (consolidatedAccountIds == null || consolidatedAccountIds.isEmpty()) {
            return false;
        }
        LambdaQueryWrapper<ConsolidatedAccountItem> lqw = Wrappers.lambdaQuery();
        lqw.in(ConsolidatedAccountItem::getConsolidatedAccountId, consolidatedAccountIds);
        return baseMapper.delete(lqw) > 0;
    }
}
