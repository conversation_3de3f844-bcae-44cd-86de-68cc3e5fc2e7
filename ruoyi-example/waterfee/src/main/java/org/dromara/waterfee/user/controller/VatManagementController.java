package org.dromara.waterfee.user.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.user.domain.VatManagement;
import org.dromara.waterfee.user.domain.vo.VatManagementVo;
import org.dromara.waterfee.user.domain.bo.VatManagementBo;
import org.dromara.waterfee.user.service.IVatManagementService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 增值税管理
 * 前端访问路由地址为:/waterfee/vatManagement
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/vatManagement")
public class VatManagementController extends BaseController {

    private final IVatManagementService vatManagementService;

    /**
     * 查询增值税管理列表
     */
    @SaCheckPermission("waterfee:vatManagement:list")
    @GetMapping("/list")
    public TableDataInfo<VatManagementVo> list(VatManagementBo bo, PageQuery pageQuery) {
        return vatManagementService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出增值税管理列表
     */
    @SaCheckPermission("waterfee:vatManagement:export")
    @Log(title = "增值税管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(VatManagementBo bo, HttpServletResponse response) {
        List<VatManagementVo> list = vatManagementService.queryList(bo);
        ExcelUtil.exportExcel(list, "增值税管理", VatManagementVo.class, response);
    }

    /**
     * 获取增值税管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("waterfee:vatManagement:query")
    @GetMapping("/{id}")
    public R<VatManagement> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(vatManagementService.queryById(id));
    }

    /**
     * 新增增值税管理
     */
    @SaCheckPermission("waterfee:vatManagement:add")
    @Log(title = "增值税管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody VatManagementBo bo) {
        return toAjax(vatManagementService.insertByBo(bo));
    }

    /**
     * 修改增值税管理
     */
    @SaCheckPermission("waterfee:vatManagement:edit")
    @Log(title = "增值税管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody VatManagementBo bo) {
        return toAjax(vatManagementService.updateByBo(bo));
    }

    /**
     * 删除增值税管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("waterfee:vatManagement:remove")
    @Log(title = "增值税管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(vatManagementService.deleteWithValidByIds(List.of(ids), true));
    }
}
