package org.dromara.waterfee.meter.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 设备告警对象 waterfee_meter_alert
 *
 * <AUTHOR>
 * @date 2025-04-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_meter_alert")
public class WaterfeeMeterAlert extends TenantEntity {

    /**
     * 创建者
     */
    @TableField(exist = false)
    private Long createBy;

    /**
     * 更新者
     */
    @TableField(exist = false)
    private Long updateBy;

    /**
     * 创建部门
     */
    @TableField(exist = false)
    private Long createDept;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 告警ID
     */
    @TableId(value = "alert_id")
    private Long alertId;

    /**
     * 水表ID
     */
    private Long meterId;

    /**
     * 水表编号
     */
    private String meterNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 营业区域ID
     */
    private Long businessAreaId;

    /**
     * 告警类型
     */
    private String alertType;

    /**
     * 告警内容
     */
    private String alertContent;

    /**
     * 告警级别（1-轻微 2-一般 3-严重 4-紧急）
     */
    private String alertLevel;

    /**
     * 处理状态（0-未处理 1-处理中 2-已处理）
     */
    private String alertStatus;

    /**
     * 告警时间
     */
    private Date alertTime;

    /**
     * 处理时间
     */
    private Date processTime;

    /**
     * 处理人
     */
    private String processUser;

    /**
     * 处理结果
     */
    private String processResult;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
