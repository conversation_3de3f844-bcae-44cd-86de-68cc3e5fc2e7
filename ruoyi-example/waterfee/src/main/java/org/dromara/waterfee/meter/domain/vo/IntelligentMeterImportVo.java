package org.dromara.waterfee.meter.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 智能表导入VO
 *
 * <AUTHOR>
 */
@Data
public class IntelligentMeterImportVo {

    @ExcelProperty(value = "水表编号")
    private String meterNo;

    @ExcelProperty(value = "水表厂家")
    private String manufacturer;

    @ExcelProperty(value = "水表口径")
    private String caliber;

    @ExcelProperty(value = "水表精度")
    private String accuracy;

    @ExcelProperty(value = "安装位置")
    private String installLocation;

    @ExcelProperty(value = "初始读数")
    private String initialReading;

    @ExcelProperty(value = "业务区域")
    private String businessAreaName;

    @ExcelProperty(value = "抄表手册")
    private String meterBookName;

    @ExcelProperty(value = "通信协议")
    private String protocol;

    @ExcelProperty(value = "通信地址")
    private String address;

    @ExcelProperty(value = "IMEI号")
    private String imei;

    @ExcelProperty(value = "IMSI号")
    private String imsi;

    @ExcelProperty(value = "物联网平台")
    private String iotPlatform;

    @ExcelProperty(value = "采集频率(分钟)")
    private String collectionFrequency;

    @ExcelProperty(value = "阀控功能")
    private Integer valveControl;

    @ExcelProperty(value = "是否预付费")
    private Integer prepaid;

    @ExcelProperty(value = "备注")
    private String remark;
}
