package org.dromara.waterfee.chargeManage.mapper;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageThirdParty;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageThirdPartyVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageThirdPartyBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 第三方对账记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface WaterfeeChargeManageThirdPartyMapper extends BaseMapperPlus<WaterfeeChargeManageThirdParty, WaterfeeChargeManageThirdPartyVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeChargeManageThirdPartyVo>> P selectVoPage(IPage<WaterfeeChargeManageThirdParty> page, Wrapper<WaterfeeChargeManageThirdParty> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询第三方对账记录列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WaterfeeChargeManageThirdPartyVo> queryList(@Param("page") Page<WaterfeeChargeManageThirdParty> page, @Param("query") WaterfeeChargeManageThirdPartyBo query);

}
