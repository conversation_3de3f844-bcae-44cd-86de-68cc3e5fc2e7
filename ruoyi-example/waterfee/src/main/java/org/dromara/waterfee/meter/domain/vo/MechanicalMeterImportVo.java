package org.dromara.waterfee.meter.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 机械表导入VO
 *
 * <AUTHOR>
 */
@Data
public class MechanicalMeterImportVo {

    @ExcelProperty(value = "水表编号")
    private String meterNo;

    @ExcelProperty(value = "水表厂家")
    private String manufacturer;

    @ExcelProperty(value = "水表口径")
    private String caliber;

    @ExcelProperty(value = "水表精度")
    private String accuracy;

    @ExcelProperty(value = "安装位置")
    private String installLocation;

    @ExcelProperty(value = "初始读数")
    private String initialReading;

    @ExcelProperty(value = "业务区域")
    private String businessAreaName;

    @ExcelProperty(value = "抄表手册")
    private String meterBookName;

    @ExcelProperty(value = "备注")
    private String remark;
}
