package org.dromara.waterfee.user.domain.bo;

import org.dromara.waterfee.user.domain.WaterfeeUserCancellationRecord;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用水用户销户记录业务对象 waterfee_user_cancellation_record
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeUserCancellationRecord.class, reverseConvertGenerate = false)
public class WaterfeeUserCancellationRecordBo extends BaseEntity {

    /**
     * 主键
     */
    private Long cancellationId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 销户原因
     */
    private String cancellationReason;

    /**
     * 销户时间
     */
    private Date cancellationTime;


}
