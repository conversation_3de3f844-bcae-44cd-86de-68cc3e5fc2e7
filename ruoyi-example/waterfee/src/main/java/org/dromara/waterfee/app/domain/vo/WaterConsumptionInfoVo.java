package org.dromara.waterfee.app.domain.vo;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用水信息视图对象
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
public class WaterConsumptionInfoVo implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 本月用水量
   */
  private BigDecimal currentMonthConsumption;

  /**
   * 上月用水量
   */
  private BigDecimal lastMonthConsumption;

  /**
   * 账单月份
   */
  private String billMonth;
}