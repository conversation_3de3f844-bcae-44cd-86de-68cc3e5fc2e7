package org.dromara.waterfee.complaint.domain.bo;

import org.dromara.waterfee.complaint.domain.ComplaintSuggestion;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 投诉建议业务对象 complaint_suggestion
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ComplaintSuggestion.class, reverseConvertGenerate = false)
public class ComplaintSuggestionBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long complaintSuggestionId;

    /**
     * 提交人姓名
     */
    @NotBlank(message = "提交人姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String submitterName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 投诉内容
     */
    private String content;

    /**
     * 投诉时间
     */
    private Date submitTime;

    /**
     * 备注
     */
    private String remark;


}
