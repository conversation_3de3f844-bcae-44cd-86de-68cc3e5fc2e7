package org.dromara.waterfee.meterBook.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;

import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.meterBook.domain.bo.MeterBookBo;
import org.dromara.waterfee.meterBook.domain.vo.MeterBookVo;
import org.dromara.waterfee.meterBook.service.IMeterBookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 抄表手册管理
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Validated
@RestController
@RequestMapping("/meterbook")
public class MeterBookController extends BaseController {

    @Autowired
    private IMeterBookService meterBookService;

    /**
     * 查询抄表手册列表
     */
    @GetMapping("/list")
    public TableDataInfo<MeterBookVo> list(MeterBookBo bo, PageQuery pageQuery) {
        return meterBookService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出抄表手册列表
     */
    @Log(title = "抄表手册", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MeterBookBo bo, HttpServletResponse response) {
        List<MeterBookVo> list = meterBookService.queryList(bo);
        ExcelUtil.exportExcel(list, "抄表手册", MeterBookVo.class, response);
    }

    /**
     * 获取抄表手册详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<MeterBookVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(meterBookService.queryById(id));
    }

    /**
     * 新增抄表手册
     */
    @Log(title = "抄表手册", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MeterBookBo bo) {
        return toAjax(meterBookService.insertByBo(bo));
    }

    /**
     * 修改抄表手册
     */
    @Log(title = "抄表手册", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MeterBookBo bo) {
        return toAjax(meterBookService.updateByBo(bo));
    }

    /**
     * 删除抄表手册
     *
     * @param ids 主键串
     */
    @Log(title = "抄表手册", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(meterBookService.deleteWithValidByIds(List.of(ids), true));
    }
}
