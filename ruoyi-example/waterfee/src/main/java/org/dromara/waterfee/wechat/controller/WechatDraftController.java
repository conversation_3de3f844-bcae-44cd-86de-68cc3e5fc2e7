package org.dromara.waterfee.wechat.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.wechat.domain.bo.WechatDraftBo;
import org.dromara.waterfee.wechat.domain.vo.WechatDraftVo;
import org.dromara.waterfee.wechat.enums.DraftTypeEnum;
import org.dromara.waterfee.wechat.service.IWechatDraftService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 微信草稿控制器
 * 前端访问路由地址为:/waterfee/wechatDraft
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/wechatDraft")
public class WechatDraftController extends BaseController {

    private final IWechatDraftService wechatDraftService;

    /**
     * 查询微信草稿列表
     */
    @SaCheckPermission("waterfee:wechatDraft:list")
    @GetMapping("/list")
    public TableDataInfo<WechatDraftVo> list(WechatDraftBo bo, PageQuery pageQuery) {
        return wechatDraftService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出微信草稿列表
     */
    @SaCheckPermission("waterfee:wechatDraft:export")
    @Log(title = "微信草稿", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WechatDraftBo bo, HttpServletResponse response) {
        List<WechatDraftVo> list = wechatDraftService.queryList(bo);
        ExcelUtil.exportExcel(list, "微信草稿", WechatDraftVo.class, response);
    }

    /**
     * 获取微信草稿详细信息
     *
     * @param draftId 主键
     */
    @SaCheckPermission("waterfee:wechatDraft:query")
    @GetMapping("/{draftId}")
    public R<WechatDraftVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable("draftId") Long draftId) {
        return R.ok(wechatDraftService.queryById(draftId));
    }

    /**
     * 新增微信草稿
     */
    @SaCheckPermission("waterfee:wechatDraft:add")
    @Log(title = "微信草稿", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WechatDraftBo bo) {
        return toAjax(wechatDraftService.insertByBo(bo));
    }

    /**
     * 新增微信草稿并同时发布到微信
     */
    @SaCheckPermission("waterfee:wechatDraft:addAndPublish")
    @Log(title = "新增并发布微信草稿", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/addAndPublish")
    public R<Void> addAndPublish(@Validated(AddGroup.class) @RequestBody WechatDraftBo bo) {
        return toAjax(wechatDraftService.insertAndPublishByBo(bo));
    }

    /**
     * 修改微信草稿
     */
    @SaCheckPermission("waterfee:wechatDraft:edit")
    @Log(title = "微信草稿", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WechatDraftBo bo) {
        return toAjax(wechatDraftService.updateByBo(bo));
    }

    /**
     * 修改微信草稿并同步更新微信端草稿
     */
    @SaCheckPermission("waterfee:wechatDraft:editAndSync")
    @Log(title = "同步更新微信草稿", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/sync")
    public R<Void> editAndSync(@Validated(EditGroup.class) @RequestBody WechatDraftBo bo) {
        return toAjax(wechatDraftService.updateAndSyncByBo(bo));
    }

    /**
     * 删除微信草稿
     *
     * @param draftIds 主键串
     */
    @SaCheckPermission("waterfee:wechatDraft:remove")
    @Log(title = "微信草稿", businessType = BusinessType.DELETE)
    @DeleteMapping("/{draftIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] draftIds) {
        return toAjax(wechatDraftService.deleteWithValidByIds(Arrays.asList(draftIds)));
    }

    /**
     * 发布草稿到微信公众号（创建草稿）
     *
     * @param draftId 草稿ID
     */
    @SaCheckPermission("waterfee:wechatDraft:publish")
    @Log(title = "发布微信草稿", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/publish/{draftId}")
    public R<Void> publishDraft(@NotNull(message = "草稿ID不能为空")
                                @PathVariable("draftId") Long draftId) {
        return toAjax(wechatDraftService.publishDraft(draftId));
    }

    /**
     * 正式发布草稿为订阅号消息推文
     *
     * @param draftId 草稿ID
     */
    @SaCheckPermission("waterfee:wechatDraft:publishArticle")
    @Log(title = "发布订阅号消息推文", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/publishArticle/{draftId}")
    public R<Void> publishArticle(@NotNull(message = "草稿ID不能为空")
                                  @PathVariable("draftId") Long draftId) {
        return toAjax(wechatDraftService.publishArticle(draftId));
    }

    /**
     * 创建微信草稿
     *
     * @param bo 微信草稿信息
     */
    @SaCheckPermission("waterfee:wechatDraft:create")
    @Log(title = "创建微信草稿", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<String> createWechatDraft(@Validated(AddGroup.class) @RequestBody WechatDraftBo bo) {
        String mediaId = wechatDraftService.createWechatDraft(bo);
        return R.ok("创建成功", mediaId);
    }

    /**
     * 删除微信草稿
     *
     * @param mediaId 微信草稿媒体ID
     */
    @SaCheckPermission("waterfee:wechatDraft:deleteWechat")
    @Log(title = "删除微信草稿", businessType = BusinessType.DELETE)
    @DeleteMapping("/wechat/{mediaId}")
    public R<Void> deleteWechatDraft(@NotEmpty(message = "媒体ID不能为空")
                                     @PathVariable("mediaId") String mediaId) {
        return toAjax(wechatDraftService.deleteWechatDraft(mediaId));
    }

    /**
     * 获取微信草稿列表
     *
     * @param offset 偏移量
     * @param count  数量
     */
    @SaCheckPermission("waterfee:wechatDraft:wechatList")
    @GetMapping("/wechat/list")
    public R<String> getWechatDraftList(@RequestParam(defaultValue = "0") Integer offset,
                                        @RequestParam(defaultValue = "20") Integer count) {
        String result = wechatDraftService.getWechatDraftList(offset, count);
        return R.ok(result);
    }

    /**
     * 删除发布的文章（同时删除本地草稿和微信端文章）
     *
     * @param draftId 草稿ID
     */
    @SaCheckPermission("waterfee:wechatDraft:deletePublished")
    @Log(title = "删除发布文章", businessType = BusinessType.DELETE)
    @DeleteMapping("/published/{draftId}")
    public R<Void> deletePublishedArticle(@NotNull(message = "草稿ID不能为空")
                                          @PathVariable("draftId") Long draftId) {
        return toAjax(wechatDraftService.deletePublishedArticle(draftId));
    }

    /**
     * 获取草稿类型列表
     */
    @GetMapping("/types")
    public R<List<Map<String, String>>> getDraftTypes() {
        return R.ok(DraftTypeEnum.getAllTypes());
    }
}
