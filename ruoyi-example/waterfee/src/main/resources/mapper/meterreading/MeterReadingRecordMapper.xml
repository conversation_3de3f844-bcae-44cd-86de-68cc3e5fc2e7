<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.meterReading.mapper.MeterReadingRecordMapper">

    <resultMap type="org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord" id="MeterReadingRecordResult">
        <result property="recordId" column="record_id"/>
        <result property="meterNo" column="meter_no"/>
        <result property="meterType" column="meter_type"/>
        <result property="lastReading" column="last_reading"/>
        <result property="lastReadingTime" column="last_reading_time"/>
        <result property="currentReading" column="current_reading"/>
        <result property="oldMeterStopReading" column="old_meter_stop_reading"/>
        <result property="waterUsage" column="water_usage"/>
        <result property="readingTime" column="reading_time"/>
        <result property="taskId" column="task_id"/>
        <result property="sourceType" column="source_type"/>
        <result property="isAudited" column="is_audited"/>
        <result property="manualId" column="manual_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="meterBookId" column="meter_book_id"/>
        <result property="isPending" column="is_pending"/>
        <result property="pendingReason" column="pending_reason"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <resultMap type="org.dromara.waterfee.meterReading.domain.vo.MeterReadingRecordVo" id="MeterReadingRecordVoResult">
        <result property="recordId" column="record_id"/>
        <result property="meterNo" column="meter_no"/>
        <result property="meterType" column="meter_type"/>
        <result property="lastReading" column="last_reading"/>
        <result property="lastReadingTime" column="last_reading_time"/>
        <result property="currentReading" column="current_reading"/>
        <result property="oldMeterStopReading" column="old_meter_stop_reading"/>
        <result property="waterUsage" column="water_usage"/>
        <result property="readingTime" column="reading_time"/>
        <result property="taskId" column="task_id"/>
        <result property="sourceType" column="source_type"/>
        <result property="isAudited" column="is_audited"/>
        <result property="manualId" column="manual_id"/>
        <result property="createTime" column="create_time"/>
        <!-- 水表相关信息 -->
        <result property="meterId" column="meter_id"/>
        <result property="meterAddress" column="meter_address"/>
        <result property="userName" column="user_name"/>
        <result property="userAddress" column="user_address"/>
        <result property="userPhone" column="user_phone"/>
        <result property="isPending" column="is_pending"/>
        <result property="pendingReason" column="pending_reason"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 根据水表编号查询最新一次的抄表记录 -->
    <select id="selectLatestByMeterNo" resultMap="MeterReadingRecordVoResult">
        SELECT
        record_id, meter_no, meter_type, last_reading, last_reading_time,
        current_reading, old_meter_stop_reading, water_usage, reading_time,
        task_id, source_type, is_audited, manual_id, create_time,
        is_pending, pending_reason, remark
        FROM
        waterfee_meter_reading_record
        WHERE
        meter_no = #{meterNo}
        AND del_flag = '0'
        ORDER BY
        reading_time DESC, record_id DESC
        LIMIT 1
    </select>

    <!-- 根据水表编号查询最新一次的抄表记录（实体） -->
    <select id="selectLatestEntityByMeterNo" resultMap="MeterReadingRecordResult">
        SELECT
        record_id, meter_no,meter_type, last_reading, last_reading_time,
        current_reading, old_meter_stop_reading, water_usage, reading_time,
        task_id, source_type, is_audited, manual_id, tenant_id, create_by, create_time,
        update_by, update_time,del_flag, meter_book_id, is_pending, pending_reason, remark
        FROM
        waterfee_meter_reading_record
        WHERE
        meter_no = #{meterNo}
        AND del_flag = '0'
        ORDER BY
        reading_time DESC, record_id DESC
        LIMIT 1
    </select>

    <!-- 分页查询抄表记录列表 -->
    <select id="selectVoPage" resultMap="MeterReadingRecordVoResult">
        SELECT
        r.record_id,
        r.meter_no,
        r.meter_type,
        r.last_reading,
        r.last_reading_time,
        r.current_reading,
        r.old_meter_stop_reading,
        r.water_usage,
        r.reading_time,
        r.task_id,
        r.source_type,
        r.is_audited,
        r.manual_id,
        r.create_time,
        r.is_pending,
        r.pending_reason,
        r.remark
        FROM
        waterfee_meter_reading_record r
        ${ew.customSqlSegment}
    </select>

    <!-- 根据水表编号查询所有已审核的抄表记录 -->
    <select id="selectAllByMeterNo" resultMap="MeterReadingRecordVoResult">
        SELECT
        record_id, meter_no, meter_type, last_reading, last_reading_time,
        current_reading, old_meter_stop_reading, water_usage, reading_time,
        task_id, source_type, is_audited, manual_id, create_time,
        is_pending, pending_reason, remark
        FROM
        waterfee_meter_reading_record
        WHERE
        meter_no = #{meterNo}
        AND is_audited = '1'
        AND del_flag = '0'
        ORDER BY
        reading_time DESC, record_id DESC
    </select>

    <!-- 根据表册ID和任务ID查询抄表记录 -->
    <select id="selectByBookIdAndTaskId" resultMap="MeterReadingRecordResult">
        SELECT
        r.record_id, r.meter_no, r.meter_type, r.last_reading, r.last_reading_time,
        r.current_reading, r.old_meter_stop_reading, r.water_usage, r.reading_time,
        r.task_id, r.source_type, r.is_audited, r.manual_id, r.tenant_id, r.create_by, r.create_time,
        r.update_by, r.update_time, r.del_flag, r.is_pending, r.pending_reason, r.remark
        FROM
        waterfee_meter_reading_record r
        JOIN
        waterfee_meter m ON r.meter_no = m.meter_no
        WHERE
        m.meter_book_id = #{meterBookId}
        AND r.task_id = #{taskId}
        AND r.del_flag = '0'
    </select>

    <!-- 根据表册ID查询机械表抄表记录 -->
    <select id="selectMechanicalMetersByBookId" resultMap="MeterReadingRecordVoResult">
        SELECT
        r.record_id, r.meter_no, r.meter_type, r.last_reading, r.last_reading_time,
        r.current_reading, r.old_meter_stop_reading, r.water_usage, r.reading_time,
        r.task_id, r.source_type, r.is_audited, r.manual_id, r.create_time,
        r.is_pending, r.pending_reason, r.remark,
        m.meter_id, m.meter_address, m.user_name, m.user_address, m.user_phone
        FROM
        waterfee_meter_reading_record r
        JOIN
        waterfee_meter m ON r.meter_no = m.meter_no
        WHERE
        m.meter_book_id = #{meterBookId}
        AND m.meter_type = 1 -- 机械表类型为1
        AND r.del_flag = '0'
        ORDER BY
        r.reading_time DESC, r.record_id DESC
    </select>

    <!-- 根据表册ID查询智能表抄表记录 -->
    <select id="selectIntelligentMetersByBookId" resultMap="MeterReadingRecordVoResult">
        SELECT
        r.record_id, r.meter_no, r.meter_type, r.last_reading, r.last_reading_time,
        r.current_reading, r.old_meter_stop_reading, r.water_usage, r.reading_time,
        r.task_id, r.source_type, r.is_audited, r.manual_id, r.create_time,
        r.is_pending, r.pending_reason, r.remark,
        m.meter_id, m.meter_address, m.user_name, m.user_address, m.user_phone
        FROM
        waterfee_meter_reading_record r
        JOIN
        waterfee_meter m ON r.meter_no = m.meter_no
        WHERE
        m.meter_book_id = #{meterBookId}
        AND m.meter_type = 2 -- 智能表类型为2
        AND r.del_flag = '0'
        ORDER BY
        r.reading_time DESC, r.record_id DESC
    </select>
</mapper>
