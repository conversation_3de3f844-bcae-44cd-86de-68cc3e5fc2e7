<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.dromara.waterfee.meterRelation.mapper.WaterfeeMeterBalanceRecordMapper">

    <insert id="insert" parameterType="org.dromara.waterfee.meterRelation.domain.WaterfeeMeterBalanceRecord">
        INSERT INTO waterfee_meter_balance_record (
        parent_meter_id, parent_meter_no, reading_time,
        parent_usage, child_usage, leak_rate,
        is_abnormal, abnormal_reason, tenant_id, create_time, del_flag
        ) VALUES (
        #{parentMeterId}, #{parentMeterNo}, #{readingTime},
        #{parentUsage}, #{childUsage}, #{leakRate},
        #{isAbnormal}, #{abnormalReason}, #{tenantId}, now(), '0'
        )
    </insert>

    <select id="selectByParentAndTime"
            resultType="org.dromara.waterfee.meterRelation.domain.WaterfeeMeterBalanceRecord">
        SELECT * FROM waterfee_meter_balance_record
        WHERE del_flag = '0'
        AND parent_meter_id = #{parentMeterId}
        AND reading_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY reading_time DESC
    </select>

    <delete id="deleteByParentMeterId">
        UPDATE waterfee_meter_balance_record
        SET del_flag = '2'
        WHERE parent_meter_id = #{parentMeterId}
    </delete>

</mapper>
