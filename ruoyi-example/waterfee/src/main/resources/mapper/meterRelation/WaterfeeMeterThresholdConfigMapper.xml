<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.meterRelation.mapper.WaterfeeMeterThresholdConfigMapper">

    <!-- 根据水表ID查询阈值配置 -->
    <select id="selectConfigList" resultType="org.dromara.waterfee.meterRelation.domain.vo.MeterThresholdConfigVO">
        SELECT
        cfg.parent_meter_id,
        pm.meter_no AS parent_meter_no,
        cfg.child_meter_id,
        cm.meter_no AS child_meter_no,
        cfg.threshold
        FROM waterfee_meter_threshold_config cfg
        LEFT JOIN waterfee_meter pm ON cfg.parent_meter_id = pm.meter_id
        LEFT JOIN waterfee_meter cm ON cfg.child_meter_id = cm.meter_id
        WHERE cfg.del_flag = '0'
    </select>

    <select id="selectByParentAndChild"
            resultType="org.dromara.waterfee.meterRelation.domain.WaterfeeMeterThresholdConfig">
        SELECT *
        FROM waterfee_meter_threshold_config
        WHERE parent_meter_id = #{parentId}
        AND del_flag = '0'
        LIMIT 1
    </select>

    <select id="selectByParentId" resultType="org.dromara.waterfee.meterRelation.domain.WaterfeeMeterThresholdConfig">
        SELECT *
        FROM waterfee_meter_threshold_config
        WHERE parent_meter_id = #{parentId}
        AND child_meter_id IS NULL
        AND del_flag = '0'
        LIMIT 1
    </select>

</mapper>
