<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManageNonOperatingIncomeMapper">
    <resultMap id="WaterfeeChargeManageNonOperatingIncomeResult" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageNonOperatingIncome">
        <id property="id" column="id"/>
    </resultMap>

    <resultMap id="WaterfeeChargeManageNonOperatingIncomeResultVo" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageNonOperatingIncomeVo">
        <id property="id" column="id"/>
    </resultMap>

    <sql id="selectWaterfeeChargeManageNonOperatingIncomeVo">
        select wcmnoi.id, wcmnoi.user_id, wcmnoi.income_type, wcmnoi.amount, wcmnoi.income_time, wcmnoi.remark, wcmnoi.tenant_id, wcmnoi.create_by, wcmnoi.create_time, wcmnoi.update_by, wcmnoi.update_time, wcmnoi.del_flag from waterfee_charge_manage_non_operating_income wcmnoi
    </sql>
    <select id="queryList" resultMap="WaterfeeChargeManageNonOperatingIncomeResultVo">
        <include refid="selectWaterfeeChargeManageNonOperatingIncomeVo"/>
        <where>
            <if test="query.userId != null"> and wcmnoi.user_id = #{query.userId}</if>
            <if test="query.incomeType != null and query.incomeType != ''"> and wcmnoi.income_type = #{query.incomeType}</if>
            <if test="query.amount != null"> and wcmnoi.amount = #{query.amount}</if>
            <if test="query.incomeTime != null"> and wcmnoi.income_time = #{query.incomeTime}</if>
            and wcmnoi.del_flag = '0'
        </where>
    </select>
</mapper>
