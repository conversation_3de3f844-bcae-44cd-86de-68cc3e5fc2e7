<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManageArAdjustmentMapper">
    <resultMap id="WaterfeeChargeManageArAdjustmentResult" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageArAdjustment">
        <id property="id" column="id"/>
    </resultMap>

    <resultMap id="WaterfeeChargeManageArAdjustmentResultVo" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageArAdjustmentVo">
        <id property="id" column="id"/>
    </resultMap>

    <sql id="selectWaterfeeChargeManageArAdjustmentVo">
        select wcmaa.id, wcmaa.user_id, wcmaa.meter_no, wcmaa.bill_period, wcmaa.original_amount, wcmaa.adjusted_amount, wcmaa.reason, wcmaa.remark, wcmaa.tenant_id, wcmaa.create_by, wcmaa.create_time, wcmaa.update_by, wcmaa.update_time, wcmaa.del_flag from waterfee_charge_manage_ar_adjustment wcmaa
    </sql>
    <select id="queryList" resultMap="WaterfeeChargeManageArAdjustmentResultVo">
        <include refid="selectWaterfeeChargeManageArAdjustmentVo"/>
        <where>
            <if test="query.userId != null"> and wcmaa.user_id = #{query.userId}</if>
            <if test="query.meterNo != null and query.meterNo != ''"> and wcmaa.meter_no = #{query.meterNo}</if>
            <if test="query.billPeriod != null and query.billPeriod != ''"> and wcmaa.bill_period = #{query.billPeriod}</if>
            <if test="query.originalAmount != null"> and wcmaa.original_amount = #{query.originalAmount}</if>
            <if test="query.adjustedAmount != null"> and wcmaa.adjusted_amount = #{query.adjustedAmount}</if>
            <if test="query.reason != null and query.reason != ''"> and wcmaa.reason = #{query.reason}</if>
            and wcmaa.del_flag = '0'
        </where>
    </select>
</mapper>
